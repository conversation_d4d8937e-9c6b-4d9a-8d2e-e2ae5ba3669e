-- 文本框渲染测试脚本
-- 验证文本框是否能完整显示所有章节内容

-- 导入必要的库
local r = reaper

-- 获取脚本路径
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"

-- 安全加载函数
local function safe_load_module(file_path)
  local success, result = pcall(dofile, file_path)
  if not success then
    r.ShowConsoleMsg("模块加载失败: " .. file_path .. " - " .. tostring(result) .. "\n")
    return nil
  end
  return result
end

-- 主函数
local function main()
  r.ShowConsoleMsg("=== 文本框渲染测试 ===\n")
  
  -- 加载所有模块
  local utils_module = safe_load_module(script_dir .. "utils_module.lua")
  local style_module = safe_load_module(script_dir .. "style_module.lua")
  local text_utils = safe_load_module(script_dir .. "text_utils.lua")
  local button_module = safe_load_module(script_dir .. "button_module.lua")
  local ui_module = safe_load_module(script_dir .. "ui_module.lua")
  local event_module = safe_load_module(script_dir .. "event_module.lua")
  
  if not utils_module or not style_module or not text_utils or not button_module or not ui_module or not event_module then
    r.ShowConsoleMsg("✗ 模块加载失败\n")
    return
  end
  r.ShowConsoleMsg("✓ 所有模块加载成功\n")
  
  -- 初始化模块
  style_module.init({utils_module = utils_module})
  
  local deps = {
    style_module = style_module,
    text_utils = text_utils,
    button_module = button_module,
    utils_module = utils_module,
    gfx = gfx,
    r = r
  }
  
  ui_module.init(deps)
  deps.ui_module = ui_module
  event_module.init(deps)
  
  if button_module and button_module.init then
    button_module.init({
      utils_module = utils_module,
      text_utils = text_utils,
      style_module = style_module
    })
  end
  
  r.ShowConsoleMsg("✓ 所有模块初始化成功\n")
  
  -- 创建应用状态
  local app_state = utils_module.app_state.create()
  r.ShowConsoleMsg("✓ 应用状态创建成功\n")
  
  -- 设置完整的测试内容（包含多个章节）
  app_state.clipboard_text = [[
[CHAPTER]第251章 孕妇打人了！（一）[/CHAPTER]
　　张文丽觉得不可能，但这报纸上的故事，笔名是鱼，口吻也是以女方的视角写的，让人不多想都不行！
　　她拿着报纸，心情复杂地看着上面的内容。
【虞梨-黎暖薇】：这真的是太巧了。
　　黎暖薇轻声说道，眼中闪过一丝疑惑。

[CHAPTER]第252章 真相大白（二）[/CHAPTER]
　　第二天一早，张文丽就去找了报社的编辑。
　　她需要确认这个故事的来源。
【张三-角色A】：我们需要调查一下这件事。
　　角色A认真地说道。
　　这件事情变得越来越复杂了。

[CHAPTER]第253章 意外发现（三）[/CHAPTER]
　　在调查的过程中，他们发现了更多的线索。
　　原来这个故事背后还有更深的秘密。
【李四-角色B】：我觉得事情没有这么简单。
　　角色B皱着眉头说道。
　　所有的证据都指向同一个方向。

[CHAPTER]第254章 最终真相（四）[/CHAPTER]
　　经过一番调查，真相终于浮出水面。
　　原来这一切都是有人精心策划的。
【王五-角色C】：现在一切都清楚了。
　　角色C松了一口气。
　　这个案子终于可以结案了。

第255章 后续发展
　　案子虽然结束了，但是后续的影响还在继续。
　　所有人都从这件事中学到了很多。
角色D：CV演员甲
　　这是一个简单格式的角色对话。
]]
  
  r.ShowConsoleMsg("设置完整测试内容成功\n")
  
  -- 解析内容
  r.ShowConsoleMsg("开始解析内容...\n")
  
  -- 句子解析
  event_module.parse_sentences(app_state)
  r.ShowConsoleMsg("✓ 句子解析完成，共 " .. #app_state.sentences .. " 个句子\n")
  
  -- CV角色对提取
  event_module.extract_cv_role_pairs(app_state)
  r.ShowConsoleMsg("✓ CV角色对提取完成，共 " .. #app_state.cv_role_pairs .. " 个角色对\n")
  
  -- 章节提取
  event_module.extract_chapters(app_state)
  r.ShowConsoleMsg("✓ 章节提取完成，共 " .. #app_state.chapters .. " 个章节\n")
  
  -- 显示解析结果详情
  r.ShowConsoleMsg("\n=== 解析结果详情 ===\n")
  
  r.ShowConsoleMsg("所有句子列表:\n")
  for i = 1, #app_state.sentences do
    local sentence = app_state.sentences[i]
    r.ShowConsoleMsg(string.format("%d. %s\n", i, sentence))
  end
  
  r.ShowConsoleMsg("\n所有章节列表:\n")
  for i = 1, #app_state.chapters do
    local chapter = app_state.chapters[i]
    r.ShowConsoleMsg(string.format("%d. %s (句子索引: %d)\n", i, chapter.title, chapter.sentence_idx))
  end
  
  r.ShowConsoleMsg("\n所有CV角色对列表:\n")
  for i = 1, #app_state.cv_role_pairs do
    local pair = app_state.cv_role_pairs[i]
    r.ShowConsoleMsg(string.format("%d. CV: %s, 角色: %s\n", i, pair.cv, pair.role))
  end
  
  -- 验证内容完整性
  r.ShowConsoleMsg("\n=== 内容完整性验证 ===\n")
  
  local expected_chapters = 5  -- 4个[CHAPTER]格式 + 1个简单格式
  local expected_cv_pairs = 5  -- 4个【】格式 + 1个简单格式
  local expected_min_sentences = 20  -- 至少应该有20个句子
  
  local chapter_test = #app_state.chapters >= expected_chapters
  local cv_test = #app_state.cv_role_pairs >= expected_cv_pairs
  local sentence_test = #app_state.sentences >= expected_min_sentences
  
  r.ShowConsoleMsg("章节数量测试: " .. (chapter_test and "✓ 通过" or "✗ 失败") .. 
                   " (期望: >=" .. expected_chapters .. ", 实际: " .. #app_state.chapters .. ")\n")
  r.ShowConsoleMsg("CV角色对测试: " .. (cv_test and "✓ 通过" or "✗ 失败") .. 
                   " (期望: >=" .. expected_cv_pairs .. ", 实际: " .. #app_state.cv_role_pairs .. ")\n")
  r.ShowConsoleMsg("句子数量测试: " .. (sentence_test and "✓ 通过" or "✗ 失败") .. 
                   " (期望: >=" .. expected_min_sentences .. ", 实际: " .. #app_state.sentences .. ")\n")
  
  -- 检查是否包含所有章节内容
  local content_check = {
    ["第251章"] = false,
    ["第252章"] = false,
    ["第253章"] = false,
    ["第254章"] = false,
    ["第255章"] = false
  }
  
  for _, sentence in ipairs(app_state.sentences) do
    for chapter_key, _ in pairs(content_check) do
      if sentence:find(chapter_key) then
        content_check[chapter_key] = true
      end
    end
  end
  
  r.ShowConsoleMsg("\n章节内容检查:\n")
  local all_chapters_found = true
  for chapter_key, found in pairs(content_check) do
    r.ShowConsoleMsg(chapter_key .. ": " .. (found and "✓ 找到" or "✗ 缺失") .. "\n")
    if not found then
      all_chapters_found = false
    end
  end
  
  -- 测试UI渲染（如果可能）
  r.ShowConsoleMsg("\n=== UI渲染测试 ===\n")
  
  local window_success = ui_module.init_window()
  if window_success then
    r.ShowConsoleMsg("✓ 窗口初始化成功\n")
    
    -- 设置章节列表可见
    app_state.is_chapter_list_visible = true
    
    -- 尝试渲染
    local render_success, render_error = pcall(function()
      ui_module.render(app_state)
    end)
    
    if render_success then
      r.ShowConsoleMsg("✓ UI渲染成功\n")
      r.ShowConsoleMsg("✓ 文本框应该显示所有 " .. #app_state.sentences .. " 个句子\n")
      r.ShowConsoleMsg("✓ 章节列表应该显示所有 " .. #app_state.chapters .. " 个章节\n")
      
      -- 启动简单的主循环来显示界面
      local function loop()
        local char = gfx.getchar()
        if char == -1 then
          r.ShowConsoleMsg("窗口已关闭\n")
          return
        end
        
        -- 处理事件和渲染
        local event_success, event_error = pcall(function()
          event_module.handle_events(app_state)
          ui_module.render(app_state)
        end)
        
        if not event_success then
          r.ShowConsoleMsg("事件处理错误: " .. tostring(event_error) .. "\n")
        end
        
        r.defer(loop)
      end
      
      r.ShowConsoleMsg("\n启动界面显示...\n")
      r.ShowConsoleMsg("现在应该可以看到：\n")
      r.ShowConsoleMsg("✓ 文本框显示所有章节的完整内容\n")
      r.ShowConsoleMsg("✓ 可以滚动查看所有句子\n")
      r.ShowConsoleMsg("✓ 章节列表显示所有章节\n")
      r.ShowConsoleMsg("✓ 可以点击章节跳转到对应位置\n")
      r.defer(loop)
      
    else
      r.ShowConsoleMsg("✗ UI渲染失败: " .. tostring(render_error) .. "\n")
    end
  else
    r.ShowConsoleMsg("! 窗口初始化跳过（可能是环境限制）\n")
  end
  
  -- 最终结果
  r.ShowConsoleMsg("\n=== 测试结果总结 ===\n")
  
  if chapter_test and cv_test and sentence_test and all_chapters_found then
    r.ShowConsoleMsg("🎉 所有测试通过！\n")
    r.ShowConsoleMsg("✅ 文档解析完整 - 所有章节内容都被正确解析\n")
    r.ShowConsoleMsg("✅ 句子分割正确 - 所有句子都被正确分割\n")
    r.ShowConsoleMsg("✅ 章节提取正确 - 所有章节都被正确提取\n")
    r.ShowConsoleMsg("✅ CV角色对提取正确 - 所有角色对都被正确提取\n")
    r.ShowConsoleMsg("✅ 文本框应该能显示完整内容\n")
    
    if window_success then
      r.ShowConsoleMsg("\n如果文本框仍然无法完整显示，可能的原因：\n")
      r.ShowConsoleMsg("1. 滚动位置问题 - 尝试滚动查看更多内容\n")
      r.ShowConsoleMsg("2. 字体大小问题 - 尝试调整字体大小\n")
      r.ShowConsoleMsg("3. 窗口大小问题 - 尝试调整窗口大小\n")
      r.ShowConsoleMsg("4. 渲染区域问题 - 检查内容区域的大小设置\n")
    end
  else
    r.ShowConsoleMsg("⚠️ 部分测试失败，可能影响文本框显示\n")
    r.ShowConsoleMsg("请检查解析逻辑是否正确\n")
  end
  
  r.ShowConsoleMsg("\n=== 文本框渲染测试完成 ===\n")
end

-- 运行测试
main()
