-- UI模块 - 负责所有界面渲染逻辑
-- 从mark.lua中提取的UI相关功能

local ui_module = {}

-- 依赖的模块（通过init函数注入）
local style_module, text_utils, button_module, utils_module, gfx, r

-- UI状态变量
local ui = {}
local window_w, window_h = 1200, 800
local font_size, content_font_size, font_name
local last_redraw_time = 0
local redraw_cooldown = 0.016  -- 约60FPS
local force_redraw = false
local partial_render = false
local full_redraw_counter = 0

-- 性能优化变量
local cached_sentence_heights = {}
local last_content_font_size = 0
local use_smooth_scroll = true
local cached_total_content_height = nil
local cached_text_wrapping = {}

-- 搜索高亮颜色
local search_highlight_color = {r = 1.0, g = 1.0, b = 0.0, a = 0.5}

-- 初始化UI模块
function ui_module.init(deps)
  -- 注入依赖
  style_module = deps.style_module
  text_utils = deps.text_utils
  button_module = deps.button_module
  utils_module = deps.utils_module
  gfx = deps.gfx
  r = deps.r

  -- 获取样式配置
  font_size = style_module.font_size
  content_font_size = style_module.content_font_size
  font_name = style_module.font_name

  return ui_module
end

-- 初始化gfx窗口
function ui_module.init_window()
  local status, err = pcall(function()
    -- 获取REAPER主窗口的位置，计算窗口居中位置
    local center_x, center_y = 0, 0
    local main_hwnd = r.GetMainHwnd()

    if main_hwnd and r.JS_Window_GetRect then
      local retval, left, top, right, bottom = r.JS_Window_GetRect(main_hwnd)
      if retval then
        local w = right - left
        local h = bottom - top
        center_x = left + math.floor((w - window_w) / 2)
        center_y = top + math.floor((h - window_h) / 2)
      end
    end

    -- 初始化窗口
    gfx.init("审听助手 v1.0 by 流浪物语", window_w, window_h, 0, center_x, center_y)
    gfx.dock(1026)  -- 停靠在附加泊坞窗右侧
    gfx.setfont(1, font_name, font_size)
    gfx.clear = 3355443  -- 深灰背景色

    -- 初始化UI元素
    ui = style_module.init_ui_elements()

    return true
  end)

  if not status then
    utils_module.error_handler:add("窗口初始化失败: " .. tostring(err), "error", "ui_module.init_window")
    return false
  end

  return true
end

-- 主渲染函数
function ui_module.render(app_state)
  local status, err = pcall(function()
    local current_time = os.clock()

    -- 检查是否需要完整重绘
    if app_state.force_redraw or (current_time - last_redraw_time) > redraw_cooldown then
      full_redraw_counter = full_redraw_counter + 1
      last_redraw_time = current_time
      app_state.force_redraw = false
      partial_render = false
    end

    -- 更新窗口大小
    if gfx.w ~= window_w or gfx.h ~= window_h then
      window_w, window_h = gfx.w, gfx.h
      ui = style_module.init_ui_elements(window_w, window_h)
      app_state.force_redraw = true
    end

    -- 绘制主界面
    ui_module.draw_main_ui(app_state)

    -- 更新显示
    gfx.update()
  end)

  if not status then
    utils_module.error_handler:add("渲染失败: " .. tostring(err), "error", "ui_module.render")
    r.ShowConsoleMsg("Render Error: " .. tostring(err) .. "\n")
  end
end

-- 绘制主界面
function ui_module.draw_main_ui(app_state)
  -- 清除背景
  gfx.clear = style_module.colors.bg_int or 3355443

  -- 绘制内容区域
  ui_module.draw_content_area(app_state)

  -- 绘制CV角色列表
  ui_module.draw_cv_role_list(app_state)

  -- 绘制选择内容区域
  ui_module.draw_selection_content(app_state)

  -- 绘制章节列表（如果可见）
  if app_state.is_chapter_list_visible then
    ui_module.draw_chapter_list(app_state)
  end

  -- 绘制输入区域
  ui_module.draw_input_areas(app_state)

  -- 绘制按钮
  ui_module.draw_buttons(app_state)

  -- 绘制搜索UI
  ui_module.draw_search_ui(app_state)
end

-- 绘制内容区域（句子列表）
function ui_module.draw_content_area(app_state)
  if not ui.content_area then return end

  local content_area = ui.content_area

  -- 绘制背景
  gfx.set(0.1, 0.1, 0.1, 1)  -- 深灰色背景
  gfx.rect(content_area.x, content_area.y, content_area.w, content_area.h)
  style_module.draw_black_outline(content_area.x, content_area.y, content_area.w, content_area.h)

  -- 绘制句子列表
  ui_module.draw_sentences_list(app_state, content_area)

  -- 绘制滚动条
  ui_module.draw_content_scrollbar(app_state, content_area)
end

-- 绘制句子列表
function ui_module.draw_sentences_list(app_state, content_area)
  local sentences = app_state.sentences
  if #sentences == 0 then
    -- 显示提示信息
    gfx.set(0.6, 0.6, 0.6, 1)
    gfx.x, gfx.y = content_area.x + 10, content_area.y + 10
    gfx.drawstr("请从剪贴板或文件读取文本内容...")
    return
  end

  -- 使用text_utils的高级文本渲染函数
  if text_utils and text_utils.draw_sentences_list then
    -- 准备搜索高亮参数
    local search_keyword = app_state.search_text ~= "" and app_state.search_text or nil
    local highlight_color = search_highlight_color

    -- 调用text_utils的高级渲染函数
    text_utils.draw_sentences_list(
      sentences,
      content_area,
      app_state.sentence_scroll_pos,
      app_state.hover_sentence_idx,
      app_state.selected_indices,
      content_font_size,
      font_name,
      app_state.cached_sentence_heights or {},
      app_state.cached_total_content_height,
      style_module,
      gfx,
      search_keyword,
      highlight_color
    )
  else
    -- 降级到简单渲染（如果text_utils不可用）
    ui_module.draw_sentences_list_simple(app_state, content_area)
  end
end

-- 简单的句子列表渲染（降级方案）
function ui_module.draw_sentences_list_simple(app_state, content_area)
  local sentences = app_state.sentences

  -- 计算可见区域
  local line_height = content_font_size + 5
  local visible_area_height = content_area.h - 10
  local visible_items = math.floor(visible_area_height / line_height)

  -- 计算滚动范围
  local max_scroll = math.max(0, #sentences - visible_items)
  app_state.sentence_scroll_pos = math.min(app_state.sentence_scroll_pos, max_scroll)

  -- 绘制可见的句子
  local y_offset = 5
  local start_idx = math.max(1, app_state.sentence_scroll_pos + 1)
  local end_idx = math.min(#sentences, start_idx + visible_items - 1)

  for i = start_idx, end_idx do
    local sentence = sentences[i]
    if sentence and sentence ~= "__SKIP_THIS_SENTENCE__" then
      local y_pos = content_area.y + y_offset

      -- 检查是否是搜索结果
      local is_search_result = false
      for _, result_idx in ipairs(app_state.search_results) do
        if result_idx == i then
          is_search_result = true
          break
        end
      end

      -- 绘制句子背景（悬停或搜索高亮）
      if app_state.hover_sentence_idx == i then
        gfx.set(0.3, 0.3, 0.4, 1)  -- 悬停背景
        gfx.rect(content_area.x + 2, y_pos - 2, content_area.w - 4, line_height)
      elseif is_search_result then
        gfx.set(search_highlight_color.r, search_highlight_color.g, search_highlight_color.b, search_highlight_color.a)
        gfx.rect(content_area.x + 2, y_pos - 2, content_area.w - 4, line_height)
      end

      -- 绘制句子文本
      gfx.set(1, 1, 1, 1)  -- 白色文本
      gfx.x, gfx.y = content_area.x + 5, y_pos

      -- 清理标签后显示
      local clean_sentence = ui_module.clean_text_tags(sentence)
      gfx.drawstr(clean_sentence)

      y_offset = y_offset + line_height
    end
  end
end

-- 绘制内容区域滚动条
function ui_module.draw_content_scrollbar(app_state, content_area)
  local sentences = app_state.sentences
  if #sentences == 0 then return end

  local line_height = content_font_size + 5
  local visible_items = math.floor((content_area.h - 10) / line_height)

  if #sentences > visible_items then
    local scrollbar = {
      x = content_area.x + content_area.w - 10,
      y = content_area.y,
      w = 10,
      h = content_area.h
    }

    local max_scroll = math.max(0, #sentences - visible_items)
    style_module.draw_scrollbar(scrollbar, app_state.sentence_scroll_pos, max_scroll, visible_items, #sentences)
  end
end

-- 清除文本标签的辅助函数
function ui_module.clean_text_tags(text)
  if not text then return "" end

  -- 清除各种标签
  local cleaned = text:gsub("%[#[0-9A-Fa-f]*%]", ""):gsub("%[#%]", "")
  cleaned = cleaned:gsub("%[bg#[0-9A-Fa-f]*%]", ""):gsub("%[bg#%]", "")
  cleaned = cleaned:gsub("%[x%]", ""):gsub("%[/x%]", "")
  cleaned = cleaned:gsub("%[CHAPTER%]", ""):gsub("%[/CHAPTER%]", "")

  return cleaned
end

-- 绘制CV角色列表
function ui_module.draw_cv_role_list(app_state)
  if not ui.cv_role_list then return end

  local cv_role_list = ui.cv_role_list

  -- 绘制背景
  gfx.set(0.15, 0.15, 0.15, 1)  -- 深灰色背景
  gfx.rect(cv_role_list.x, cv_role_list.y, cv_role_list.w, cv_role_list.h)
  style_module.draw_black_outline(cv_role_list.x, cv_role_list.y, cv_role_list.w, cv_role_list.h)

  -- 绘制标题
  gfx.set(1, 1, 1, 1)  -- 白色文本
  gfx.x, gfx.y = cv_role_list.x + 5, cv_role_list.y + 5
  gfx.drawstr("CV角色列表")

  -- 绘制CV角色对
  ui_module.draw_cv_role_pairs(app_state, cv_role_list)

  -- 绘制滚动条
  ui_module.draw_cv_role_scrollbar(app_state, cv_role_list)
end

-- 绘制CV角色对
function ui_module.draw_cv_role_pairs(app_state, cv_role_list)
  local cv_role_pairs = app_state.cv_role_pairs
  if #cv_role_pairs == 0 then
    gfx.set(0.6, 0.6, 0.6, 1)
    gfx.x, gfx.y = cv_role_list.x + 10, cv_role_list.y + 30
    gfx.drawstr("暂无CV角色数据")
    return
  end

  -- 计算可见区域
  local line_height = font_size + 3
  local visible_area_height = cv_role_list.h - 30
  local visible_items = math.floor(visible_area_height / line_height)

  -- 计算滚动范围
  local max_scroll = math.max(0, #cv_role_pairs - visible_items)
  app_state.cv_role_scroll_pos = math.min(app_state.cv_role_scroll_pos, max_scroll)

  -- 绘制可见的CV角色对
  local y_offset = 30
  local start_idx = math.max(1, app_state.cv_role_scroll_pos + 1)
  local end_idx = math.min(#cv_role_pairs, start_idx + visible_items - 1)

  for i = start_idx, end_idx do
    local pair = cv_role_pairs[i]
    if pair then
      local y_pos = cv_role_list.y + y_offset

      -- 检查是否是选中的CV角色对
      local is_selected = (pair.cv == app_state.selected_cv and pair.role == app_state.selected_role)

      -- 绘制背景
      if is_selected then
        gfx.set(0.2, 0.4, 0.6, 1)  -- 选中背景
        gfx.rect(cv_role_list.x + 2, y_pos - 2, cv_role_list.w - 4, line_height)
      elseif app_state.hover_cv_role.cv == pair.cv and app_state.hover_cv_role.role == pair.role then
        gfx.set(0.3, 0.3, 0.4, 1)  -- 悬停背景
        gfx.rect(cv_role_list.x + 2, y_pos - 2, cv_role_list.w - 4, line_height)
      end

      -- 绘制CV角色文本
      gfx.set(1, 1, 1, 1)  -- 白色文本
      gfx.x, gfx.y = cv_role_list.x + 5, y_pos
      gfx.drawstr(string.format("%s - %s", pair.cv, pair.role))

      y_offset = y_offset + line_height
    end
  end
end

-- 绘制CV角色列表滚动条
function ui_module.draw_cv_role_scrollbar(app_state, cv_role_list)
  local cv_role_pairs = app_state.cv_role_pairs
  if #cv_role_pairs == 0 then return end

  local line_height = font_size + 3
  local visible_items = math.floor((cv_role_list.h - 30) / line_height)

  if #cv_role_pairs > visible_items then
    local scrollbar = {
      x = cv_role_list.x + cv_role_list.w - 10,
      y = cv_role_list.y,
      w = 10,
      h = cv_role_list.h
    }

    local max_scroll = math.max(0, #cv_role_pairs - visible_items)
    style_module.draw_scrollbar(scrollbar, app_state.cv_role_scroll_pos, max_scroll, visible_items, #cv_role_pairs)
  end
end

-- 绘制选择内容区域
function ui_module.draw_selection_content(app_state)
  if not ui.selection_area then return end

  local selection_area = ui.selection_area

  -- 绘制背景
  gfx.set(0.15, 0.15, 0.15, 1)  -- 深灰色背景
  gfx.rect(selection_area.x, selection_area.y, selection_area.w, selection_area.h)
  style_module.draw_black_outline(selection_area.x, selection_area.y, selection_area.w, selection_area.h)

  -- 准备显示内容
  local content_to_draw = {}

  -- 添加选中的CV和角色信息
  if app_state.selected_cv ~= "" and app_state.selected_role ~= "" then
    table.insert(content_to_draw, {
      type = "text",
      text = "选中的CV: " .. app_state.selected_cv,
      height = 20,
      is_header = true
    })
    table.insert(content_to_draw, {
      type = "text",
      text = "选中的角色: " .. app_state.selected_role,
      height = 20,
      is_header = true
    })
  end

  -- 添加选中的文本内容
  if #app_state.selected_texts > 0 then
    table.insert(content_to_draw, {
      type = "text",
      text = "选中的文本:",
      height = 20,
      is_header = true
    })

    for i, text in ipairs(app_state.selected_texts) do
      local clean_text = ui_module.clean_text_tags(text)
      table.insert(content_to_draw, {
        type = "text",
        text = string.format("%d. %s", i, clean_text),
        height = 20,
        is_header = false
      })
    end
  elseif app_state.selected_text ~= "" then
    table.insert(content_to_draw, {
      type = "text",
      text = "选中的文本:",
      height = 20,
      is_header = true
    })

    local clean_text = ui_module.clean_text_tags(app_state.selected_text)
    table.insert(content_to_draw, {
      type = "text",
      text = clean_text,
      height = 20,
      is_header = false
    })
  end

  -- 计算总内容高度
  local total_height = 0
  for _, content in ipairs(content_to_draw) do
    total_height = total_height + content.height
  end

  -- 绘制内容
  local y_offset = 5 - app_state.selection_scroll_pos
  for _, content in ipairs(content_to_draw) do
    if y_offset + content.height > 0 and y_offset < selection_area.h then
      if content.type == "text" then
        gfx.set(1, 1, 1, 1)  -- 白色文本
        local x_offset = content.is_header and 15 or 5
        gfx.x, gfx.y = selection_area.x + x_offset, selection_area.y + y_offset
        gfx.drawstr(content.text)
      end
    end
    y_offset = y_offset + content.height
  end

  -- 绘制滚动条
  if total_height > selection_area.h then
    local scrollbar = {
      x = selection_area.x + selection_area.w - 10,
      y = selection_area.y,
      w = 10,
      h = selection_area.h
    }

    local max_scroll = math.max(0, total_height - selection_area.h)
    style_module.draw_scrollbar(scrollbar, app_state.selection_scroll_pos, max_scroll, selection_area.h, total_height)
  end
end

-- 绘制章节列表
function ui_module.draw_chapter_list(app_state)
  if not ui.chapter_list then return end

  local chapter_list = ui.chapter_list

  -- 绘制背景
  gfx.set(0.12, 0.12, 0.12, 1)  -- 深灰色背景
  gfx.rect(chapter_list.x, chapter_list.y, chapter_list.w, chapter_list.h)
  style_module.draw_black_outline(chapter_list.x, chapter_list.y, chapter_list.w, chapter_list.h)

  -- 绘制标题
  gfx.set(1, 1, 1, 1)  -- 白色文本
  gfx.x, gfx.y = chapter_list.x + 5, chapter_list.y + 5
  gfx.drawstr("章节列表")

  local chapters = app_state.chapters
  if #chapters == 0 then
    gfx.set(0.6, 0.6, 0.6, 1)
    gfx.x, gfx.y = chapter_list.x + 10, chapter_list.y + 30
    gfx.drawstr("暂无章节数据")
    return
  end

  -- 计算可见区域
  local line_height = font_size + 5
  local visible_area_height = chapter_list.h - 10
  local visible_items = math.floor(visible_area_height / line_height)

  -- 计算滚动范围
  local max_scroll = math.max(0, #chapters - visible_items)
  app_state.chapter_scroll_pos = math.min(app_state.chapter_scroll_pos, max_scroll)

  -- 绘制滚动条
  if #chapters > visible_items then
    local scrollbar = {
      x = chapter_list.x + chapter_list.w - 10,
      y = chapter_list.y,
      w = 10,
      h = chapter_list.h
    }
    style_module.draw_scrollbar(scrollbar, app_state.chapter_scroll_pos, max_scroll, visible_items, #chapters)
  end

  -- 绘制章节
  local y_offset = 30
  local start_idx = math.max(1, app_state.chapter_scroll_pos + 1)
  local end_idx = math.min(#chapters, start_idx + visible_items - 1)

  for i = start_idx, end_idx do
    local chapter = chapters[i]
    if chapter then
      local y_pos = chapter_list.y + y_offset

      -- 检查悬停状态
      if app_state.hover_chapter_idx == i then
        gfx.set(0.3, 0.3, 0.4, 1)  -- 悬停背景
        gfx.rect(chapter_list.x + 2, y_pos - 2, chapter_list.w - 4, line_height)
      end

      -- 绘制章节文本
      gfx.set(1, 1, 1, 1)  -- 白色文本
      gfx.x, gfx.y = chapter_list.x + 5, y_pos
      gfx.drawstr(chapter.title)

      y_offset = y_offset + line_height
    end
  end
end

-- 绘制输入区域
function ui_module.draw_input_areas(app_state)
  if not ui.error_input then return end

  -- 绘制各个输入区域
  button_module.draw_input_area(ui.error_input, app_state.error_note, "", "错误描述:")
  button_module.draw_input_area(ui.correct_input, app_state.correct_note, "", "正确表达:")
  button_module.draw_input_area(ui.episode_input, app_state.episode_number, "", "集数:")
  button_module.draw_suggestion_input(ui.suggestion_input, app_state.process_suggestion, "", "处理建议:")
end

-- 绘制按钮
function ui_module.draw_buttons(app_state)
  if not ui.block_mark_button then return end

  -- 检查按钮启用状态
  local has_selected_item = r.GetSelectedMediaItem(0, 0) ~= nil
  local role_cv_selected = app_state.selected_cv ~= "" and app_state.selected_role ~= ""
  local has_process_suggestion = app_state.process_suggestion ~= ""

  -- 块标按钮需要满足三个条件
  local block_mark_button_enabled = has_selected_item and role_cv_selected and has_process_suggestion
  -- 区标按钮需要满足两个条件
  local region_mark_button_enabled = role_cv_selected and has_process_suggestion

  -- 绘制打标按钮
  button_module.draw_block_mark_button(ui.block_mark_button, block_mark_button_enabled)
  button_module.draw_region_mark_button(ui.region_mark_button, region_mark_button_enabled)

  -- 绘制播放按钮
  if ui.play_button then
    button_module.draw_play_button(ui.play_button, app_state.is_playing)
  end

  -- 绘制Excel按钮
  if ui.excel_button then
    local has_selected_text = #app_state.selected_texts > 0 or app_state.selected_text ~= ""
    button_module.draw_excel_button(ui.excel_button, has_selected_text)
  end

  -- 绘制速率控制按钮
  if ui.rate_minus_button and ui.rate_plus_button and ui.rate_display_area then
    -- 确保current_playrate有默认值
    local current_playrate = app_state.current_playrate or 1.0
    -- 修正参数顺序：rate_minus_button, rate_display_area, rate_plus_button, rate_reset_button, current_playrate
    button_module.draw_rate_buttons(ui.rate_minus_button, ui.rate_display_area, ui.rate_plus_button, ui.rate_reset_button, current_playrate)
  end

  -- 绘制字体大小按钮
  if ui.font_decrease_button and ui.font_increase_button then
    button_module.draw_font_buttons(ui.font_size_label, ui.font_decrease_button, ui.font_increase_button, ui.font_size_display, content_font_size)
  end

  -- 绘制文档和剪贴板按钮
  if ui.document_button and ui.clipboard_button then
    button_module.draw_document_buttons(ui.document_button, ui.clipboard_button)
  end

  -- 绘制开/AU/区名/文名/轨色/分轨按钮
  if ui.open_csv_button and ui.au_button then
    button_module.draw_open_au_buttons(
      ui.open_csv_button,
      ui.au_button,
      ui.region_name_button,
      ui.file_name_button,
      ui.track_color_button,
      ui.track_split_button
    )
  end

  -- 绘制对轨按钮
  if ui.track_align_button then
    button_module.draw_track_align_button(ui.track_align_button, app_state.is_track_align_enabled)
  end

  -- 绘制章节按钮
  if ui.chapter_button then
    button_module.draw_chapter_button(ui.chapter_button, app_state.is_chapter_list_visible)
  end
end

-- 绘制搜索UI
function ui_module.draw_search_ui(app_state)
  if not ui.content_area or not ui.track_align_button then return end

  local content_area = ui.content_area

  -- 搜索框位置 - 放在轨按钮左边
  local search_box = {
    x = ui.track_align_button.x - 210,  -- 在轨按钮左边，留出10像素间距
    y = ui.track_align_button.y,       -- 与轨按钮同一水平线
    w = 200,
    h = 25
  }

  -- 更新UI元素中的搜索框位置
  ui.search_box = search_box

  -- 绘制搜索框背景
  if app_state.search_input_active then
    gfx.set(1.0, 1.0, 0.9, 1)  -- 激活时浅黄色背景
  else
    gfx.set(0.9, 0.9, 0.9, 1)  -- 普通时浅灰色背景
  end
  gfx.rect(search_box.x, search_box.y, search_box.w, search_box.h)

  -- 绘制搜索框边框
  if app_state.search_input_active then
    gfx.set(0.0, 0.5, 1.0, 1)  -- 激活时蓝色边框
  else
    gfx.set(0.5, 0.5, 0.5, 1)  -- 普通时深灰色边框
  end
  style_module.draw_black_outline(search_box.x, search_box.y, search_box.w, search_box.h)

  -- 绘制搜索文本
  gfx.set(0, 0, 0, 1)  -- 黑色文本
  gfx.x, gfx.y = search_box.x + 5, search_box.y + 5
  local display_text = app_state.search_text
  if display_text == "" then
    if app_state.search_input_active then
      display_text = "输入搜索关键词..."
      gfx.set(0.4, 0.4, 0.4, 1)  -- 激活时深灰色占位符
    else
      display_text = "搜索... (Ctrl+F)"
      gfx.set(0.6, 0.6, 0.6, 1)  -- 普通时浅灰色占位符
    end
  end
  gfx.drawstr(display_text)

  -- 绘制光标
  if app_state.search_input_active then
    local text_width = gfx.measurestr(display_text)
    gfx.set(0, 0, 0, 1)
    gfx.line(search_box.x + 5 + text_width, search_box.y + 3, search_box.x + 5 + text_width, search_box.y + search_box.h - 3)
  end

  -- 绘制搜索结果信息和导航按钮
  if #app_state.search_results > 0 then
    ui_module.draw_search_results_info(app_state, search_box)
  end

  -- 存储搜索框信息供事件处理使用
  ui.search_box = search_box
end

-- 绘制搜索结果信息和导航按钮
function ui_module.draw_search_results_info(app_state, search_box)
  -- 显示搜索结果数量
  gfx.set(0, 0, 0, 1)
  gfx.x, gfx.y = search_box.x + search_box.w + 10, search_box.y + 5
  local result_text = string.format("%d/%d", app_state.current_search_index, #app_state.search_results)
  gfx.drawstr(result_text)

  -- 绘制导航按钮
  local btn_size = 20
  local btn_y = search_box.y + 2

  -- 上一个按钮
  local prev_btn = {
    x = search_box.x + search_box.w + 50,
    y = btn_y,
    w = btn_size,
    h = btn_size
  }

  -- 下一个按钮
  local next_btn = {
    x = prev_btn.x + btn_size + 5,
    y = btn_y,
    w = btn_size,
    h = btn_size
  }

  -- 绘制按钮背景
  gfx.set(0.8, 0.8, 0.8, 1)
  gfx.rect(prev_btn.x, prev_btn.y, prev_btn.w, prev_btn.h)
  gfx.rect(next_btn.x, next_btn.y, next_btn.w, next_btn.h)

  -- 绘制按钮边框
  gfx.set(0.5, 0.5, 0.5, 1)
  style_module.draw_black_outline(prev_btn.x, prev_btn.y, prev_btn.w, prev_btn.h)
  style_module.draw_black_outline(next_btn.x, next_btn.y, next_btn.w, next_btn.h)

  -- 绘制按钮文本
  gfx.set(0, 0, 0, 1)
  gfx.x, gfx.y = prev_btn.x + 6, prev_btn.y + 4
  gfx.drawstr("↑")
  gfx.x, gfx.y = next_btn.x + 6, next_btn.y + 4
  gfx.drawstr("↓")

  -- 存储按钮信息供事件处理使用
  ui.search_prev_btn = prev_btn
  ui.search_next_btn = next_btn
end

-- 获取UI元素（供事件处理使用）
function ui_module.get_ui_elements()
  return ui
end

-- 强制重绘
function ui_module.force_redraw()
  force_redraw = true
end

-- 滚动到指定句子
function ui_module.scroll_to_sentence(app_state, sentence_index)
  if not sentence_index or sentence_index < 1 or sentence_index > #app_state.sentences then
    return
  end

  -- 计算目标滚动位置
  local line_height = content_font_size + 5
  local visible_items_count = math.floor((ui.content_area.h - 10) / line_height)
  local target_scroll = math.max(0, sentence_index - math.floor(visible_items_count / 2))

  -- 确保不超过最大滚动范围
  local max_scroll = math.max(0, #app_state.sentences - visible_items_count)
  app_state.sentence_scroll_pos = math.min(target_scroll, max_scroll)
  app_state.force_redraw = true
end

return ui_module