-- 打标工具 - REAPER脚本 (重构版本)
-- 用于音频后期制作工作流程
-- 阶段1重构：将UI和事件处理逻辑分离到独立模块

-- 导入必要的库
local r = reaper

-- 获取脚本路径
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"

-- 安全加载函数
local function safe_load_module(file_path)
  local success, result = pcall(dofile, file_path)
  if not success then
    r.ShowConsoleMsg("模块加载失败: " .. file_path .. " - " .. tostring(result) .. "\n")
    return {}
  end
  return result
end

-- 加载模块加载器
local module_loader = safe_load_module(script_dir .. "module_loader.lua")

-- 使用模块加载器加载所有核心模块
local modules = module_loader.load_all({
  show_init_message = false
})

-- 获取各个模块
local utils_module = modules.utils_module
local text_utils = modules.text_utils
local excel_module = modules.excel_module
local button_module = modules.button_module
local style_module = modules.style_module
local word_module = modules.word_module

-- 加载新的UI和事件模块
local ui_module = safe_load_module(script_dir .. "ui_module.lua")
local event_module = safe_load_module(script_dir .. "event_module.lua")

-- 验证关键模块是否加载成功
if not utils_module or not ui_module or not event_module or not style_module then
  r.ShowConsoleMsg("关键模块加载失败，脚本无法运行\n")
  return
end

-- 初始化缓存功能
if utils_module and utils_module.init_cache then
  utils_module.init_cache({
    max_size = 2000,
    cleanup_interval = 60,
    cache_lifetime = 3600,
    enable_auto_cleanup = true
  })
end

-- 创建应用状态
local app_state = utils_module.app_state.create()

-- 初始化style_module（必须先初始化，因为其他模块依赖它）
style_module.init({utils_module = utils_module})

-- 初始化模块依赖
local deps = {
  style_module = style_module,
  text_utils = text_utils,
  button_module = button_module,
  utils_module = utils_module,
  gfx = gfx,
  r = r
}

-- 初始化UI模块
ui_module.init(deps)

-- 初始化事件模块
deps.ui_module = ui_module
event_module.init(deps)

-- 初始化按钮模块
if button_module and button_module.init then
  button_module.init({
    utils_module = utils_module,
    text_utils = text_utils,
    excel_module = excel_module,
    word_module = word_module,
    style_module = style_module
  })
end

-- 辅助函数：从剪贴板读取文本
function load_from_clipboard()
  local success, text, error_msg = text_utils.get_clipboard()
  if success and text and text ~= "" then
    app_state.clipboard_text = text
    app_state.sentences = text_utils.parse_sentences(text)
    app_state.cv_role_pairs = text_utils.extract_cv_role_pairs(app_state.sentences, false)

    -- 提取章节信息
    if text_utils.extract_chapters then
      app_state.chapters = text_utils.extract_chapters(app_state.sentences)
    end

    app_state.force_redraw = true
    return true, "成功从剪贴板读取 " .. #app_state.sentences .. " 个句子"
  else
    return false, error_msg or "剪贴板为空或读取失败"
  end
end

-- 辅助函数：从文件读取文本
function load_from_file(file_path)
  if not file_path then return false, "未指定文件路径" end

  local success, text, error_msg = text_utils.read_text_file(file_path)
  if success and text and text ~= "" then
    app_state.clipboard_text = text
    app_state.sentences = text_utils.parse_sentences(text)
    app_state.cv_role_pairs = text_utils.extract_cv_role_pairs(app_state.sentences, false)

    -- 提取章节信息
    if text_utils.extract_chapters then
      app_state.chapters = text_utils.extract_chapters(app_state.sentences)
    end

    app_state.force_redraw = true
    return true, "成功从文件读取 " .. #app_state.sentences .. " 个句子"
  else
    return false, error_msg or "文件读取失败"
  end
end

-- 处理搜索相关键盘输入
function handle_search_input(char)
  if app_state.search_input_active then
    if char == 13 then  -- 回车键
      event_module.perform_search(app_state, app_state.search_text)
      app_state.search_input_active = false
    elseif char == 27 then  -- ESC键
      app_state.search_input_active = false
    elseif char == 8 then  -- 退格键
      if #app_state.search_text > 0 then
        app_state.search_text = app_state.search_text:sub(1, -2)
        event_module.perform_search(app_state, app_state.search_text)
      end
    elseif char > 31 and char < 127 then  -- 可打印ASCII字符
      app_state.search_text = app_state.search_text .. string.char(char)
      event_module.perform_search(app_state, app_state.search_text)
    end
    return true  -- 表示已处理
  end
  return false  -- 表示未处理
end

-- 处理全局快捷键
function handle_global_shortcuts(char)
  -- Ctrl+O: 从剪贴板读取
  if char == 15 then  -- Ctrl+O
    local success, message = load_from_clipboard()
    if success then
      r.ShowConsoleMsg(message .. "\n")
    else
      r.ShowConsoleMsg("错误: " .. message .. "\n")
    end
    return true
  end

  -- Ctrl+F: 激活搜索
  if char == 6 then  -- Ctrl+F
    app_state.search_input_active = true
    app_state.force_redraw = true
    return true
  end

  -- Ctrl+G: 下一个搜索结果
  if char == 7 then  -- Ctrl+G
    event_module.goto_next_search_result(app_state)
    return true
  end

  -- Ctrl+P: 上一个搜索结果
  if char == 16 then  -- Ctrl+P
    event_module.goto_prev_search_result(app_state)
    return true
  end

  -- ESC: 退出
  if char == 27 then
    app_state.should_exit = true
    return true
  end

  return false
end

-- 初始化函数
function init()
  local status, err = pcall(function()
    -- 初始化UI窗口
    if not ui_module.init_window() then
      return false
    end

    -- 尝试从剪贴板加载初始数据
    load_from_clipboard()

    return true
  end)

  if not status then
    utils_module.error_handler:add("初始化失败: " .. tostring(err), "error", "init")
    r.ShowConsoleMsg("初始化失败: " .. tostring(err) .. "\n")
    return false
  end

  return true
end

-- 主循环函数
function main_loop()
  local char = gfx.getchar()

  -- 处理搜索输入
  if handle_search_input(char) then
    return true  -- 搜索模式下不处理其他键盘事件
  end

  -- 处理全局快捷键
  if handle_global_shortcuts(char) then
    return not app_state.should_exit
  end

  -- 检查窗口是否关闭
  if char == -1 then
    app_state.should_exit = true
    return false
  end

  -- 处理事件
  event_module.handle_events(app_state)

  -- 渲染界面
  ui_module.render(app_state)

  -- 检查退出条件
  return not app_state.should_exit
end

-- 清理函数
function cleanup()
  -- 保存设置或执行清理操作
  if utils_module and utils_module.cache_clear then
    utils_module.cache_clear()
  end
end

-- 主函数
function main()
  -- 初始化
  if not init() then
    return
  end

  -- 主循环
  local function loop()
    if main_loop() then
      r.defer(loop)
    else
      cleanup()
    end
  end

  -- 启动循环
  r.defer(loop)
end

-- 安全执行主函数
local function safe_main()
  local status, err = pcall(main)
  if not status then
    r.ShowConsoleMsg("主函数执行失败: " .. tostring(err) .. "\n")
  end
end

-- 运行主函数
safe_main()
