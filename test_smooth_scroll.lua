-- 平滑滚动测试脚本
-- 验证改进后的滚动功能是否像原mark.lua那样丝滑

-- 导入必要的库
local r = reaper

-- 获取脚本路径
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"

-- 安全加载函数
local function safe_load_module(file_path)
  local success, result = pcall(dofile, file_path)
  if not success then
    r.ShowConsoleMsg("模块加载失败: " .. file_path .. " - " .. tostring(result) .. "\n")
    return nil
  end
  return result
end

-- 主函数
local function main()
  r.ShowConsoleMsg("=== 平滑滚动测试 ===\n")
  
  -- 加载所有模块
  local utils_module = safe_load_module(script_dir .. "utils_module.lua")
  local style_module = safe_load_module(script_dir .. "style_module.lua")
  local text_utils = safe_load_module(script_dir .. "text_utils.lua")
  local button_module = safe_load_module(script_dir .. "button_module.lua")
  local ui_module = safe_load_module(script_dir .. "ui_module.lua")
  local event_module = safe_load_module(script_dir .. "event_module.lua")
  
  if not utils_module or not style_module or not text_utils or not button_module or not ui_module or not event_module then
    r.ShowConsoleMsg("✗ 模块加载失败\n")
    return
  end
  r.ShowConsoleMsg("✓ 所有模块加载成功\n")
  
  -- 初始化模块
  style_module.init({utils_module = utils_module})
  
  local deps = {
    style_module = style_module,
    text_utils = text_utils,
    button_module = button_module,
    utils_module = utils_module,
    gfx = gfx,
    r = r
  }
  
  ui_module.init(deps)
  deps.ui_module = ui_module
  event_module.init(deps)
  
  if button_module and button_module.init then
    button_module.init({
      utils_module = utils_module,
      text_utils = text_utils,
      style_module = style_module
    })
  end
  
  r.ShowConsoleMsg("✓ 所有模块初始化成功\n")
  
  -- 创建应用状态
  local app_state = utils_module.app_state.create()
  r.ShowConsoleMsg("✓ 应用状态创建成功\n")
  
  -- 设置大量测试内容以便测试滚动
  local test_content = {}
  for i = 1, 100 do
    table.insert(test_content, string.format("[CHAPTER]第%d章 测试章节标题[/CHAPTER]", i))
    table.insert(test_content, string.format("　　这是第%d章的内容，用于测试滚动功能。", i))
    table.insert(test_content, string.format("　　这一章包含了很多有趣的内容和对话。"))
    table.insert(test_content, string.format("【测试CV%d-角色%d】：这是一个测试对话。", i, i))
    table.insert(test_content, string.format("　　角色%d说了很多话，用来测试文本显示。", i))
    table.insert(test_content, string.format("　　这样我们就有足够的内容来测试滚动了。"))
    table.insert(test_content, "")  -- 空行
  end
  
  app_state.clipboard_text = table.concat(test_content, "\n")
  r.ShowConsoleMsg("✓ 设置大量测试内容成功（" .. #test_content .. " 行）\n")
  
  -- 解析内容
  r.ShowConsoleMsg("开始解析内容...\n")
  
  -- 句子解析
  event_module.parse_sentences(app_state)
  r.ShowConsoleMsg("✓ 句子解析完成，共 " .. #app_state.sentences .. " 个句子\n")
  
  -- CV角色对提取
  event_module.extract_cv_role_pairs(app_state)
  r.ShowConsoleMsg("✓ CV角色对提取完成，共 " .. #app_state.cv_role_pairs .. " 个角色对\n")
  
  -- 章节提取
  event_module.extract_chapters(app_state)
  r.ShowConsoleMsg("✓ 章节提取完成，共 " .. #app_state.chapters .. " 个章节\n")
  
  -- 初始化平滑滚动状态
  event_module.init_smooth_scroll(app_state)
  r.ShowConsoleMsg("✓ 平滑滚动状态初始化完成\n")
  
  -- 启动UI测试
  r.ShowConsoleMsg("\n=== 启动平滑滚动测试 ===\n")
  
  local window_success = ui_module.init_window()
  if window_success then
    r.ShowConsoleMsg("✓ 窗口初始化成功\n")
    
    -- 设置章节列表可见
    app_state.is_chapter_list_visible = true
    
    -- 初始渲染
    local render_success, render_error = pcall(function()
      ui_module.render(app_state)
    end)
    
    if render_success then
      r.ShowConsoleMsg("✓ 初始渲染成功\n")
      
      -- 启动主循环
      local function loop()
        local char = gfx.getchar()
        if char == -1 then
          r.ShowConsoleMsg("窗口已关闭\n")
          return
        end
        
        -- 处理事件和渲染
        local event_success, event_error = pcall(function()
          event_module.handle_events(app_state)
          ui_module.render(app_state)
        end)
        
        if not event_success then
          r.ShowConsoleMsg("事件处理错误: " .. tostring(event_error) .. "\n")
        end
        
        r.defer(loop)
      end
      
      r.ShowConsoleMsg("\n🎉 平滑滚动测试启动成功！\n")
      r.ShowConsoleMsg("\n=== 滚动测试指南 ===\n")
      r.ShowConsoleMsg("请在打开的窗口中测试以下滚动功能：\n")
      r.ShowConsoleMsg("\n1. 文本框滚动测试：\n")
      r.ShowConsoleMsg("   • 将鼠标移到文本框区域\n")
      r.ShowConsoleMsg("   • 使用鼠标滚轮上下滚动\n")
      r.ShowConsoleMsg("   • 观察滚动是否平滑丝滑\n")
      r.ShowConsoleMsg("   • 测试快速滚动和慢速滚动\n")
      
      r.ShowConsoleMsg("\n2. 章节列表滚动测试：\n")
      r.ShowConsoleMsg("   • 将鼠标移到左侧章节列表区域\n")
      r.ShowConsoleMsg("   • 使用鼠标滚轮上下滚动\n")
      r.ShowConsoleMsg("   • 观察滚动是否平滑\n")
      
      r.ShowConsoleMsg("\n3. CV角色列表滚动测试：\n")
      r.ShowConsoleMsg("   • 将鼠标移到右侧CV角色列表区域\n")
      r.ShowConsoleMsg("   • 使用鼠标滚轮上下滚动\n")
      r.ShowConsoleMsg("   • 观察滚动是否平滑\n")
      
      r.ShowConsoleMsg("\n=== 平滑滚动改进特点 ===\n")
      r.ShowConsoleMsg("✅ 减少滚轮冷却时间 - 提高响应性\n")
      r.ShowConsoleMsg("✅ 平滑滚动算法 - 使用目标位置和当前位置插值\n")
      r.ShowConsoleMsg("✅ 可调节平滑系数 - 控制滚动平滑程度\n")
      r.ShowConsoleMsg("✅ 每帧更新 - 确保滚动连续性\n")
      r.ShowConsoleMsg("✅ 优化滚动量 - 更合适的滚动距离\n")
      
      r.ShowConsoleMsg("\n=== 技术参数 ===\n")
      r.ShowConsoleMsg("• 滚轮冷却时间: 0.005秒（原来0.01秒）\n")
      r.ShowConsoleMsg("• 平滑系数: 0.15（值越小越平滑）\n")
      r.ShowConsoleMsg("• 最小变化量: 0.01（防止无限小的变化）\n")
      r.ShowConsoleMsg("• 滚动量: 1.5倍滚轮值（优化后）\n")
      
      r.ShowConsoleMsg("\n=== 对比原mark.lua ===\n")
      r.ShowConsoleMsg("如果滚动感觉还不够丝滑，可能的原因：\n")
      r.ShowConsoleMsg("1. 平滑系数需要调整（当前0.15）\n")
      r.ShowConsoleMsg("2. 滚动量需要微调（当前1.5倍）\n")
      r.ShowConsoleMsg("3. 冷却时间需要进一步减少\n")
      r.ShowConsoleMsg("4. 需要添加更多的滚动优化\n")
      
      r.ShowConsoleMsg("\n现在请测试滚动功能，看看是否达到了原mark.lua的丝滑程度！\n")
      r.defer(loop)
      
    else
      r.ShowConsoleMsg("✗ 初始渲染失败: " .. tostring(render_error) .. "\n")
    end
  else
    r.ShowConsoleMsg("! 窗口初始化跳过（可能是环境限制）\n")
    r.ShowConsoleMsg("但是平滑滚动逻辑已经实现，主脚本应该能正常工作\n")
  end
  
  r.ShowConsoleMsg("\n=== 平滑滚动改进总结 ===\n")
  r.ShowConsoleMsg("已实施的改进：\n")
  r.ShowConsoleMsg("✅ 平滑滚动状态管理 - 目标位置和当前位置分离\n")
  r.ShowConsoleMsg("✅ 插值算法 - 使用线性插值实现平滑过渡\n")
  r.ShowConsoleMsg("✅ 每帧更新 - 在每次事件处理时更新滚动状态\n")
  r.ShowConsoleMsg("✅ 优化参数 - 调整滚轮冷却时间和滚动量\n")
  r.ShowConsoleMsg("✅ 多区域支持 - 文本框、章节列表、CV列表都支持平滑滚动\n")
  
  r.ShowConsoleMsg("\n现在可以运行主脚本测试完整功能：\n")
  r.ShowConsoleMsg("dofile(\"mark_new.lua\")\n")
  
  r.ShowConsoleMsg("\n=== 平滑滚动测试完成 ===\n")
end

-- 运行测试
main()
