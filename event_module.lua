-- 事件模块 - 负责所有事件处理逻辑
-- 从mark.lua中提取的事件相关功能

local event_module = {}

-- 字符串trim函数
local function trim(s)
  if not s then return "" end
  return s:match("^%s*(.-)%s*$") or ""
end

-- 依赖的模块（通过init函数注入）
local ui_module, button_module, text_utils, utils_module, style_module, gfx, r

-- 事件处理状态
local event_state = {
  last_mouse_x = 0,
  last_mouse_y = 0,
  last_mouse_state = 0,
  last_click_time = 0,
  last_wheel_time = 0,
  click_cooldown = 0.01,
  wheel_cooldown = 0.005,  -- 减少滚轮冷却时间，提高响应性
  is_dragging_sentence_scrollbar = false,
  is_dragging_cv_role_scrollbar = false,
  is_dragging_chapter_scrollbar = false,
  is_dragging_selection_scrollbar = false,
  is_mouse_down = false,
  is_ctrl_down = false,

  -- 平滑滚动状态
  smooth_scroll = {
    content_target = 0,
    content_current = 0,
    cv_role_target = 0,
    cv_role_current = 0,
    chapter_target = 0,
    chapter_current = 0,
    selection_target = 0,
    selection_current = 0,
    smoothing_factor = 0.3,   -- 平滑系数，进一步提高响应速度
    min_delta = 0.1   -- 最小变化量，进一步减少微小变化
  }
}

-- 初始化事件模块
function event_module.init(deps)
  -- 注入依赖
  ui_module = deps.ui_module
  button_module = deps.button_module
  text_utils = deps.text_utils
  utils_module = deps.utils_module
  style_module = deps.style_module
  gfx = deps.gfx
  r = deps.r

  return event_module
end

-- 主事件处理函数
function event_module.handle_events(app_state)
  local status, err = pcall(function()
    -- 处理鼠标事件
    event_module.handle_mouse_events(app_state)

    -- 处理键盘事件
    event_module.handle_keyboard_events(app_state)

    -- 处理窗口事件
    event_module.handle_window_events(app_state)

    -- 更新按钮状态
    event_module.update_button_states(app_state)
  end)

  if not status then
    utils_module.error_handler:add("事件处理失败: " .. tostring(err), "error", "event_module.handle_events")
    r.ShowConsoleMsg("Event handling error: " .. tostring(err) .. "\n")
  end
end

-- 处理鼠标事件
function event_module.handle_mouse_events(app_state)
  local mouse_x, mouse_y = gfx.mouse_x, gfx.mouse_y
  local mouse_cap = gfx.mouse_cap
  local wheel = gfx.mouse_wheel
  local current_time = r.time_precise()

  -- 更新鼠标位置记录
  event_state.last_mouse_x, event_state.last_mouse_y = mouse_x, mouse_y

  -- 更新平滑滚动（每帧都更新）
  event_module.update_smooth_scroll(app_state)

  -- 处理滚轮事件
  if wheel ~= 0 and (current_time - event_state.last_wheel_time) > event_state.wheel_cooldown then
    event_module.handle_wheel_events(app_state, mouse_x, mouse_y, wheel)
    event_state.last_wheel_time = current_time
    gfx.mouse_wheel = 0  -- 重置滚轮值
  end

  -- 处理鼠标点击事件
  if mouse_cap ~= event_state.last_mouse_state and (current_time - event_state.last_click_time) > event_state.click_cooldown then
    event_module.handle_click_events(app_state, mouse_x, mouse_y, mouse_cap)
    event_state.last_click_time = current_time
  end

  -- 处理鼠标拖拽事件
  event_module.handle_drag_events(app_state, mouse_x, mouse_y, mouse_cap)

  -- 处理鼠标悬停事件
  event_module.handle_hover_events(app_state, mouse_x, mouse_y)

  -- 更新鼠标状态
  event_state.last_mouse_state = mouse_cap
  event_state.is_mouse_down = (mouse_cap & 1) == 1
  event_state.is_ctrl_down = (mouse_cap & 4) == 4
end

-- 处理滚轮事件
function event_module.handle_wheel_events(app_state, mouse_x, mouse_y, wheel)
  local ui = ui_module.get_ui_elements()
  if not ui then return end

  -- 改进的滚动量计算 - 大幅减少滚动速度
  local base_scroll_amount = wheel * 0.2   -- 基础滚动量（大幅减少）
  local smooth_scroll_amount = wheel * 0.15 -- 平滑滚动量（大幅减少）

  -- 检查鼠标在哪个区域
  if event_module.is_point_in_rect(mouse_x, mouse_y, ui.content_area) then
    -- 内容区域滚动
    local line_height = style_module.content_font_size + 5
    local visible_items = math.floor((ui.content_area.h - 10) / line_height)
    local max_scroll = math.max(0, #app_state.sentences - visible_items)

    -- 使用平滑滚动
    event_state.smooth_scroll.content_target = math.max(0, math.min(max_scroll,
      event_state.smooth_scroll.content_target - smooth_scroll_amount))

    app_state.force_redraw = true

  elseif event_module.is_point_in_rect(mouse_x, mouse_y, ui.cv_role_list) then
    -- CV角色列表滚动
    local line_height = style_module.font_size + 3
    local visible_items = math.floor((ui.cv_role_list.h - 30) / line_height)
    local max_scroll = math.max(0, #app_state.cv_role_pairs - visible_items)

    -- 使用平滑滚动
    event_state.smooth_scroll.cv_role_target = math.max(0, math.min(max_scroll,
      event_state.smooth_scroll.cv_role_target - smooth_scroll_amount))

    app_state.force_redraw = true

  elseif ui.chapter_list and event_module.is_point_in_rect(mouse_x, mouse_y, ui.chapter_list) then
    -- 章节列表滚动
    local line_height = style_module.font_size + 5
    local visible_items = math.floor((ui.chapter_list.h - 10) / line_height)
    local max_scroll = math.max(0, #app_state.chapters - visible_items)

    -- 使用平滑滚动
    event_state.smooth_scroll.chapter_target = math.max(0, math.min(max_scroll,
      event_state.smooth_scroll.chapter_target - smooth_scroll_amount))

    app_state.force_redraw = true

  elseif event_module.is_point_in_rect(mouse_x, mouse_y, ui.selection_area) then
    -- 选择区域滚动 - 进一步减少倍数
    event_state.smooth_scroll.selection_target = math.max(0,
      event_state.smooth_scroll.selection_target - smooth_scroll_amount * 1)

    app_state.force_redraw = true
  end
end

-- 处理点击事件
function event_module.handle_click_events(app_state, mouse_x, mouse_y, mouse_cap)
  local ui = ui_module.get_ui_elements()
  if not ui then return end

  -- 检查是否是鼠标按下事件
  if (mouse_cap & 1) == 1 and event_state.last_mouse_state == 0 then
    -- 左键按下
    event_module.handle_left_click(app_state, mouse_x, mouse_y)
  elseif (mouse_cap & 2) == 2 and (event_state.last_mouse_state & 2) == 0 then
    -- 右键按下
    event_module.handle_right_click(app_state, mouse_x, mouse_y)
  end
end

-- 处理左键点击
function event_module.handle_left_click(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()

  -- 检查搜索框点击
  if ui.search_box and event_module.is_point_in_rect(mouse_x, mouse_y, ui.search_box) then
    app_state.search_input_active = true
    app_state.force_redraw = true
    return
  else
    app_state.search_input_active = false
  end

  -- 检查搜索导航按钮
  if ui.search_prev_btn and event_module.is_point_in_rect(mouse_x, mouse_y, ui.search_prev_btn) then
    event_module.goto_prev_search_result(app_state)
    return
  end

  if ui.search_next_btn and event_module.is_point_in_rect(mouse_x, mouse_y, ui.search_next_btn) then
    event_module.goto_next_search_result(app_state)
    return
  end

  -- 检查内容区域点击（句子选择）
  if event_module.is_point_in_rect(mouse_x, mouse_y, ui.content_area) then
    event_module.handle_sentence_click(app_state, mouse_x, mouse_y)
    return
  end

  -- 检查CV角色列表点击
  if event_module.is_point_in_rect(mouse_x, mouse_y, ui.cv_role_list) then
    event_module.handle_cv_role_click(app_state, mouse_x, mouse_y)
    return
  end

  -- 检查章节列表点击
  if ui.chapter_list and event_module.is_point_in_rect(mouse_x, mouse_y, ui.chapter_list) then
    event_module.handle_chapter_click(app_state, mouse_x, mouse_y)
    return
  end

  -- 检查按钮点击
  event_module.handle_button_clicks(app_state, mouse_x, mouse_y)
end

-- 处理右键点击
function event_module.handle_right_click(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()

  -- 检查内容区域右键点击
  if event_module.is_point_in_rect(mouse_x, mouse_y, ui.content_area) then
    event_module.handle_sentence_right_click(app_state, mouse_x, mouse_y)
    return
  end
end

-- 处理拖拽事件
function event_module.handle_drag_events(app_state, mouse_x, mouse_y, mouse_cap)
  -- 处理滚动条拖拽
  if (mouse_cap & 1) == 1 then  -- 左键按下
    event_module.handle_scrollbar_drag(app_state, mouse_x, mouse_y)
  else
    -- 重置拖拽状态
    event_state.is_dragging_sentence_scrollbar = false
    event_state.is_dragging_cv_role_scrollbar = false
    event_state.is_dragging_chapter_scrollbar = false
    event_state.is_dragging_selection_scrollbar = false
  end
end

-- 处理悬停事件
function event_module.handle_hover_events(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()
  if not ui then return end

  -- 重置悬停状态
  local old_hover_sentence = app_state.hover_sentence_idx
  local old_hover_chapter = app_state.hover_chapter_idx
  local old_hover_cv_role = app_state.hover_cv_role

  app_state.hover_sentence_idx = -1
  app_state.hover_chapter_idx = -1
  app_state.hover_cv_role = {cv = "", role = "", is_cv = false}

  -- 检查内容区域悬停
  if event_module.is_point_in_rect(mouse_x, mouse_y, ui.content_area) then
    app_state.hover_sentence_idx = event_module.get_sentence_index_at_position(app_state, mouse_x, mouse_y)
  end

  -- 检查CV角色列表悬停
  if event_module.is_point_in_rect(mouse_x, mouse_y, ui.cv_role_list) then
    local cv_role = event_module.get_cv_role_at_position(app_state, mouse_x, mouse_y)
    if cv_role then
      app_state.hover_cv_role = cv_role
    end
  end

  -- 检查章节列表悬停
  if ui.chapter_list and event_module.is_point_in_rect(mouse_x, mouse_y, ui.chapter_list) then
    app_state.hover_chapter_idx = event_module.get_chapter_index_at_position(app_state, mouse_x, mouse_y)
  end

  -- 检查是否需要重绘
  if old_hover_sentence ~= app_state.hover_sentence_idx or
     old_hover_chapter ~= app_state.hover_chapter_idx or
     old_hover_cv_role.cv ~= app_state.hover_cv_role.cv or
     old_hover_cv_role.role ~= app_state.hover_cv_role.role then
    app_state.force_redraw = true
  end
end

-- 处理键盘事件
function event_module.handle_keyboard_events(app_state)
  -- 键盘事件处理在主循环中进行
  -- 这里主要处理快捷键

  -- Ctrl+F 激活搜索
  if event_state.is_ctrl_down and gfx.getchar() == 102 then  -- 'f' key
    app_state.search_input_active = true
    app_state.force_redraw = true
  end
end

-- 处理窗口事件
function event_module.handle_window_events(app_state)
  -- 检查窗口大小变化
  if gfx.w ~= event_state.last_window_w or gfx.h ~= event_state.last_window_h then
    event_state.last_window_w = gfx.w
    event_state.last_window_h = gfx.h
    app_state.force_redraw = true
  end
end

-- 更新平滑滚动
function event_module.update_smooth_scroll(app_state)
  local smooth = event_state.smooth_scroll
  local needs_redraw = false

  -- 更新内容区域滚动
  local content_delta = smooth.content_target - smooth.content_current
  if math.abs(content_delta) > smooth.min_delta then
    smooth.content_current = smooth.content_current + content_delta * smooth.smoothing_factor
    app_state.sentence_scroll_pos = math.floor(smooth.content_current + 0.5)
    needs_redraw = true
  else
    smooth.content_current = smooth.content_target
    app_state.sentence_scroll_pos = smooth.content_target
  end

  -- 更新CV角色列表滚动
  local cv_role_delta = smooth.cv_role_target - smooth.cv_role_current
  if math.abs(cv_role_delta) > smooth.min_delta then
    smooth.cv_role_current = smooth.cv_role_current + cv_role_delta * smooth.smoothing_factor
    app_state.cv_role_scroll_pos = math.floor(smooth.cv_role_current + 0.5)
    needs_redraw = true
  else
    smooth.cv_role_current = smooth.cv_role_target
    app_state.cv_role_scroll_pos = smooth.cv_role_target
  end

  -- 更新章节列表滚动
  local chapter_delta = smooth.chapter_target - smooth.chapter_current
  if math.abs(chapter_delta) > smooth.min_delta then
    smooth.chapter_current = smooth.chapter_current + chapter_delta * smooth.smoothing_factor
    app_state.chapter_scroll_pos = math.floor(smooth.chapter_current + 0.5)
    needs_redraw = true
  else
    smooth.chapter_current = smooth.chapter_target
    app_state.chapter_scroll_pos = smooth.chapter_target
  end

  -- 更新选择区域滚动
  local selection_delta = smooth.selection_target - smooth.selection_current
  if math.abs(selection_delta) > smooth.min_delta then
    smooth.selection_current = smooth.selection_current + selection_delta * smooth.smoothing_factor
    app_state.selection_scroll_pos = math.floor(smooth.selection_current + 0.5)
    needs_redraw = true
  else
    smooth.selection_current = smooth.selection_target
    app_state.selection_scroll_pos = smooth.selection_target
  end

  if needs_redraw then
    app_state.force_redraw = true
  end
end

-- 初始化平滑滚动状态
function event_module.init_smooth_scroll(app_state)
  local smooth = event_state.smooth_scroll
  smooth.content_target = app_state.sentence_scroll_pos or 0
  smooth.content_current = smooth.content_target
  smooth.cv_role_target = app_state.cv_role_scroll_pos or 0
  smooth.cv_role_current = smooth.cv_role_target
  smooth.chapter_target = app_state.chapter_scroll_pos or 0
  smooth.chapter_current = smooth.chapter_target
  smooth.selection_target = app_state.selection_scroll_pos or 0
  smooth.selection_current = smooth.selection_target
end

-- 更新按钮状态
function event_module.update_button_states(app_state)
  local mouse_x, mouse_y = gfx.mouse_x, gfx.mouse_y
  local mouse_cap = gfx.mouse_cap

  -- 获取所有按钮
  local ui = ui_module.get_ui_elements()
  if not ui then return end

  local buttons = {
    ui.play_button,
    ui.block_mark_button,
    ui.region_mark_button,
    ui.excel_button,
    ui.rate_minus_button,
    ui.rate_plus_button,
    ui.font_decrease_button,
    ui.font_increase_button
  }

  -- 更新按钮状态
  local button_state_changed = style_module.update_button_states(buttons, mouse_x, mouse_y, mouse_cap)
  style_module.update_button_animations()

  if button_state_changed then
    app_state.force_redraw = true
  end
end

-- 辅助函数：检查点是否在矩形内
function event_module.is_point_in_rect(x, y, rect)
  if not rect then return false end
  return x >= rect.x and x <= rect.x + rect.w and y >= rect.y and y <= rect.y + rect.h
end

-- 处理句子点击
function event_module.handle_sentence_click(app_state, mouse_x, mouse_y)
  local sentence_idx = event_module.get_sentence_index_at_position(app_state, mouse_x, mouse_y)
  if sentence_idx > 0 and sentence_idx <= #app_state.sentences then
    local sentence = app_state.sentences[sentence_idx]

    if event_state.is_ctrl_down then
      -- Ctrl+点击：多选
      if not app_state.selected_indices then
        app_state.selected_indices = {}
        app_state.selected_texts = {}
      end

      -- 检查是否已选中
      local already_selected = false
      for i, idx in ipairs(app_state.selected_indices) do
        if idx == sentence_idx then
          -- 取消选择
          table.remove(app_state.selected_indices, i)
          table.remove(app_state.selected_texts, i)
          already_selected = true
          break
        end
      end

      if not already_selected then
        -- 添加到选择
        table.insert(app_state.selected_indices, sentence_idx)
        table.insert(app_state.selected_texts, sentence)
      end
    else
      -- 普通点击：单选
      app_state.selected_text = sentence
      app_state.selected_texts = {sentence}
      app_state.selected_indices = {sentence_idx}
    end

    app_state.force_redraw = true
  end
end

-- 处理句子右键点击
function event_module.handle_sentence_right_click(app_state, mouse_x, mouse_y)
  local sentence_idx = event_module.get_sentence_index_at_position(app_state, mouse_x, mouse_y)
  if sentence_idx > 0 and sentence_idx <= #app_state.sentences then
    -- 右键点击可以实现特殊功能，比如快速标记等
    -- 这里暂时只是选择句子
    app_state.selected_text = app_state.sentences[sentence_idx]
    app_state.force_redraw = true
  end
end

-- 处理CV角色点击
function event_module.handle_cv_role_click(app_state, mouse_x, mouse_y)
  local cv_role = event_module.get_cv_role_at_position(app_state, mouse_x, mouse_y)
  if cv_role then
    app_state.selected_cv = cv_role.cv
    app_state.selected_role = cv_role.role
    app_state.force_redraw = true
  end
end

-- 处理章节点击
function event_module.handle_chapter_click(app_state, mouse_x, mouse_y)
  local chapter_idx = event_module.get_chapter_index_at_position(app_state, mouse_x, mouse_y)
  if chapter_idx > 0 and chapter_idx <= #app_state.chapters then
    local chapter = app_state.chapters[chapter_idx]
    if chapter then
      -- 跳转到章节对应的句子
      ui_module.scroll_to_sentence(app_state, chapter.sentence_idx)
    end
  end
end

-- 处理按钮点击
function event_module.handle_button_clicks(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()
  if not ui then return end

  -- 检查各种按钮点击
  if ui.play_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.play_button) then
    event_module.handle_play_button_click(app_state)
  elseif ui.block_mark_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.block_mark_button) then
    event_module.handle_block_mark_button_click(app_state)
  elseif ui.region_mark_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.region_mark_button) then
    event_module.handle_region_mark_button_click(app_state)
  elseif ui.excel_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.excel_button) then
    event_module.handle_excel_button_click(app_state)
  elseif ui.rate_minus_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.rate_minus_button) then
    event_module.handle_rate_minus_click(app_state)
  elseif ui.rate_plus_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.rate_plus_button) then
    event_module.handle_rate_plus_click(app_state)
  elseif ui.rate_reset_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.rate_reset_button) then
    event_module.handle_rate_reset_click(app_state)
  elseif ui.document_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.document_button) then
    event_module.handle_document_button_click(app_state)
  elseif ui.clipboard_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.clipboard_button) then
    event_module.handle_clipboard_button_click(app_state)
  elseif ui.open_csv_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.open_csv_button) then
    event_module.handle_open_csv_button_click(app_state)
  elseif ui.au_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.au_button) then
    event_module.handle_au_button_click(app_state)
  elseif ui.region_name_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.region_name_button) then
    event_module.handle_region_name_button_click(app_state)
  elseif ui.file_name_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.file_name_button) then
    event_module.handle_file_name_button_click(app_state)
  elseif ui.track_color_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.track_color_button) then
    event_module.handle_track_color_button_click(app_state)
  elseif ui.track_split_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.track_split_button) then
    event_module.handle_track_split_button_click(app_state)
  elseif ui.track_align_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.track_align_button) then
    event_module.handle_track_align_button_click(app_state)
  elseif ui.chapter_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.chapter_button) then
    event_module.handle_chapter_button_click(app_state)
  elseif ui.font_decrease_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.font_decrease_button) then
    event_module.handle_font_decrease_click(app_state)
  elseif ui.font_increase_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.font_increase_button) then
    event_module.handle_font_increase_click(app_state)
  elseif ui.cv_role_reverse_checkbox and event_module.is_point_in_rect(mouse_x, mouse_y, ui.cv_role_reverse_checkbox) then
    event_module.handle_cv_role_reverse_checkbox_click(app_state)
  end
end

-- 处理播放按钮点击
function event_module.handle_play_button_click(app_state)
  app_state.is_playing = utils_module.toggle_play()
  app_state.force_redraw = true
end

-- 处理块标按钮点击
function event_module.handle_block_mark_button_click(app_state)
  -- 检查条件
  local has_selected_item = r.GetSelectedMediaItem(0, 0) ~= nil
  local role_cv_selected = app_state.selected_cv ~= "" and app_state.selected_role ~= ""
  local has_process_suggestion = app_state.process_suggestion ~= ""

  if has_selected_item and role_cv_selected and has_process_suggestion then
    -- 执行块标操作
    button_module.mark_error(app_state)
  end
end

-- 处理区标按钮点击
function event_module.handle_region_mark_button_click(app_state)
  -- 检查条件
  local role_cv_selected = app_state.selected_cv ~= "" and app_state.selected_role ~= ""
  local has_process_suggestion = app_state.process_suggestion ~= ""

  if role_cv_selected and has_process_suggestion then
    -- 执行区标操作
    button_module.create_region_for_auto(app_state)
  end
end

-- 处理Excel按钮点击
function event_module.handle_excel_button_click(app_state)
  if #app_state.selected_texts > 0 or app_state.selected_text ~= "" then
    -- 导出到Excel
    button_module.export_to_excel(app_state)
  end
end

-- 处理速率减少按钮点击
function event_module.handle_rate_minus_click(app_state)
  app_state.current_playrate = utils_module.adjust_playrate(-0.1)
  app_state.force_redraw = true
end

-- 处理速率增加按钮点击
function event_module.handle_rate_plus_click(app_state)
  app_state.current_playrate = utils_module.adjust_playrate(0.1)
  app_state.force_redraw = true
end

-- 处理速率重置按钮点击
function event_module.handle_rate_reset_click(app_state)
  app_state.current_playrate = utils_module.reset_playrate()
  app_state.force_redraw = true
end

-- 处理读取文档按钮点击
function event_module.handle_document_button_click(app_state)
  local callbacks = {
    handle_text_content = function(text_content)
      app_state.clipboard_text = text_content
      event_module.parse_sentences(app_state)
      event_module.extract_cv_role_pairs(app_state)
      event_module.extract_chapters(app_state)  -- 添加章节提取
    end,
    set_clipboard_text = function(text)
      app_state.clipboard_text = text
    end,
    parse_sentences = function()
      event_module.parse_sentences(app_state)
    end,
    extract_cv_role_pairs = function()
      event_module.extract_cv_role_pairs(app_state)
    end
  }

  local result = button_module.handle_document_button(callbacks)
  app_state.force_redraw = true
  utils_module.info(result)
end

-- 处理读取剪贴板按钮点击
function event_module.handle_clipboard_button_click(app_state)
  local callbacks = {
    get_clipboard = function()
      app_state.clipboard_text = utils_module.get_clipboard()
    end,
    parse_sentences = function()
      event_module.parse_sentences(app_state)
    end,
    extract_cv_role_pairs = function()
      event_module.extract_cv_role_pairs(app_state)
    end
  }

  local result = button_module.handle_clipboard_button(callbacks)
  app_state.force_redraw = true
  utils_module.info(result)
end

-- 处理打开CSV按钮点击
function event_module.handle_open_csv_button_click(app_state)
  local result = button_module.open_csv_file()
  utils_module.info(result)
end

-- 处理AU按钮点击
function event_module.handle_au_button_click(app_state)
  local result = button_module.run_au_script()
  utils_module.info(result)
end

-- 处理区名按钮点击
function event_module.handle_region_name_button_click(app_state)
  local result = button_module.handle_region_name_button_click(app_state.chapters)
  app_state.force_redraw = true
  utils_module.info(result)
end

-- 处理文名按钮点击
function event_module.handle_file_name_button_click(app_state)
  local result = button_module.handle_file_name_button_click(app_state.chapters)
  app_state.force_redraw = true
  utils_module.info(result)
end

-- 处理轨色按钮点击
function event_module.handle_track_color_button_click(app_state)
  local result = button_module.handle_track_color_button_click(app_state.cv_role_pairs)
  app_state.force_redraw = true
  utils_module.info(result)
end

-- 处理分轨按钮点击
function event_module.handle_track_split_button_click(app_state)
  local result = button_module.handle_track_split_button_click(app_state.cv_role_pairs)
  app_state.force_redraw = true
  utils_module.info(result)
end

-- 处理对轨按钮点击
function event_module.handle_track_align_button_click(app_state)
  app_state.is_track_align_enabled = not app_state.is_track_align_enabled
  app_state.force_redraw = true
  local status = app_state.is_track_align_enabled and "开启" or "关闭"
  utils_module.info("对轨功能已" .. status)
end

-- 处理章节按钮点击
function event_module.handle_chapter_button_click(app_state)
  app_state.is_chapter_list_visible = not app_state.is_chapter_list_visible
  app_state.force_redraw = true
  local status = app_state.is_chapter_list_visible and "显示" or "隐藏"
  utils_module.info("章节列表已" .. status)
end

-- 处理CV角色交换位置复选框点击
function event_module.handle_cv_role_reverse_checkbox_click(app_state)
  app_state.is_cv_role_reversed = not app_state.is_cv_role_reversed
  app_state.force_redraw = true
  local status = app_state.is_cv_role_reversed and "已交换" or "未交换"
  utils_module.info("CV角色位置" .. status)
end

-- 处理字体缩小按钮点击
function event_module.handle_font_decrease_click(app_state)
  if style_module.font_size > 12 then
    style_module.font_size = style_module.font_size - 1
    app_state.force_redraw = true
    utils_module.info("字体大小: " .. style_module.font_size)
  end
end

-- 处理字体放大按钮点击
function event_module.handle_font_increase_click(app_state)
  if style_module.font_size < 32 then
    style_module.font_size = style_module.font_size + 1
    app_state.force_redraw = true
    utils_module.info("字体大小: " .. style_module.font_size)
  end
end

-- 处理滚动条拖拽
function event_module.handle_scrollbar_drag(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()
  if not ui then return end

  -- 检查各个滚动条
  local content_scrollbar = {
    x = ui.content_area.x + ui.content_area.w - 10,
    y = ui.content_area.y,
    w = 10,
    h = ui.content_area.h
  }

  if event_module.is_point_in_rect(mouse_x, mouse_y, content_scrollbar) then
    event_state.is_dragging_sentence_scrollbar = true
    -- 计算新的滚动位置
    local relative_y = (mouse_y - content_scrollbar.y) / content_scrollbar.h
    local line_height = style_module.content_font_size + 5
    local visible_items = math.floor((ui.content_area.h - 10) / line_height)
    local max_scroll = math.max(0, #app_state.sentences - visible_items)

    app_state.sentence_scroll_pos = math.max(0, math.min(max_scroll, math.floor(relative_y * max_scroll)))
    app_state.force_redraw = true
  end

  -- 类似地处理其他滚动条...
end

-- 搜索功能
function event_module.perform_search(app_state, keyword)
  if not keyword or keyword == "" then
    app_state.search_results = {}
    app_state.current_search_index = 0
    return
  end

  app_state.search_results = {}
  local keyword_lower = keyword:lower()

  -- 在所有句子中搜索
  for i, sentence in ipairs(app_state.sentences) do
    if sentence and sentence ~= "__SKIP_THIS_SENTENCE__" then
      -- 清除标签后搜索
      local clean_sentence = ui_module.clean_text_tags(sentence):lower()
      if clean_sentence:find(keyword_lower, 1, true) then
        table.insert(app_state.search_results, i)
      end
    end
  end

  -- 重置当前搜索索引
  app_state.current_search_index = #app_state.search_results > 0 and 1 or 0
  app_state.force_redraw = true
end

-- 跳转到下一个搜索结果
function event_module.goto_next_search_result(app_state)
  if #app_state.search_results == 0 then return end

  app_state.current_search_index = app_state.current_search_index + 1
  if app_state.current_search_index > #app_state.search_results then
    app_state.current_search_index = 1
  end

  -- 滚动到当前结果
  local target_sentence = app_state.search_results[app_state.current_search_index]
  ui_module.scroll_to_sentence(app_state, target_sentence)
end

-- 跳转到上一个搜索结果
function event_module.goto_prev_search_result(app_state)
  if #app_state.search_results == 0 then return end

  app_state.current_search_index = app_state.current_search_index - 1
  if app_state.current_search_index < 1 then
    app_state.current_search_index = #app_state.search_results
  end

  -- 滚动到当前结果
  local target_sentence = app_state.search_results[app_state.current_search_index]
  ui_module.scroll_to_sentence(app_state, target_sentence)
end

-- 获取鼠标位置对应的句子索引
function event_module.get_sentence_index_at_position(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()
  if not ui or not ui.content_area then return -1 end

  local content_area = ui.content_area
  if not event_module.is_point_in_rect(mouse_x, mouse_y, content_area) then
    return -1
  end

  local line_height = style_module.content_font_size + 5
  local relative_y = mouse_y - content_area.y - 5
  local line_index = math.floor(relative_y / line_height)

  return app_state.sentence_scroll_pos + line_index + 1
end

-- 获取鼠标位置对应的CV角色（按分类处理）
function event_module.get_cv_role_at_position(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()
  if not ui or not ui.cv_role_list then return nil end

  local cv_role_list = ui.cv_role_list
  if not event_module.is_point_in_rect(mouse_x, mouse_y, cv_role_list) then
    return nil
  end

  local cv_role_pairs = app_state.cv_role_pairs
  if #cv_role_pairs == 0 then return nil end

  -- 使用基于字体大小的动态行高
  local cv_role_line_height = math.max(20, style_module.font_size + 2)

  -- 按CV分类整理角色列表
  local cv_categories, cv_order = ui_module.get_cv_role_categories(cv_role_pairs, app_state.is_cv_role_reversed)

  -- 计算所有分类项的总数（包括CV分类标题）
  local total_items = 0
  for _, roles in pairs(cv_categories) do
    total_items = total_items + 1 + #roles
  end

  -- 计算可见项目数
  local visible_items = math.floor((cv_role_list.h - 10) / cv_role_line_height)

  -- 计算点击位置对应的项目索引
  local relative_y = mouse_y - cv_role_list.y - 5
  local clicked_line = math.floor(relative_y / cv_role_line_height)
  local clicked_item = app_state.cv_role_scroll_pos + clicked_line + 1

  -- 遍历分类项，找到对应的CV和角色
  local current_item = 1
  for _, cv_name in ipairs(cv_order) do
    local roles = cv_categories[cv_name]

    -- 检查是否点击了CV分类标题
    if current_item == clicked_item then
      -- 点击了CV分类标题，不返回具体角色
      return nil
    end
    current_item = current_item + 1

    -- 检查是否点击了该CV下的某个角色
    for _, role_info in ipairs(roles) do
      if current_item == clicked_item then
        -- 点击了具体角色
        return {cv = cv_name, role = role_info.role, is_cv = false}
      end
      current_item = current_item + 1
    end
  end

  return nil
end

-- 获取鼠标位置对应的章节索引
function event_module.get_chapter_index_at_position(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()
  if not ui or not ui.chapter_list then return -1 end

  local chapter_list = ui.chapter_list
  if not event_module.is_point_in_rect(mouse_x, mouse_y, chapter_list) then
    return -1
  end

  local line_height = style_module.font_size + 5
  local relative_y = mouse_y - chapter_list.y - 30
  local line_index = math.floor(relative_y / line_height)

  return app_state.chapter_scroll_pos + line_index + 1
end

-- 解析句子
function event_module.parse_sentences(app_state)
  if not app_state.clipboard_text or app_state.clipboard_text == "" then
    app_state.sentences = {}
    return
  end

  -- 使用text_utils解析句子
  if text_utils and text_utils.parse_sentences then
    app_state.sentences = text_utils.parse_sentences(app_state.clipboard_text)
  else
    -- 改进的句子分割逻辑 - 确保完整性
    app_state.sentences = {}
    local content = app_state.clipboard_text

    -- 首先按行分割，保持所有非空行
    for line in content:gmatch("[^\r\n]+") do
      local trimmed_line = trim(line)
      if trimmed_line and trimmed_line ~= "" then
        -- 检查是否是特殊格式（章节标记、CV对话等）
        local is_special_format = trimmed_line:find("%[CHAPTER%]") or
                                 trimmed_line:find("【.-】") or
                                 trimmed_line:find("第%d+章")

        if is_special_format then
          -- 特殊格式行直接作为一个句子
          table.insert(app_state.sentences, trimmed_line)
        elseif trimmed_line:find("[。！？]") then
          -- 包含句子结束符的行，进一步分割
          local temp_sentences = {}
          for sentence in trimmed_line:gmatch("[^。！？]+[。！？]?") do
            local trimmed_sentence = trim(sentence)
            if trimmed_sentence and trimmed_sentence ~= "" then
              table.insert(temp_sentences, trimmed_sentence)
            end
          end

          -- 如果分割后有内容，添加到句子列表
          if #temp_sentences > 0 then
            for _, s in ipairs(temp_sentences) do
              table.insert(app_state.sentences, s)
            end
          else
            -- 如果分割失败，保留原行
            table.insert(app_state.sentences, trimmed_line)
          end
        else
          -- 不包含句子结束符的行，直接作为一个句子
          table.insert(app_state.sentences, trimmed_line)
        end
      end
    end

    -- 确保至少有一些内容
    if #app_state.sentences == 0 and content and content ~= "" then
      -- 如果按行分割失败，尝试简单分割
      table.insert(app_state.sentences, content)
    end
  end

  app_state.force_redraw = true
end

-- 提取CV角色对
function event_module.extract_cv_role_pairs(app_state)
  if not app_state.sentences or #app_state.sentences == 0 then
    app_state.cv_role_pairs = {}
    return
  end

  -- 使用text_utils提取CV角色对
  if text_utils and text_utils.extract_cv_role_pairs then
    app_state.cv_role_pairs = text_utils.extract_cv_role_pairs(app_state.sentences)
  else
    -- 改进的CV角色对提取
    app_state.cv_role_pairs = {}
    local cv_pairs = {}  -- 用于去重

    for _, sentence in ipairs(app_state.sentences) do
      if sentence then
        -- 查找【角色-CV】格式
        for cv_role in sentence:gmatch("【(.-)】") do
          if cv_role:find("-") or cv_role:find("－") then
            local cv, role = cv_role:match("(.-)[-－](.+)")
            if cv and role then
              local trimmed_cv = trim(cv)
              local trimmed_role = trim(role)
              local key = trimmed_cv .. "-" .. trimmed_role
              if not cv_pairs[key] then
                cv_pairs[key] = true
                table.insert(app_state.cv_role_pairs, {cv = trimmed_cv, role = trimmed_role})
              end
            end
          end
        end

        -- 也查找简单的角色：CV格式
        local role, cv = sentence:match("([^：]+)：([^，。！？]+)")
        if role and cv then
          local trimmed_role = trim(role)
          local trimmed_cv = trim(cv)
          local key = trimmed_cv .. "-" .. trimmed_role
          if not cv_pairs[key] then
            cv_pairs[key] = true
            table.insert(app_state.cv_role_pairs, {cv = trimmed_cv, role = trimmed_role})
          end
        end
      end
    end
  end

  app_state.force_redraw = true
end

-- 提取章节信息
function event_module.extract_chapters(app_state)
  if not app_state.sentences or #app_state.sentences == 0 then
    app_state.chapters = {}
    return
  end

  app_state.chapters = {}

  -- 遍历所有句子，查找章节标记
  for i, sentence in ipairs(app_state.sentences) do
    if sentence then
      -- 查找章节标记：[CHAPTER]章节标题[/CHAPTER]
      local chapter_title = sentence:match("%[CHAPTER%](.-)%[/CHAPTER%]")
      if chapter_title then
        table.insert(app_state.chapters, {
          title = trim(chapter_title),
          sentence_idx = i
        })
      end

      -- 也查找简单的章节格式：第X章
      if not chapter_title then
        local simple_chapter = sentence:match("第%d+章[^。！？]*")
        if simple_chapter then
          table.insert(app_state.chapters, {
            title = trim(simple_chapter),
            sentence_idx = i
          })
        end
      end
    end
  end

  app_state.force_redraw = true
end

-- 字符串trim函数
if not string.trim then
  function string.trim(s)
    return s:match("^%s*(.-)%s*$")
  end
end

return event_module