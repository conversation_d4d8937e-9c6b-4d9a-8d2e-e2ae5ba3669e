# REAPER 音频打标工具

## 项目概述

这是一个为REAPER数字音频工作站(DAW)开发的脚本工具集，主要用于音频后期制作工作流程中的打标、标记和文本处理工作。该工具可以帮助音频工程师、配音导演和后期制作人员更高效地处理音频项目，特别适用于配音、广播和影视后期制作场景。

本工具专为中文配音和音频后期制作环境设计，提供了完整的文本解析、角色管理和标记系统，能够显著提高音频制作效率。

## 主要功能

- **音频标记管理**：在音频项目中添加、编辑和管理标记点
- **播放控制**：调整播放速率、控制播放/暂停等功能
- **文本处理**：解析脚本文本，提取句子和角色信息
- **CV（配音演员）和角色管理**：管理配音演员和角色的对应关系
- **章节管理**：对音频内容进行章节划分和管理
- **Excel导入/导出**：支持与Excel/CSV表格交互，导入导出数据，便于项目管理和数据整理
- **Word文档处理**：支持从Word文档中提取文本和格式信息，保留颜色和删除线样式，自动识别角色和配音演员信息
- **数据持久化**：保存和加载工作状态和设置
- **缓存系统**：优化性能，减少重复计算

## 模块结构

项目采用模块化设计，经过优化合并后主要包含以下模块：

- **mark.lua**：主脚本入口，整合所有功能模块，提供用户界面和交互逻辑
- **module_loader.lua**：模块加载器，管理模块依赖和加载顺序，解决循环依赖问题，支持模块映射
- **utils_module.lua**：通用工具函数模块，提供以下功能：
  - 日志、时间格式化、字符串处理等基础功能支持
  - 播放控制功能（原 playback_control.lua）：管理音频播放、暂停和速率调整
  - 缓存管理功能（原 cache_module.lua）：优化性能，减少重复计算
- **text_utils.lua**：文本处理模块，处理文本内容和解析，提取句子和角色信息
- **button_module.lua**：按钮UI模块，包含以下功能：
  - 界面交互和事件响应
  - 打标功能（原 mark_function.lua）：实现标记添加和管理，支持项目级和音频块级标记
- **chapter_module.lua**：章节管理模块，处理音频内容的章节划分
- **excel_module.lua**：Excel数据处理模块，包含以下功能：
  - CSV格式导入导出，清理标签和格式化数据
  - 数据持久化功能（原 data_persist_module.lua）：保存和加载工作状态
- **style_module.lua**：UI样式管理模块，包含以下功能：
  - 控制界面外观和字体设置
  - 按钮辅助功能（原 button_helpers.lua）：提供额外的按钮功能
- **word_module.lua**：Word文档处理模块，从docx文件中提取文本并保留颜色和删除线样式，识别角色-配音演员对应关系

## 安装方法

1. 确保已安装REAPER 7.35或更高版本
2. 将整个`mark6`文件夹复制到REAPER的Scripts目录下
   - Windows通常位于：`%APPDATA%\REAPER\Scripts\`
   - macOS通常位于：`~/Library/Application Support/REAPER/Scripts/`
3. 在REAPER中，选择`Actions > Show action list`
4. 点击`New action...`按钮，选择`Load ReaScript...`
5. 导航到Scripts/mark6目录，选择`mark.lua`文件
6. 可选：为脚本分配快捷键以便快速访问

## 使用方法

1. 在REAPER中打开或创建一个项目
2. 运行mark.lua脚本（通过动作列表或已分配的快捷键）
3. 使用界面上的按钮和控件进行操作：
   - 导入文本（从剪贴板、文本文件或Word文档）
   - 解析句子和角色信息（自动识别角色-配音演员对应关系）
   - 在播放过程中添加标记（支持项目级和音频块级标记）
   - 调整播放速率（支持1.0-2.0倍速，保持音高不变）
   - 管理章节和标记（添加、编辑、删除章节）
   - 导出数据到Excel/CSV（支持标记数据和角色信息导出）
   - 使用缓存系统提高性能（自动清理过期缓存）

## 数据存储

工具会在`data`目录下保存以下数据：
- `save_data.txt`：保存的工作状态和设置
- `debug`目录：包含调试信息和日志

## 开发信息

- 语言：Lua
- 依赖：REAPER API
- 设计模式：模块化设计，解决了循环依赖问题
- 模块映射系统：支持模块合并和重定向，保持向后兼容性
- 缓存系统：优化性能，减少重复计算
- 错误处理：完善的错误捕获和日志系统
- 模块加载顺序：精心设计的依赖关系管理
- 性能优化：减少重复计算，优化UI渲染
- 代码优化：通过模块合并减少文件数量，简化依赖关系

## 注意事项

- 该工具主要针对中文配音和音频后期制作场景优化
- 界面使用中文，支持中文文本处理
- 建议使用微软雅黑等支持中文显示的字体

## 更新历史

- 当前版本：1.2.0 (计划中)
- 初始发布 (1.0.0)：包含基本的打标和文本处理功能
- 功能更新 (1.0.x)：添加了Word文档处理、缓存系统和性能优化
- 代码优化 (1.1.0)：
  - 模块合并：减少文件数量，简化依赖关系
  - 将 button_helpers.lua 合并到 style_module.lua
  - 将 mark_function.lua 合并到 button_module.lua
  - 将 data_persist_module.lua 合并到 excel_module.lua
  - 将 playback_control.lua 和 cache_module.lua 合并到 utils_module.lua
  - 添加模块映射系统，保持向后兼容性
- 深度优化 (1.2.0)：
  - 主脚本重构：将UI和事件处理逻辑分离
  - 模块进一步整合：合并小模块，优化依赖关系
  - 性能优化：减少代码重复，优化缓存和字符串处理
  - 代码清理：移除死代码，统一错误处理机制