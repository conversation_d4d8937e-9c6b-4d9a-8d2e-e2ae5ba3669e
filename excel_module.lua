-- Excel数据导出模块 - REAPER脚本
-- 用于处理Excel/CSV数据导出相关功能

local r = reaper

-- 模块表
local excel_module = {}

-- 导入工具模块，获取通用函数
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"
local function safe_dofile(file_path)
  local success, result = pcall(dofile, file_path)
  if not success then return {} end
  return result
end
local utils_module = safe_dofile(script_dir .. "utils_module.lua")

-- 导入text_utils模块用于清理标签
local text_utils_module = safe_dofile(script_dir .. "text_utils.lua")

-- 使用utils_module中的format_time
local format_time = utils_module.format_time

-- 配置变量
local last_excel_click_time = 0  -- 上次点击写入EXCEL按钮的时间
local excel_click_cooldown = 0.3  -- 写入EXCEL按钮点击冷却时间(秒)

-- 获取上次点击时间
function excel_module.get_last_click_time()
  return last_excel_click_time
end

-- 设置上次点击时间
function excel_module.set_last_click_time(time)
  last_excel_click_time = time
end

-- 获取点击冷却时间
function excel_module.get_click_cooldown()
  return excel_click_cooldown
end

-- 清理CSV文本中的标签
function excel_module.clean_csv_text(text)
  -- 如果text_utils_module有clean_cv_role_tags函数，则使用它
  if text_utils_module and text_utils_module.clean_cv_role_tags then
    return text_utils_module.clean_cv_role_tags(text)
  end

  -- 否则使用内置的简单清理功能
  if not text or text == "" then
    return text
  end

  -- 清理各种标签
  local cleaned_text = text

  -- 清理背景色标签 [bg#RRGGBB] 和 [bg#]
  cleaned_text = cleaned_text:gsub("%[bg#[0-9A-Fa-f]+%]", "")
  cleaned_text = cleaned_text:gsub("%[bg#%]", "")

  -- 清理前景色标签 [#RRGGBB] 和 [#]
  cleaned_text = cleaned_text:gsub("%[#[0-9A-Fa-f]+%]", "")
  cleaned_text = cleaned_text:gsub("%[#%]", "")

  -- 清理删除线标签 [x] 和 [/x]
  cleaned_text = cleaned_text:gsub("%[x%]", "")
  cleaned_text = cleaned_text:gsub("%[/x%]", "")

  return cleaned_text
end

-- 写入数据到文件(CSV)
function excel_module.write_to_file(params)
  local status, err = pcall(function()
    local episode_number = params.episode_number
    local selected_role = params.selected_role
    local selected_cv = params.selected_cv
    local marked_relative_time = params.marked_relative_time
    local selected_text = params.selected_text
    local error_note = params.error_note
    local correct_note = params.correct_note
    local process_suggestion = params.process_suggestion

    if selected_cv == "" or selected_role == "" or error_note == "" then
      return "请先选择CV、角色并填写错误描述"
    end

    -- 获取当前选中的项目
    local selected_item = r.GetSelectedMediaItem(0, 0)
    local item_pos = 0

    -- 使用标记的内部时间而不是项目时间
    local formatted_time = ""

    if marked_relative_time and marked_relative_time ~= "" then
      -- 如果有标记的内部时间，使用它
      formatted_time = marked_relative_time
    else
      -- 如果没有标记的内部时间，使用当前项目时间（兼容旧行为）
      if selected_item then
        -- 获取选中项目的位置
        item_pos = r.GetMediaItemInfo_Value(selected_item, "D_POSITION")
      else
        -- 如果没有选中项目，使用当前播放位置
        item_pos = r.GetPlayPosition()
      end
      formatted_time = format_time(item_pos)
    end

    -- 清理文本中的标签，确保CSV中不含格式标签
    selected_text = excel_module.clean_csv_text(selected_text)

    -- 构建CSV行数据（确保中文字符正确编码）
    -- 按照"集数，角色，CV，问题时间，文本内容，错误描述，正确表达，处理建议"的顺序

    -- 转义CSV字段中的双引号（在CSV中，双引号需要用两个双引号来转义）
    local function escape_csv_field(field)
      if not field then return "" end
      -- 将字段中的双引号替换为两个双引号
      return (tostring(field):gsub('"', '""'))
    end

    -- 处理角色名，去掉开头的-符号（仅用于CSV写入）
    local clean_role = selected_role
    if clean_role and clean_role:sub(1, 1) == "-" then
      clean_role = clean_role:sub(2)  -- 去掉开头的-符号
    end
    
    -- 转义所有字段
    local escaped_episode = escape_csv_field(episode_number)
    local escaped_role = escape_csv_field(clean_role)
    local escaped_cv = escape_csv_field(selected_cv)
    local escaped_time = escape_csv_field(formatted_time)
    local escaped_text = escape_csv_field(selected_text)
    local escaped_error = escape_csv_field(error_note)
    local escaped_correct = escape_csv_field(correct_note)
    local escaped_suggestion = escape_csv_field(process_suggestion)

    -- 构建CSV行，使用转义后的字段
    local csv_line = string.format("\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"",
                                  escaped_episode,
                                  escaped_role,
                                  escaped_cv,
                                  escaped_time,
                                  escaped_text,
                                  escaped_error,
                                  escaped_correct,
                                  escaped_suggestion)

    -- 获取脚本目录
    local script_path = r.GetResourcePath() .. "/Scripts/"
    local file_path = script_path .. "审听报告.csv"

    -- 将函数拆分为更小的函数，提高可读性和可维护性
    local function get_file_size(path)
      local file = io.open(path, "r")
      if not file then return 0 end
      local size = file:seek("end")
      file:close()
      return size
    end

    local function append_to_csv(path, line)
      -- 检查文件是否存在和内容
      local file_exists = r.file_exists(path)
      local file_size = get_file_size(path)

      -- 如果文件不存在或为空，创建新文件
      if not file_exists or file_size <= 3 then
        local f = io.open(path, "wb")  -- 使用二进制模式确保UTF-8正确写入
        if not f then
          return false, "无法创建新CSV文件"
        end

        -- 写入UTF-8 BOM头和标题行
        f:write("\239\187\191")  -- UTF-8 BOM
        f:write("\"集数\",\"角色\",\"CV\",\"问题时间\",\"文本内容\",\"错误描述\",\"正确表达\",\"处理建议\"\n")
        f:write(line .. "\n")
        f:close()
        return true
      else
        -- 文件存在且非空，尝试追加内容
        -- 先检查文件是否可以打开
        local test = io.open(path, "r+b")
        if not test then
          r.MB("CSV文件被占用，请关闭审听报告.csv后再试。", "写入失败", 0)
          return false, "CSV文件被占用，请关闭审听报告.csv后再试"
        end
        test:close()

        -- 检查文件是否包含标题行
        local has_header = false
        local f = io.open(path, "rb")
        if f then
          local header = f:read("*line")
          f:close()
          has_header = header and (header:find("集数") or header:find("CV"))
        end

        if not has_header then
          -- 如果没有标题行，可能不是有效的CSV文件或格式不对
          r.ShowConsoleMsg("CSV文件没有有效的标题行，重新创建文件...\n")
          local f = io.open(path, "wb")
          if not f then
            return false, "无法重新创建CSV文件"
          end
          f:write("\239\187\191")  -- UTF-8 BOM
          f:write("\"集数\",\"角色\",\"CV\",\"问题时间\",\"文本内容\",\"错误描述\",\"正确表达\",\"处理建议\"\n")
          f:write(line .. "\n")
          f:close()
        else
          -- 有标题行，直接追加内容
          local f = io.open(path, "ab")  -- 使用追加模式，确保不会覆盖原内容
          if not f then
            return false, "无法追加到CSV文件"
          end
          f:write(line .. "\n")
          f:close()
        end
        return true
      end
    end

    -- 尝试执行CSV写入操作
    local success, msg = append_to_csv(file_path, csv_line)

    -- 简化的验证过程 - 只验证文件大小是否增加，而不是检查具体内容
    if success then
      -- 再次获取文件大小，检查是否增加
      local new_size = get_file_size(file_path)

      if new_size > 0 then
        return "" -- 成功写入
      else
        return "写入验证失败：文件大小未增加，请检查文件权限"
      end
    else
      return msg or "写入CSV文件失败"
    end
  end)

  if not status then
    r.ShowConsoleMsg("写入CSV失败: " .. tostring(err) .. "\n")
    return "写入CSV失败: " .. tostring(err)
  end

  return err or ""  -- 返回错误信息（如果有）
end

-- 处理Excel按钮点击
function excel_module.handle_excel_button_click(params)
  -- 获取当前时间
  local current_excel_click_time = r.time_precise()
  local result_message = ""

  -- 检查是否超过冷却时间
  if current_excel_click_time - last_excel_click_time >= excel_click_cooldown then
    result_message = excel_module.write_to_file(params)
    -- 更新上次点击时间
    last_excel_click_time = current_excel_click_time

    if result_message == "" then
      result_message = "已写入报告文件 (点击时间: " .. current_excel_click_time .. ")"
    end
  else
    result_message = "写入操作太频繁，请稍后再试 (冷却中: " .. excel_click_cooldown - (current_excel_click_time - last_excel_click_time) .. "秒)"
  end

  return result_message
end

-- =====================================================
-- 数据持久化功能（从data_persist_module.lua合并）
-- =====================================================

-- 默认的保存文件名
local DEFAULT_SAVE_FILENAME = "save_data.txt"

-- 获取数据目录路径
-- @return string 数据目录的完整路径
function excel_module.get_data_dir()
  local data_dir = script_dir .. "data"

  -- 确保数据目录存在
  if not utils_module.file_exists(data_dir) then
    local success, message = utils_module.create_directory(data_dir)
    if not success then
      r.ShowConsoleMsg("创建数据目录失败: " .. message .. "\n")
    end
  end

  return data_dir
end

-- 获取保存文件的完整路径
-- @param filename 可选的文件名，默认为save_data.txt
-- @return string 保存文件的完整路径
function excel_module.get_save_file_path(filename)
  filename = filename or DEFAULT_SAVE_FILENAME
  return excel_module.get_data_dir() .. "/" .. filename
end

-- 将数据序列化为简单文本格式
-- @param sentences 句子列表
-- @param cv_role_pairs CV角色对列表
-- @param scroll_positions 滚动位置信息，包含sentence_scroll_pos和content_scroll_y等
-- @return string 序列化后的文本
local function serialize_data(sentences, cv_role_pairs, scroll_positions)
  local result = ""

  -- 添加句子列表
  result = result .. "===SENTENCES===\n"
  for i, sentence in ipairs(sentences) do
    -- 对句子中的换行符进行编码，以便能正确保存和还原
    local encoded_sentence = sentence:gsub("\n", "\\n"):gsub("\r", "\\r")
    result = result .. encoded_sentence .. "\n"
  end

  -- 添加CV角色对
  result = result .. "===CV_ROLE_PAIRS===\n"
  for i, pair in ipairs(cv_role_pairs) do
    local cv = pair.cv or ""
    local role = pair.role or ""
    -- 编码CV和角色名，避免特殊字符问题
    cv = cv:gsub("\n", "\\n"):gsub("\r", "\\r"):gsub("|", "\\|")
    role = role:gsub("\n", "\\n"):gsub("\r", "\\r"):gsub("|", "\\|")
    result = result .. cv .. "|" .. role .. "\n"
  end

  -- 添加滚动位置信息
  result = result .. "===SCROLL_POSITIONS===\n"
  if scroll_positions then
    -- 保存句子滚动位置
    result = result .. "sentence_scroll_pos=" .. (scroll_positions.sentence_scroll_pos or 0) .. "\n"
    -- 保存内容区域垂直滚动位置
    result = result .. "content_scroll_y=" .. (scroll_positions.content_scroll_y or 0) .. "\n"
    -- 保存章节滚动位置
    result = result .. "chapter_scroll_pos=" .. (scroll_positions.chapter_scroll_pos or 0) .. "\n"
    -- 保存CV角色列表滚动位置
    result = result .. "cv_role_scroll_pos=" .. (scroll_positions.cv_role_scroll_pos or 0) .. "\n"
  end

  return result
end

-- 从简单文本格式反序列化数据
-- @param data_str 序列化的文本数据
-- @return table 包含sentences、cv_role_pairs和scroll_positions的表
local function deserialize_data(data_str)
  local result = {
    sentences = {},
    cv_role_pairs = {},
    scroll_positions = {
      sentence_scroll_pos = 0,
      content_scroll_y = 0,
      chapter_scroll_pos = 0,
      cv_role_scroll_pos = 0
    }
  }

  local current_section = nil
  for line in data_str:gmatch("[^\n]+") do
    if line == "===SENTENCES===" then
      current_section = "sentences"
    elseif line == "===CV_ROLE_PAIRS===" then
      current_section = "cv_role_pairs"
    elseif line == "===SCROLL_POSITIONS===" then
      current_section = "scroll_positions"
    elseif current_section == "sentences" then
      -- 解码句子中的转义字符
      local decoded_line = line:gsub("\\n", "\n"):gsub("\\r", "\r")
      table.insert(result.sentences, decoded_line)
    elseif current_section == "cv_role_pairs" then
      -- 解析CV和角色名
      local cv, role = line:match("^(.-)|(.*)")
      if cv and role then
        -- 解码CV和角色名中的转义字符和HTML实体
        cv = cv:gsub("\\n", "\n"):gsub("\\r", "\r"):gsub("\\|", "|"):gsub("&quot;", '"')
        role = role:gsub("\\n", "\n"):gsub("\\r", "\r"):gsub("\\|", "|"):gsub("&quot;", '"')
        table.insert(result.cv_role_pairs, {cv = cv, role = role})
      end
    elseif current_section == "scroll_positions" then
      -- 解析滚动位置信息
      local key, value = line:match("^([%w_]+)=(%d+%.?%d*)")
      if key and value then
        -- 转换为数字
        local num_value = tonumber(value)
        if num_value then
          result.scroll_positions[key] = num_value
        end
      end
    end
  end

  return result
end

-- 保存画本内容（简化版本，直接保存文本）
-- @param sentences 句子列表
-- @param cv_role_pairs CV角色对列表
-- @param scroll_positions 滚动位置信息（可选）
-- @param filename 可选的文件名，默认为save_data.txt
-- @return boolean 是否成功保存
-- @return string 成功或错误消息
function excel_module.save_storyboard(sentences, cv_role_pairs, scroll_positions, filename)
  if not sentences or type(sentences) ~= "table" then
    return false, "无效的句子列表"
  end

  -- 确保scroll_positions是table
  local scroll_pos = scroll_positions or {}

  -- 序列化数据为简单文本格式
  local text_data = serialize_data(sentences, cv_role_pairs or {}, scroll_pos)

  -- 获取保存文件路径
  local file_path = excel_module.get_save_file_path(filename)

  -- 保存数据
  local success, message = utils_module.save_data_to_file(file_path, text_data)

  return success, message
end

-- 加载画本内容（简化版本，直接加载文本）
-- @param filename 可选的文件名，默认为save_data.txt
-- @return boolean 是否成功加载
-- @return table 加载的数据或错误消息字符串
function excel_module.load_storyboard(filename)
  -- 获取保存文件路径
  local file_path = excel_module.get_save_file_path(filename)

  -- 检查文件是否存在
  if not utils_module.file_exists(file_path) then
    return false, "保存文件不存在"
  end

  -- 加载数据
  local success, data = utils_module.load_data_from_file(file_path)
  if not success then
    return false, data -- 返回错误消息
  end

  -- 解析文本数据
  local loaded_data = deserialize_data(data)

  return true, loaded_data
end

-- 检查是否有保存的画本内容
-- @param filename 可选的文件名，默认为save_data.txt
-- @return boolean 是否存在保存的画本内容
function excel_module.has_saved_storyboard(filename)
  local file_path = excel_module.get_save_file_path(filename)
  return utils_module.file_exists(file_path)
end

return excel_module