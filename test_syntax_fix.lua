-- 语法修复验证脚本
-- 验证event_module.lua的语法错误是否已修复

-- 导入必要的库
local r = reaper

-- 获取脚本路径
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"

-- 安全加载函数
local function safe_load_module(file_path)
  local success, result = pcall(dofile, file_path)
  if not success then
    r.ShowConsoleMsg("模块加载失败: " .. file_path .. " - " .. tostring(result) .. "\n")
    return nil
  end
  return result
end

-- 主函数
local function main()
  r.ShowConsoleMsg("=== 语法修复验证测试 ===\n")
  
  -- 测试加载event_module
  r.ShowConsoleMsg("测试加载event_module.lua...\n")
  local event_module = safe_load_module(script_dir .. "event_module.lua")
  
  if not event_module then
    r.ShowConsoleMsg("✗ event_module加载失败\n")
    return
  end
  
  r.ShowConsoleMsg("✓ event_module加载成功\n")
  
  -- 测试加载其他模块
  r.ShowConsoleMsg("测试加载其他模块...\n")
  
  local utils_module = safe_load_module(script_dir .. "utils_module.lua")
  local style_module = safe_load_module(script_dir .. "style_module.lua")
  local text_utils = safe_load_module(script_dir .. "text_utils.lua")
  local button_module = safe_load_module(script_dir .. "button_module.lua")
  local ui_module = safe_load_module(script_dir .. "ui_module.lua")
  
  if not utils_module then
    r.ShowConsoleMsg("✗ utils_module加载失败\n")
    return
  end
  
  if not style_module then
    r.ShowConsoleMsg("✗ style_module加载失败\n")
    return
  end
  
  if not text_utils then
    r.ShowConsoleMsg("✗ text_utils加载失败\n")
    return
  end
  
  if not button_module then
    r.ShowConsoleMsg("✗ button_module加载失败\n")
    return
  end
  
  if not ui_module then
    r.ShowConsoleMsg("✗ ui_module加载失败\n")
    return
  end
  
  r.ShowConsoleMsg("✓ 所有模块加载成功\n")
  
  -- 测试模块初始化
  r.ShowConsoleMsg("测试模块初始化...\n")
  
  -- 初始化style_module
  local style_init_success, style_init_error = pcall(function()
    style_module.init({utils_module = utils_module})
  end)
  
  if not style_init_success then
    r.ShowConsoleMsg("✗ style_module初始化失败: " .. tostring(style_init_error) .. "\n")
    return
  end
  
  r.ShowConsoleMsg("✓ style_module初始化成功\n")
  
  -- 初始化其他模块
  local deps = {
    style_module = style_module,
    text_utils = text_utils,
    button_module = button_module,
    utils_module = utils_module,
    gfx = gfx,
    r = r
  }
  
  local ui_init_success, ui_init_error = pcall(function()
    ui_module.init(deps)
  end)
  
  if not ui_init_success then
    r.ShowConsoleMsg("✗ ui_module初始化失败: " .. tostring(ui_init_error) .. "\n")
    return
  end
  
  r.ShowConsoleMsg("✓ ui_module初始化成功\n")
  
  deps.ui_module = ui_module
  
  local event_init_success, event_init_error = pcall(function()
    event_module.init(deps)
  end)
  
  if not event_init_success then
    r.ShowConsoleMsg("✗ event_module初始化失败: " .. tostring(event_init_error) .. "\n")
    return
  end
  
  r.ShowConsoleMsg("✓ event_module初始化成功\n")
  
  -- 测试应用状态创建
  r.ShowConsoleMsg("测试应用状态创建...\n")
  
  local app_state_success, app_state_error = pcall(function()
    return utils_module.app_state.create()
  end)
  
  if not app_state_success then
    r.ShowConsoleMsg("✗ 应用状态创建失败: " .. tostring(app_state_error) .. "\n")
    return
  end
  
  local app_state = app_state_error  -- pcall返回的实际是结果
  r.ShowConsoleMsg("✓ 应用状态创建成功\n")
  
  -- 测试新的解析函数
  r.ShowConsoleMsg("测试新的解析函数...\n")
  
  -- 设置测试内容
  app_state.clipboard_text = [[
[CHAPTER]第251章 孕妇打人了！（一）[/CHAPTER]
　　张文丽觉得不可能，但这报纸上的故事，笔名是鱼，口吻也是以女方的视角写的，让人不多想都不行！
【虞梨-黎暖薇】：这是一个测试对话。
【张三-角色A】：另一个测试对话。
第252章 测试章节
这是另一个测试句子。
]]
  
  -- 测试句子解析
  local parse_success, parse_error = pcall(function()
    event_module.parse_sentences(app_state)
  end)
  
  if not parse_success then
    r.ShowConsoleMsg("✗ 句子解析失败: " .. tostring(parse_error) .. "\n")
    return
  end
  
  r.ShowConsoleMsg("✓ 句子解析成功，解析了 " .. #app_state.sentences .. " 个句子\n")
  
  -- 测试CV角色对提取
  local cv_extract_success, cv_extract_error = pcall(function()
    event_module.extract_cv_role_pairs(app_state)
  end)
  
  if not cv_extract_success then
    r.ShowConsoleMsg("✗ CV角色对提取失败: " .. tostring(cv_extract_error) .. "\n")
    return
  end
  
  r.ShowConsoleMsg("✓ CV角色对提取成功，提取了 " .. #app_state.cv_role_pairs .. " 个角色对\n")
  
  -- 测试章节提取
  local chapter_extract_success, chapter_extract_error = pcall(function()
    event_module.extract_chapters(app_state)
  end)
  
  if not chapter_extract_success then
    r.ShowConsoleMsg("✗ 章节提取失败: " .. tostring(chapter_extract_error) .. "\n")
    return
  end
  
  r.ShowConsoleMsg("✓ 章节提取成功，提取了 " .. #app_state.chapters .. " 个章节\n")
  
  -- 显示测试结果
  r.ShowConsoleMsg("\n=== 测试结果 ===\n")
  r.ShowConsoleMsg("句子数量: " .. #app_state.sentences .. "\n")
  r.ShowConsoleMsg("CV角色对数量: " .. #app_state.cv_role_pairs .. "\n")
  r.ShowConsoleMsg("章节数量: " .. #app_state.chapters .. "\n")
  
  if #app_state.sentences > 0 then
    r.ShowConsoleMsg("\n前3个句子:\n")
    for i = 1, math.min(3, #app_state.sentences) do
      r.ShowConsoleMsg(i .. ". " .. app_state.sentences[i] .. "\n")
    end
  end
  
  if #app_state.cv_role_pairs > 0 then
    r.ShowConsoleMsg("\nCV角色对:\n")
    for i = 1, #app_state.cv_role_pairs do
      local pair = app_state.cv_role_pairs[i]
      r.ShowConsoleMsg(i .. ". CV: " .. pair.cv .. ", 角色: " .. pair.role .. "\n")
    end
  end
  
  if #app_state.chapters > 0 then
    r.ShowConsoleMsg("\n章节:\n")
    for i = 1, #app_state.chapters do
      local chapter = app_state.chapters[i]
      r.ShowConsoleMsg(i .. ". " .. chapter.title .. " (句子索引: " .. chapter.sentence_idx .. ")\n")
    end
  end
  
  r.ShowConsoleMsg("\n🎉 所有语法错误已修复！\n")
  r.ShowConsoleMsg("✅ event_module.lua语法正确\n")
  r.ShowConsoleMsg("✅ 所有模块加载成功\n")
  r.ShowConsoleMsg("✅ 所有模块初始化成功\n")
  r.ShowConsoleMsg("✅ 句子解析功能正常\n")
  r.ShowConsoleMsg("✅ CV角色对提取功能正常\n")
  r.ShowConsoleMsg("✅ 章节提取功能正常\n")
  r.ShowConsoleMsg("✅ 应用状态管理正常\n")
  
  r.ShowConsoleMsg("\n现在可以运行完整的测试脚本了：\n")
  r.ShowConsoleMsg("dofile(\"test_complete_fix.lua\")\n")
  
  r.ShowConsoleMsg("\n=== 语法修复验证完成 ===\n")
end

-- 运行测试
main()
