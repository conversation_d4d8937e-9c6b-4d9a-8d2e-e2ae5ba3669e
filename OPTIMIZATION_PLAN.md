# 代码优化详细方案

## 当前状态分析

### 文件大小统计
- `mark.lua`: 4749行 - **过大，需要重构**
- `button_module.lua`: 2530行 - **较大，可优化**
- `text_utils.lua`: ~1800行 - **合理**
- `style_module.lua`: ~1600行 - **合理**
- `word_module.lua`: ~1200行 - **合理**
- `utils_module.lua`: ~800行 - **合理**
- `excel_module.lua`: 460行 - **合理**
- `module_loader.lua`: 212行 - **合理**
- `JHKAU.lua`: 269行 - **独立脚本**
- `track_align_script.lua`: 127行 - **小模块**
- `chapter_module.lua`: 158行 - **小模块**

### 主要问题识别

#### 1. 主脚本过于庞大
- **问题**: `mark.lua` 包含4749行代码，混合了UI、事件处理、业务逻辑
- **影响**: 难以维护、调试困难、加载缓慢
- **优先级**: 🔴 高

#### 2. 模块依赖复杂
- **问题**: 模块间存在循环依赖风险，映射关系复杂
- **影响**: 加载顺序敏感、难以理解
- **优先级**: 🟡 中

#### 3. 代码重复
- **问题**: 多处相似的错误处理、UI绘制代码
- **影响**: 维护成本高、一致性差
- **优先级**: 🟡 中

#### 4. 小模块过多
- **问题**: `chapter_module.lua`、`track_align_script.lua` 等文件过小
- **影响**: 文件管理复杂、加载开销
- **优先级**: 🟢 低

## 优化方案

### 阶段1: 主脚本重构 (1-2天)

#### 目标
将 `mark.lua` 从4749行减少到1500行以内

#### 具体步骤
1. **创建 `ui_module.lua`** - 提取UI渲染逻辑
   - 移动所有 `draw_*` 函数
   - 移动UI布局计算
   - 移动界面状态管理
   - 预计: ~1500行

2. **创建 `event_module.lua`** - 提取事件处理逻辑
   - 移动鼠标事件处理
   - 移动键盘事件处理
   - 移动窗口事件处理
   - 预计: ~800行

3. **精简 `mark.lua`** - 保留核心逻辑
   - 保留初始化代码
   - 保留主循环
   - 保留模块协调逻辑
   - 目标: ~1200行

#### 预期效果
- 主脚本可读性大幅提升
- 模块职责更加清晰
- 便于并行开发和维护

### 阶段2: 小模块整合 (0.5天)

#### 目标
减少文件数量，简化项目结构

#### 具体步骤
1. **合并 `chapter_module.lua` → `text_utils.lua`**
   - 章节功能与文本处理相关性强
   - 减少一个独立文件

2. **合并 `track_align_script.lua` → `button_module.lua`**
   - 对轨功能由按钮触发
   - 逻辑相关性强

#### 预期效果
- 文件数量从11个减少到9个
- 相关功能更加集中
- 减少模块加载开销

### 阶段3: 代码优化 (1天)

#### 目标
提升代码质量和性能

#### 具体步骤
1. **统一错误处理**
   - 创建统一的错误处理机制
   - 移除重复的错误处理代码

2. **优化字符串处理**
   - 减少字符串拼接操作
   - 使用更高效的字符串处理方法

3. **改进缓存机制**
   - 优化缓存策略
   - 减少重复计算

4. **移除死代码**
   - 删除未使用的函数和变量
   - 清理注释掉的代码

#### 预期效果
- 性能提升10-20%
- 代码更加简洁
- 维护成本降低

### 阶段4: 模块依赖优化 (0.5天)

#### 目标
简化模块依赖关系

#### 具体步骤
1. **清理模块映射**
   - 移除已合并模块的映射
   - 简化 `module_loader.lua`

2. **重新设计依赖关系**
   - 减少循环依赖
   - 优化加载顺序

#### 预期效果
- 模块关系更加清晰
- 加载更加稳定
- 便于理解和维护

## 实施计划

### 时间安排
- **总计**: 3-4天
- **阶段1**: 1-2天 (主脚本重构)
- **阶段2**: 0.5天 (小模块整合)
- **阶段3**: 1天 (代码优化)
- **阶段4**: 0.5天 (依赖优化)

### 风险控制
1. **备份策略**: 每个阶段完成后创建备份
2. **测试策略**: 每个阶段完成后进行功能测试
3. **回滚策略**: 如有问题可快速回滚到上一个稳定版本

### 成功指标
1. **代码行数**: 主脚本减少到1500行以内
2. **文件数量**: 从11个减少到9个
3. **性能**: 启动时间减少10%以上
4. **可维护性**: 模块职责更加清晰

## 详细实施步骤

### 步骤1: 创建UI模块
```lua
-- ui_module.lua
-- 负责所有UI渲染逻辑
local ui_module = {}

-- 从mark.lua移动的函数:
-- - draw_sentences_list()
-- - draw_chapter_list()
-- - draw_selection_content()
-- - render()
-- - draw_ui()
-- 等所有UI相关函数
```

### 步骤2: 创建事件模块
```lua
-- event_module.lua
-- 负责所有事件处理逻辑
local event_module = {}

-- 从mark.lua移动的函数:
-- - handle_mouse_events()
-- - handle_keyboard_events()
-- - handle_window_events()
-- 等所有事件处理函数
```

### 步骤3: 精简主脚本
```lua
-- mark.lua (精简后)
-- 只保留核心初始化和协调逻辑
local modules = module_loader.load_all()
local ui = modules.ui_module
local events = modules.event_module

-- 主循环
function main_loop()
    ui.render()
    events.handle_events()
end
```

## 技术细节分析

### 当前代码问题详细分析

#### mark.lua 中的主要问题
1. **UI渲染逻辑混杂** (约2000行)
   - `render()` 函数过于复杂
   - `draw_sentences_list()` 包含业务逻辑
   - UI状态管理分散

2. **事件处理逻辑混杂** (约1000行)
   - 鼠标事件处理分散在多个函数中
   - 键盘事件处理不统一
   - 事件状态管理复杂

3. **全局变量过多** (约100个)
   - 状态管理混乱
   - 模块间耦合严重
   - 难以追踪变量生命周期

#### 模块依赖问题
1. **循环依赖风险**
   - `button_module` ↔ `style_module`
   - `text_utils` ↔ `utils_module`

2. **过时的映射关系**
   ```lua
   -- module_loader.lua 中的过时映射
   button_helpers = "style_module", -- 已合并
   cache_module = "utils_module"    -- 已合并
   ```

### 性能优化机会

#### 字符串处理优化
```lua
-- 当前低效的字符串拼接
local result = ""
for i, item in ipairs(items) do
    result = result .. item .. "\n"  -- 每次都创建新字符串
end

-- 优化后的方案
local parts = {}
for i, item in ipairs(items) do
    parts[i] = item
end
local result = table.concat(parts, "\n")  -- 一次性拼接
```

#### 缓存优化
```lua
-- 当前缓存策略问题
cached_sentence_heights = {}  -- 全局缓存，难以管理

-- 优化后的缓存策略
local cache = {
    sentence_heights = {},
    text_wrapping = {},
    cleanup_interval = 60,
    max_size = 1000
}
```

#### UI渲染优化
```lua
-- 当前每帧都重绘所有内容
function render()
    draw_everything()  -- 性能问题
end

-- 优化后的增量渲染
function render()
    if need_full_redraw then
        draw_everything()
    else
        draw_changed_parts()  -- 只重绘变化的部分
    end
end
```

## 实施细节

### 阶段1详细步骤

#### 1.1 创建ui_module.lua
```bash
# 需要移动的函数列表
- render()                    # 主渲染函数
- draw_ui()                   # UI绘制
- draw_sentences_list()       # 句子列表
- draw_chapter_list()         # 章节列表
- draw_selection_content()    # 选择内容
- draw_search_ui()           # 搜索界面
- calculate_sentence_height() # 高度计算
- ensure_sentence_heights_cached() # 缓存管理
```

#### 1.2 创建event_module.lua
```bash
# 需要移动的函数列表
- handle_mouse_events()       # 鼠标事件
- handle_keyboard_events()    # 键盘事件
- handle_window_resize()      # 窗口事件
- handle_cv_role_checkbox_click() # 复选框事件
- perform_search()            # 搜索事件
- goto_next_search_result()   # 搜索导航
```

#### 1.3 重构mark.lua
```lua
-- 精简后的mark.lua结构
local modules = module_loader.load_all()
local ui_module = modules.ui_module
local event_module = modules.event_module

-- 全局状态管理
local app_state = {
    sentences = {},
    cv_role_pairs = {},
    selected_cv = "",
    selected_role = "",
    -- 其他状态变量
}

-- 主循环
function main_loop()
    ui_module.render(app_state)
    event_module.handle_events(app_state)

    if app_state.should_exit then
        return false
    end
    return true
end

-- 初始化
function init()
    -- 初始化逻辑
    return true
end

-- 主函数
function main()
    if not init() then return end

    while main_loop() do
        -- 主循环
    end

    cleanup()
end

main()
```

### 阶段2详细步骤

#### 2.1 合并chapter_module到text_utils
```lua
-- text_utils.lua 中添加章节功能
text_utils.chapter = {
    extract_chapters = function(sentences) ... end,
    get_chapter_by_sentence_idx = function(idx) ... end,
    jump_to_chapter = function(idx, callback) ... end
}
```

#### 2.2 合并track_align_script到button_module
```lua
-- button_module.lua 中添加对轨功能
button_module.track_align = {
    align_track_by_cv = function(cv_name) ... end,
    handle_track_align_click = function(is_right_click) ... end
}
```

### 测试策略

#### 功能测试清单
- [ ] UI渲染正常
- [ ] 事件响应正常
- [ ] 模块加载正常
- [ ] 数据保存/加载正常
- [ ] 搜索功能正常
- [ ] 章节功能正常
- [ ] 对轨功能正常
- [ ] Excel导出正常

#### 性能测试
- [ ] 启动时间 < 2秒
- [ ] UI响应时间 < 100ms
- [ ] 内存使用 < 50MB
- [ ] 大文件处理能力

这个优化方案将显著提升代码的可维护性、性能和开发效率。
