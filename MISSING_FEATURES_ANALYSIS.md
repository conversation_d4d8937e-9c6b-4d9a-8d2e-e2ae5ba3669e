# 原始mark.lua功能对比分析

## 已实现的功能 ✅

### 核心UI组件
- ✅ 内容区域（句子列表显示）
- ✅ CV角色列表（按CV分类显示）
- ✅ 选择内容区域
- ✅ 章节列表
- ✅ 输入区域（错误描述、正确表达、集数、处理建议）
- ✅ 搜索框和搜索功能

### 按钮功能
- ✅ 块标按钮（mark_error）
- ✅ 区标按钮（region_mark）
- ✅ Excel写入按钮
- ✅ 播放/暂停按钮
- ✅ 速率控制按钮（+/-/重置）
- ✅ 字体大小控制按钮
- ✅ 文档读取按钮
- ✅ 剪贴板读取按钮
- ✅ 开按钮（打开CSV）
- ✅ AU按钮（运行JHKAU.lua）
- ✅ 区名按钮
- ✅ 文名按钮
- ✅ 轨色按钮
- ✅ 分轨按钮
- ✅ 对轨按钮
- ✅ 章节按钮

### 交互功能
- ✅ 鼠标点击选择句子
- ✅ CV角色点击选择
- ✅ 滚动功能（平滑滚动）
- ✅ 搜索功能（Ctrl+F）
- ✅ 内容区域拖拽调整大小

### 数据处理
- ✅ 句子解析
- ✅ CV角色对提取
- ✅ 章节提取
- ✅ 文本清理和标签处理

## 缺失的功能 ❌

### 1. 高级键盘快捷键 ❌
- ❌ Ctrl+G (下一个搜索结果)
- ❌ Ctrl+P (上一个搜索结果)
- ❌ 其他快捷键组合

### 2. 搜索导航按钮 ❌
- ❌ 搜索结果导航按钮（上一个/下一个）
- ❌ 搜索结果计数显示
- ❌ 搜索结果高亮跳转

### 3. 处理建议下拉菜单 ❌
- ❌ 处理建议下拉选项（返音、补音、后期处理）
- ❌ 下拉菜单交互
- ❌ 建议选项记忆功能

### 4. 时间显示功能 ❌
- ❌ 光标时间显示
- ❌ 音频块内部时间显示
- ❌ 标记时间记录

### 5. CV角色交换位置功能 ❌
- ❌ CV角色位置交换复选框
- ❌ 交换状态记忆
- ❌ 交换后的显示逻辑

### 6. 高级文本处理 ❌
- ❌ 文本标签清理显示
- ❌ 颜色标签处理
- ❌ 删除线样式处理

### 7. 性能优化功能 ❌
- ❌ 句子高度缓存
- ❌ 文本换行缓存
- ❌ 渲染跳帧优化
- ❌ 鼠标移动检测优化

### 8. 状态管理 ❌
- ❌ 上次选择的处理建议记忆
- ❌ 窗口大小和位置记忆
- ❌ 用户偏好设置保存

### 9. 错误处理和调试 ❌
- ❌ 详细的错误日志
- ❌ 调试信息显示
- ❌ 性能监控

### 10. 高级UI效果 ❌
- ❌ 按钮动画效果
- ❌ 悬停状态跟踪
- ❌ 按钮状态动画

## 需要添加的具体功能

### A. 搜索导航功能
```lua
-- 需要添加的函数
function goto_next_search_result()
function goto_prev_search_result()
function draw_search_navigation_buttons()
```

### B. 处理建议下拉菜单
```lua
-- 需要添加的变量和函数
local show_suggestion_dropdown = false
local suggestion_options = {"返音", "补音", "后期处理"}
function draw_suggestion_dropdown()
function handle_suggestion_selection()
```

### C. 时间显示功能
```lua
-- 需要添加的函数
function get_cursor_time()
function get_item_internal_time()
function draw_time_display()
```

### D. CV角色交换功能
```lua
-- 需要添加的变量和函数
local is_cv_role_reversed = false
function toggle_cv_role_position()
function draw_cv_role_reverse_checkbox()
```

### E. 性能优化
```lua
-- 需要添加的缓存系统
local cached_sentence_heights = {}
local cached_text_wrapping = {}
local skip_render_counter = 0
function optimize_rendering()
```

### F. 高级键盘处理
```lua
-- 需要扩展的键盘处理
function handle_advanced_shortcuts(char)
  -- Ctrl+G, Ctrl+P等
end
```

### G. 状态持久化
```lua
-- 需要添加的状态管理
function save_user_preferences()
function load_user_preferences()
function remember_last_settings()
```

## 优先级排序

### 高优先级（核心功能）
1. 搜索导航按钮和快捷键
2. 处理建议下拉菜单
3. CV角色交换位置功能
4. 时间显示功能

### 中优先级（用户体验）
5. 高级键盘快捷键
6. 按钮动画效果
7. 状态记忆功能

### 低优先级（性能优化）
8. 渲染优化
9. 缓存系统
10. 调试功能

## 实施计划

### 第一阶段：核心功能补充
- 添加搜索导航功能
- 实现处理建议下拉菜单
- 添加CV角色交换功能
- 实现时间显示

### 第二阶段：用户体验改进
- 添加高级快捷键
- 实现按钮动画
- 添加状态记忆

### 第三阶段：性能和稳定性
- 优化渲染性能
- 完善错误处理
- 添加调试功能

## 文件修改计划

### event_module.lua
- 添加搜索导航函数
- 添加高级快捷键处理
- 添加下拉菜单事件处理

### ui_module.lua
- 添加搜索导航按钮绘制
- 添加下拉菜单绘制
- 添加时间显示
- 添加CV角色交换复选框

### style_module.lua
- 添加下拉菜单样式
- 添加按钮动画效果
- 添加复选框样式

### utils_module.lua
- 添加时间处理函数
- 添加状态持久化
- 添加性能优化工具

### mark_new.lua
- 添加缺失的全局变量
- 添加高级快捷键处理
- 集成新功能
