-- 文本框完整显示修复测试脚本
-- 专门测试文本框是否能完整显示所有章节

-- 导入必要的库
local r = reaper

-- 获取脚本路径
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"

-- 安全加载函数
local function safe_load_module(file_path)
  local success, result = pcall(dofile, file_path)
  if not success then
    r.ShowConsoleMsg("模块加载失败: " .. file_path .. " - " .. tostring(result) .. "\n")
    return nil
  end
  return result
end

-- 主函数
local function main()
  r.ShowConsoleMsg("=== 文本框完整显示修复测试 ===\n")
  
  -- 加载所有模块
  local utils_module = safe_load_module(script_dir .. "utils_module.lua")
  local style_module = safe_load_module(script_dir .. "style_module.lua")
  local text_utils = safe_load_module(script_dir .. "text_utils.lua")
  local button_module = safe_load_module(script_dir .. "button_module.lua")
  local ui_module = safe_load_module(script_dir .. "ui_module.lua")
  local event_module = safe_load_module(script_dir .. "event_module.lua")
  
  if not utils_module or not style_module or not text_utils or not button_module or not ui_module or not event_module then
    r.ShowConsoleMsg("✗ 模块加载失败\n")
    return
  end
  r.ShowConsoleMsg("✓ 所有模块加载成功\n")
  
  -- 初始化模块
  style_module.init({utils_module = utils_module})
  
  local deps = {
    style_module = style_module,
    text_utils = text_utils,
    button_module = button_module,
    utils_module = utils_module,
    gfx = gfx,
    r = r
  }
  
  ui_module.init(deps)
  deps.ui_module = ui_module
  event_module.init(deps)
  
  if button_module and button_module.init then
    button_module.init({
      utils_module = utils_module,
      text_utils = text_utils,
      style_module = style_module
    })
  end
  
  r.ShowConsoleMsg("✓ 所有模块初始化成功\n")
  
  -- 创建应用状态
  local app_state = utils_module.app_state.create()
  r.ShowConsoleMsg("✓ 应用状态创建成功\n")
  
  -- 测试文档读取功能
  r.ShowConsoleMsg("\n=== 测试文档读取功能 ===\n")
  r.ShowConsoleMsg("请选择一个DOCX文件来测试完整显示功能...\n")
  
  local callbacks = {
    handle_text_content = function(text_content)
      app_state.clipboard_text = text_content
      r.ShowConsoleMsg("✓ 接收到文档内容，长度: " .. #text_content .. " 字符\n")
      
      -- 解析句子
      event_module.parse_sentences(app_state)
      r.ShowConsoleMsg("✓ 解析了 " .. #app_state.sentences .. " 个句子\n")
      
      -- 提取CV角色对
      event_module.extract_cv_role_pairs(app_state)
      r.ShowConsoleMsg("✓ 提取了 " .. #app_state.cv_role_pairs .. " 个CV角色对\n")
      
      -- 提取章节
      event_module.extract_chapters(app_state)
      r.ShowConsoleMsg("✓ 提取了 " .. #app_state.chapters .. " 个章节\n")
    end,
    parse_sentences = function()
      event_module.parse_sentences(app_state)
    end,
    extract_cv_role_pairs = function()
      event_module.extract_cv_role_pairs(app_state)
    end
  }
  
  local result = button_module.handle_document_button(callbacks)
  r.ShowConsoleMsg("文档处理结果: " .. tostring(result) .. "\n")
  
  if #app_state.sentences > 0 then
    r.ShowConsoleMsg("\n=== 内容分析 ===\n")
    r.ShowConsoleMsg("总句子数: " .. #app_state.sentences .. "\n")
    r.ShowConsoleMsg("总章节数: " .. #app_state.chapters .. "\n")
    r.ShowConsoleMsg("总CV角色对数: " .. #app_state.cv_role_pairs .. "\n")
    
    -- 分析句子内容
    local chapter_sentences = 0
    local content_sentences = 0
    local cv_sentences = 0
    
    for i, sentence in ipairs(app_state.sentences) do
      if sentence:find("%[CHAPTER%]") then
        chapter_sentences = chapter_sentences + 1
      elseif sentence:find("【.-】") then
        cv_sentences = cv_sentences + 1
      else
        content_sentences = content_sentences + 1
      end
    end
    
    r.ShowConsoleMsg("章节标记句子: " .. chapter_sentences .. "\n")
    r.ShowConsoleMsg("CV对话句子: " .. cv_sentences .. "\n")
    r.ShowConsoleMsg("普通内容句子: " .. content_sentences .. "\n")
    
    -- 显示前10个句子作为示例
    r.ShowConsoleMsg("\n前10个句子示例:\n")
    for i = 1, math.min(10, #app_state.sentences) do
      r.ShowConsoleMsg(string.format("%d. %s\n", i, app_state.sentences[i]))
    end
    
    -- 显示章节信息
    if #app_state.chapters > 0 then
      r.ShowConsoleMsg("\n章节信息:\n")
      for i = 1, #app_state.chapters do
        local chapter = app_state.chapters[i]
        r.ShowConsoleMsg(string.format("%d. %s (句子索引: %d)\n", i, chapter.title, chapter.sentence_idx))
      end
    end
    
    -- 启动UI测试
    r.ShowConsoleMsg("\n=== 启动UI测试 ===\n")
    
    local window_success = ui_module.init_window()
    if window_success then
      r.ShowConsoleMsg("✓ 窗口初始化成功\n")
      
      -- 设置章节列表可见
      app_state.is_chapter_list_visible = true
      
      -- 初始渲染
      local render_success, render_error = pcall(function()
        ui_module.render(app_state)
      end)
      
      if render_success then
        r.ShowConsoleMsg("✓ 初始渲染成功\n")
        
        -- 启动主循环
        local function loop()
          local char = gfx.getchar()
          if char == -1 then
            r.ShowConsoleMsg("窗口已关闭\n")
            return
          end
          
          -- 处理事件和渲染
          local event_success, event_error = pcall(function()
            event_module.handle_events(app_state)
            ui_module.render(app_state)
          end)
          
          if not event_success then
            r.ShowConsoleMsg("事件处理错误: " .. tostring(event_error) .. "\n")
          end
          
          r.defer(loop)
        end
        
        r.ShowConsoleMsg("\n🎉 文本框完整显示测试启动成功！\n")
        r.ShowConsoleMsg("\n=== 验证指南 ===\n")
        r.ShowConsoleMsg("请在打开的窗口中验证以下内容：\n")
        r.ShowConsoleMsg("1. 文本框是否显示所有 " .. #app_state.sentences .. " 个句子\n")
        r.ShowConsoleMsg("2. 是否可以滚动查看所有内容\n")
        r.ShowConsoleMsg("3. 章节列表是否显示所有 " .. #app_state.chapters .. " 个章节\n")
        r.ShowConsoleMsg("4. 点击章节是否能跳转到对应位置\n")
        r.ShowConsoleMsg("5. 搜索功能是否正常工作\n")
        r.ShowConsoleMsg("6. CV角色列表是否显示所有 " .. #app_state.cv_role_pairs .. " 个角色对\n")
        
        r.ShowConsoleMsg("\n=== 可能的问题和解决方案 ===\n")
        r.ShowConsoleMsg("如果文本框仍然无法完整显示：\n")
        r.ShowConsoleMsg("1. 滚动问题 - 使用鼠标滚轮在文本框区域滚动\n")
        r.ShowConsoleMsg("2. 字体大小 - 点击字体大小按钮调整\n")
        r.ShowConsoleMsg("3. 窗口大小 - 拖拽窗口边缘调整大小\n")
        r.ShowConsoleMsg("4. 渲染区域 - 检查内容是否被其他元素遮挡\n")
        
        r.ShowConsoleMsg("\n=== 功能测试 ===\n")
        r.ShowConsoleMsg("测试以下功能是否正常：\n")
        r.ShowConsoleMsg("• 文本框滚动 - 在文本区域使用鼠标滚轮\n")
        r.ShowConsoleMsg("• 章节跳转 - 点击章节列表中的章节\n")
        r.ShowConsoleMsg("• 章节显示/隐藏 - 点击'章节'按钮\n")
        r.ShowConsoleMsg("• 搜索功能 - 在搜索框中输入关键词\n")
        r.ShowConsoleMsg("• 句子选择 - 点击文本框中的句子\n")
        r.ShowConsoleMsg("• CV角色选择 - 点击CV角色列表中的项目\n")
        
        r.defer(loop)
        
      else
        r.ShowConsoleMsg("✗ 初始渲染失败: " .. tostring(render_error) .. "\n")
      end
    else
      r.ShowConsoleMsg("! 窗口初始化跳过（可能是环境限制）\n")
      r.ShowConsoleMsg("但是内容解析是成功的，主脚本应该能正常工作\n")
    end
    
  else
    r.ShowConsoleMsg("\n⚠️ 没有解析到句子内容\n")
    if result:find("用户取消") then
      r.ShowConsoleMsg("用户取消了文件选择\n")
    else
      r.ShowConsoleMsg("可能的问题: " .. result .. "\n")
    end
  end
  
  r.ShowConsoleMsg("\n=== 修复总结 ===\n")
  r.ShowConsoleMsg("已实施的修复：\n")
  r.ShowConsoleMsg("✅ 文件选择过滤器 - 使用格式3\n")
  r.ShowConsoleMsg("✅ 句子解析逻辑 - 改进的分割算法\n")
  r.ShowConsoleMsg("✅ 章节提取功能 - 支持多种章节格式\n")
  r.ShowConsoleMsg("✅ CV角色对提取 - 支持多种格式\n")
  r.ShowConsoleMsg("✅ trim函数修复 - 解决语法错误\n")
  r.ShowConsoleMsg("✅ UI渲染逻辑 - 完整的文本框渲染\n")
  
  r.ShowConsoleMsg("\n现在可以运行主脚本：\n")
  r.ShowConsoleMsg("dofile(\"mark_new.lua\")\n")
  
  r.ShowConsoleMsg("\n=== 文本框完整显示修复测试完成 ===\n")
end

-- 运行测试
main()
