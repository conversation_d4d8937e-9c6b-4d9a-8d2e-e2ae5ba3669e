-- 统一文本处理模块 - REAPER脚本
-- 合并了原text_module.lua和text_processing.lua的功能
-- 用于处理文本内容，解析句子，提取CV和角色信息等

local r = reaper

-- 使用module_loader加载依赖
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"
local module_loader = dofile(script_dir .. "module_loader.lua")

-- 加载依赖模块
local utils_module = module_loader.load("utils_module")

-- 确保缓存功能被初始化
if utils_module.init_cache then
  utils_module.init_cache()
end

-- 为了兼容性，创建一个cache_module引用，指向utils_module中的缓存函数
local cache_module = {
  has = utils_module.cache_has,
  get = utils_module.cache_get,
  add = utils_module.cache_add,
  remove = utils_module.cache_remove,
  clear = utils_module.cache_clear,
  clear_pattern = utils_module.cache_clear_pattern,
  init = utils_module.init_cache
}

-- 创建模块表
local text_utils = {
  -- 导入utils_module中的函数
  format_time = utils_module.format_time,
}

-- 获取剪贴板内容
function text_utils.get_clipboard()
  local status, clipboard = pcall(r.CF_GetClipboard, "")
  if not status then
    return nil, "获取剪贴板错误: " .. tostring(clipboard)
  end

  if clipboard and clipboard ~= "" then
    return clipboard
  end

  return nil, "剪贴板为空"
end

-- 读取文本文件内容
function text_utils.read_text_file()
  -- 使用REAPER的文件对话框API
  local retval, file_path = r.GetUserFileNameForRead("", "选择文本文件", ".txt")

  if not retval then
    return false, "用户取消了文件选择"
  end

  -- 尝试打开并读取文件
  local file = io.open(file_path, "r")
  if not file then
    return false, "无法打开文件: " .. file_path
  end

  -- 读取文件内容
  local content = file:read("*all")
  file:close()

  if content and content ~= "" then
    return true, content, "已读取文件: " .. file_path
  else
    return false, "", "文件内容为空"
  end
end

-- 错误处理现在统一使用utils_module.error_handler

-- 优化文本解析函数，使用缓存提高性能
function text_utils.parse_sentences(text)
    -- 使用统一的参数验证
    local valid, error_msg = utils_module.validate.string(text, "text", false)
    if not valid then
        return {}
    end

    -- 使用统一的缓存装饰器生成缓存键
    local cache_key = utils_module.cache_decorator.text_cache_key(text, "parse_sentences")

    -- 检查缓存是否有结果
    if utils_module.cache_has(cache_key) then
        return utils_module.cache_get(cache_key)
    end

    local sentences = {}
    local current_sentence = ""
    local in_brackets = false
    local in_color_tag = false
    local in_strike_tag = false
    local in_chapter_tag = false  -- 追踪是否在章节标记内
    local in_role_cv = false  -- 追踪是否在【角色-CV】格式内
    local continuous_text = false  -- 追踪是否在连续文本中
    local color_tag_stack = 0  -- 用于处理嵌套的颜色标签

    -- 使用更高效的字符遍历
    local i = 1
    while i <= #text do
        local char = text:sub(i, i)
        local next_chars = text:sub(i, i + 2)

        -- 检查是否处于颜色标签内（使用堆栈跟踪嵌套标签）
        if next_chars == "[#]" then
            color_tag_stack = math.max(0, color_tag_stack - 1)
            in_color_tag = (color_tag_stack > 0)
            current_sentence = current_sentence .. next_chars
            i = i + 3
        elseif i + 1 <= #text and text:sub(i, i + 1) == "[#" then
            -- 检查是否是颜色标签的开始
            -- 查找颜色代码的结束位置
            local end_pos = text:find("%]", i + 2)
            if end_pos and end_pos - i <= 9 then  -- 确保颜色代码长度合理（不超过7个字符）
                color_tag_stack = color_tag_stack + 1
                in_color_tag = true
                -- 添加整个颜色标签
                local tag = text:sub(i, end_pos)
                current_sentence = current_sentence .. tag
                i = end_pos + 1
            else
                -- 非标准颜色标签，当作普通文本处理
                current_sentence = current_sentence .. char
                i = i + 1
            end
        -- 检查是否处于删除线标签内
        elseif text:sub(i, i + 3) == "[/x]" then
            in_strike_tag = false
            current_sentence = current_sentence .. "[/x]"
            i = i + 4
        elseif text:sub(i, i + 2) == "[x]" then
            in_strike_tag = true
            current_sentence = current_sentence .. "[x]"
            i = i + 3
        -- 检查是否处于章节标记内
        elseif text:sub(i, i + 9) == "[CHAPTER]" then
            in_chapter_tag = true
            current_sentence = current_sentence .. "[CHAPTER]"
            i = i + 9
        elseif text:sub(i, i + 10) == "[/CHAPTER]" then
            in_chapter_tag = false
            current_sentence = current_sentence .. "[/CHAPTER]"
            i = i + 10
        else
            -- 处理角色名标签和常规文本
            if char == "【" then
                in_brackets = true
                in_role_cv = true  -- 可能是角色-CV格式
                continuous_text = false  -- 重置连续文本标记
                current_sentence = current_sentence .. char
                i = i + 1
            elseif char == "】" then
                in_brackets = false
                in_role_cv = false  -- 结束角色-CV格式
                continuous_text = true  -- 开始连续文本
                current_sentence = current_sentence .. char
                i = i + 1
            elseif char == "\n" and not in_brackets and not in_color_tag and not in_strike_tag and not in_chapter_tag then
                -- 只有当不在任何标签内时，才在换行符处分割句子
                if current_sentence ~= "" then
                    table.insert(sentences, current_sentence)
                    current_sentence = ""
                    continuous_text = false  -- 重置连续文本标记
                end
                i = i + 1
            else
                -- 检查连续性标记
                if not in_brackets and (char == "。" or char == "！" or char == "？") then
                    continuous_text = false  -- 句子已结束，重置连续性
                elseif not in_brackets and char ~= " " and char ~= "\t" then
                    continuous_text = true  -- 普通文字，标记为连续
                end

                current_sentence = current_sentence .. char
                i = i + 1
            end
        end
    end

    -- 添加最后一个句子
    if current_sentence ~= "" then
        table.insert(sentences, current_sentence)
    end

    -- 后处理：检查颜色标签或删除线标签是否完整
    for i = 1, #sentences do
        local sentence = sentences[i]

        -- 处理不完整的颜色标签
        local color_start_count = 0
        local color_end_count = 0
        -- 使用更精确的正则表达式计算颜色标签
        for _ in sentence:gmatch("%[#[0-9A-Fa-f]+%]") do
            color_start_count = color_start_count + 1
        end
        for _ in sentence:gmatch("%[#%]") do
            color_end_count = color_end_count + 1
        end

        -- 如果缺少结束标签，并且下一句存在，尝试与下一句合并
        if color_start_count > color_end_count and i < #sentences then
            sentences[i] = sentence .. " " .. sentences[i+1]
            sentences[i+1] = "__SKIP_THIS_SENTENCE__"
        end

        -- 处理不完整的删除线标签
        local strike_start_count = 0
        local strike_end_count = 0
        for _ in sentence:gmatch("%[x%]") do
            strike_start_count = strike_start_count + 1
        end
        for _ in sentence:gmatch("%[/x%]") do
            strike_end_count = strike_end_count + 1
        end

        -- 如果缺少结束标签，并且下一句存在，尝试与下一句合并
        if strike_start_count > strike_end_count and i < #sentences then
            sentences[i] = sentence .. " " .. sentences[i+1]
            sentences[i+1] = "__SKIP_THIS_SENTENCE__"
        end
    end

    -- 过滤被标记为跳过的句子
    local filtered_sentences = {}
    for _, sentence in ipairs(sentences) do
        if sentence ~= "__SKIP_THIS_SENTENCE__" then
            table.insert(filtered_sentences, sentence)
        end
    end

    -- 缓存结果
    cache_module.add(cache_key, filtered_sentences)

    return filtered_sentences
end

-- 从文本中提取角色和CV信息
function text_utils.extract_cv_role_from_text(text, is_cv_role_reversed)
  local role, cv = "旁白", "旁白" -- 默认为旁白

  -- 清理可能的颜色标签
  local clean_text = text_utils.clean_cv_role_tags(text)

  -- 遍历所有【】中的内容，寻找符合角色-CV格式的内容
  for bracket_content in clean_text:gmatch("【(.-)】") do
    -- 创建扫描算法，处理可能出现的括号
    local in_parenthesis = false
    local last_dash_pos = 0

    -- 字符一个个扫描，正确识别仅当不在括号内时的破折号
    for i = 1, #bracket_content do
      local char = bracket_content:sub(i, i)

      -- 处理括号（支持中英文括号）
      if char == "(" or char == "（" then
        in_parenthesis = true
      elseif char == ")" or char == "）" then
        in_parenthesis = false
      -- 只在不在括号内时记录破折号位置
      elseif not in_parenthesis and char == "-" then
        last_dash_pos = i
      end
    end

    -- 如果存在破折号，则进行处理
    if last_dash_pos > 0 then
      -- 根据is_cv_role_reversed决定哪边是角色，哪边是CV
      local left_part = bracket_content:sub(1, last_dash_pos - 1)
      local right_part = bracket_content:sub(last_dash_pos + 1)

      -- 去除前后空格
      left_part = left_part:match("^%s*(.-)%s*$")
      right_part = right_part:match("^%s*(.-)%s*$")

      -- 移除右部分可能的括号内容，如"CV(描述)"中的(描述)
      local cv_part = right_part:gsub("%([^%)]*%)", ""):match("^%s*(.-)%s*$")

      -- 只有当处理后的CV部分不为空时才使用处理结果
      if cv_part ~= "" then
        right_part = cv_part
      end

      if left_part ~= "" and right_part ~= "" then
        if is_cv_role_reversed then
          -- 如果反转，左边是CV，右边是角色
          return right_part, left_part
        else
          -- 默认情况，左边是角色，右边是CV
          return left_part, right_part
        end
      end
    end
  end

  -- 如果没有找到符合格式的内容，返回默认值
  return role, cv
end

-- 优化CV角色提取函数，使用缓存提高性能
function text_utils.extract_cv_role_pairs(sentences, is_cv_role_reversed)
    -- 使用统一的参数验证
    local valid, error_msg = utils_module.validate.table(sentences, "sentences", false)
    if not valid then
        return {}
    end

    -- 为句子数组创建唯一标识
    local sentences_hash = ""
    for i, sentence in ipairs(sentences) do
        if i <= 10 then  -- 只使用前10个句子来生成哈希，避免过大的键
            sentences_hash = sentences_hash .. #sentence
        end
    end

    -- 使用统一的缓存装饰器生成缓存键
    local cache_params = {
        reversed = is_cv_role_reversed,
        count = #sentences
    }
    local cache_key = utils_module.cache_decorator.sentences_cache_key(sentences, cache_params)

    -- 检查缓存是否有结果
    if utils_module.cache_has(cache_key) then
        return utils_module.cache_get(cache_key)
    end

    local cv_role_pairs = {}
    local seen_pairs = {}  -- 用于快速查找重复

    -- 添加默认的旁白角色
    table.insert(cv_role_pairs, {role = "旁白", cv = "旁白"})
    seen_pairs["旁白-旁白"] = true

    for _, sentence in ipairs(sentences) do
        -- 清理标签，以防角色和CV信息中包含颜色标签
        local clean_sentence = text_utils.clean_cv_role_tags(sentence)

        -- 遍历所有【】中的内容
        for bracket_content in clean_sentence:gmatch("【(.-)】") do
            -- 创建扫描算法，处理可能出现的括号
            local in_parenthesis = false
            local last_dash_pos = 0

            -- 字符一个个扫描，正确识别仅当不在括号内时的破折号
            for i = 1, #bracket_content do
              local char = bracket_content:sub(i, i)

              -- 处理括号
              if char == "(" then
                in_parenthesis = true
              elseif char == ")" then
                in_parenthesis = false
              -- 只在不在括号内时记录破折号位置
              elseif not in_parenthesis and char == "-" then
                last_dash_pos = i
              end
            end

            -- 如果存在破折号，则进行处理
            if last_dash_pos > 0 then
              -- 根据is_cv_role_reversed决定哪边是角色，哪边是CV
              local left_part = bracket_content:sub(1, last_dash_pos - 1)
              local right_part = bracket_content:sub(last_dash_pos + 1)

              -- 去除前后空格
              left_part = left_part:match("^%s*(.-)%s*$")
              right_part = right_part:match("^%s*(.-)%s*$")

              -- 移除右部分可能的括号内容，如"CV(描述)"中的(描述)
              local cv_part = right_part:gsub("%([^%)]*%)", ""):match("^%s*(.-)%s*$")

              -- 只有当处理后的CV部分不为空时才使用处理结果
              if cv_part ~= "" then
                right_part = cv_part
              end

              if left_part ~= "" and right_part ~= "" then
                local role, cv
                if is_cv_role_reversed then
                  -- 如果反转，左边是CV，右边是角色
                  role = right_part
                  cv = left_part
                else
                  -- 默认情况，左边是角色，右边是CV
                  role = left_part
                  cv = right_part
                end

                local pair_key = role .. "-" .. cv
                if not seen_pairs[pair_key] then
                  table.insert(cv_role_pairs, {role = role, cv = cv})
                  seen_pairs[pair_key] = true
                end
              end
            end
        end
    end

    -- 确保旁白角色存在（即使已经在初始添加过，再次检查以保证安全）
    local has_narrator = false
    for _, pair in ipairs(cv_role_pairs) do
      if pair.role == "旁白" and pair.cv == "旁白" then
        has_narrator = true
        break
      end
    end

    if not has_narrator and #cv_role_pairs > 0 then
      -- 把旁白放在最前面
      table.insert(cv_role_pairs, 1, {role = "旁白", cv = "旁白"})
    elseif #cv_role_pairs == 0 then
      -- 如果没有提取到任何角色，至少添加一个旁白
      table.insert(cv_role_pairs, {role = "旁白", cv = "旁白"})
    end

    -- 缓存结果
    utils_module.cache_add(cache_key, cv_role_pairs)

    return cv_role_pairs
end

-- 添加一个函数用于查找特定CV和角色在列表中的索引
function text_utils.find_cv_role_index(cv_role_pairs, role, cv)
  if not cv_role_pairs or type(cv_role_pairs) ~= "table" then
    return 0
  end

  for i, pair in ipairs(cv_role_pairs) do
    if pair.role == role and pair.cv == cv then
      return i
    end
  end

  return 0  -- 如果未找到，返回0
end

-- 添加一个函数用于智能处理CV-角色选择，支持单击文本或选择CV角色列表
function text_utils.handle_cv_role_selection(text, cv_role_pairs, selected_role, selected_cv, is_cv_role_reversed)
  -- 首先检查是否包含章节标记或为章节标题，如果是则直接返回章节文本作为角色和CV
  if text and text ~= "" then
    -- 首先检查是否包含章节标记
    local chapter_content = text:match("%[CHAPTER%](.-)%[/CHAPTER%]")
    if chapter_content then
      -- 如果包含章节标记，提取章节内容并使用作为角色和CV
      local chapter_clean = chapter_content:match("^%s*(.-)%s*$") or chapter_content
      return chapter_clean, chapter_clean
    end

    -- 然后检查是否为章节标题格式（兼容没有标记的情况）
    if text:match("^%s*第%s*[%d一二三四五六七八九十百千]+%s*[章节集话回]") or
       text:match("^%s*章节%s*[%d一二三四五六七八九十百千]+") then
      -- 清理文本，去除前后空白
      local clean_text = text:match("^%s*(.-)%s*$")
      return clean_text, clean_text
    end

    -- 检查是否是CV:角色格式（来自CV角色列表的点击）
    local cv_part, role_part = text:match("^(.+):(.+)$")
    if cv_part and role_part then
      -- 清理空白字符
      cv_part = cv_part:match("^%s*(.-)%s*$") or cv_part
      role_part = role_part:match("^%s*(.-)%s*$") or role_part

      -- 验证这个CV角色对是否在列表中
      for _, pair in ipairs(cv_role_pairs) do
        if pair.cv == cv_part and pair.role == role_part then
          -- 根据is_cv_role_reversed决定返回顺序
          if is_cv_role_reversed then
            return cv_part, role_part  -- 交换时：角色=CV，CV=角色
          else
            return role_part, cv_part  -- 正常时：角色=角色，CV=CV
          end
        end
      end
    end
  end

  -- 如果提供了文本，尝试从中提取CV和角色
  if text and text ~= "" then
    local role, cv = text_utils.extract_cv_role_from_text(text, is_cv_role_reversed)
    if role ~= "" and cv ~= "" then
      return role, cv
    end
  end

  -- 如果已有选择且有效，保持不变
  if selected_role and selected_role ~= "" and selected_cv and selected_cv ~= "" then
    -- 验证选择的CV和角色是否在列表中
    for _, pair in ipairs(cv_role_pairs) do
      if pair.role == selected_role and pair.cv == selected_cv then
        return selected_role, selected_cv
      end
    end
  end

  -- 如果以上都失败，使用第一个可用选项（通常是旁白）
  if cv_role_pairs and #cv_role_pairs > 0 then
    return cv_role_pairs[1].role, cv_role_pairs[1].cv
  end

  -- 最后的后备方案
  return "旁白", "旁白"
end

-- 计算文本显示时需要的高度（简化版，保留兼容性）
function text_utils.calculate_text_height_simple(text, max_width, gfx)
  if not text or text == "" then return 0 end

  -- 获取当前字体信息
  local _, _, font_size = gfx.getfont()

  -- 基本行高（字体大小加上一些间距）
  local line_height = font_size + 2

  -- 如果文本很短，可能不需要换行
  local text_w = gfx.measurestr(text)

  -- 添加安全检查，确保text_w不为nil
  if text_w == nil then
    text_w = max_width * 2  -- 假设文本宽度超过最大宽度
  end

  if text_w <= max_width then
    return line_height
  end

  -- 计算需要多少行来显示文本
  local lines = 0
  local current_line = ""

  for word in text:gmatch("%S+") do
    local test_line = current_line ~= "" and (current_line .. " " .. word) or word
    local test_w = gfx.measurestr(test_line)

    if test_w > max_width then
      -- 当前行已满，开始新行
      lines = lines + 1
      current_line = word
    else
      -- 继续添加到当前行
      current_line = test_line
    end
  end

  -- 加上最后一行
  if current_line ~= "" then
    lines = lines + 1
  end

  -- 返回总高度
  return lines * line_height
end

-- 简化版文本换行函数（保留兼容性）
function text_utils.wrap_text_simple(text, max_width, x, y, draw, min_y, max_y, gfx)
  if not text or text == "" then return 0 end

  -- 获取当前字体信息
  local _, _, font_size = gfx.getfont()

  -- 基本行高（字体大小加上一些间距）
  local line_height = font_size + 2

  -- 如果文本很短，可能不需要换行
  local text_w = gfx.measurestr(text)

  -- 添加安全检查，确保text_w不为nil
  if text_w == nil then
    text_w = max_width * 2  -- 假设文本宽度超过最大宽度
  end

  if text_w <= max_width then
    if draw and y >= min_y and y <= max_y then
      gfx.x, gfx.y = x, y
      gfx.drawstr(text)
    end
    return line_height
  end

  -- 换行绘制
  local current_y = y
  local current_line = ""

  for word in text:gmatch("%S+") do
    local test_line = current_line ~= "" and (current_line .. " " .. word) or word
    local test_w = gfx.measurestr(test_line)

    if test_w > max_width then
      -- 绘制当前行
      if draw and current_y >= min_y and current_y <= max_y then
        gfx.x, gfx.y = x, current_y
        gfx.drawstr(current_line)
      end

      -- 移动到下一行
      current_y = current_y + line_height
      current_line = word
    else
      -- 继续添加到当前行
      current_line = test_line
    end
  end

  -- 绘制最后一行
  if current_line ~= "" and draw and current_y >= min_y and current_y <= max_y then
    gfx.x, gfx.y = x, current_y
    gfx.drawstr(current_line)
  end

  -- 返回总高度
  return current_y - y + line_height
end

-- 完全重写的文本换行函数，更好地处理长句子
function text_utils.wrap_text(text, max_width, x, y, draw, min_y, max_y, gfx)
  -- 安全检查
  if not text or text == "" or not max_width or max_width <= 0 then
    return 0
  end

  -- 获取当前字体信息
  local _, _, font_size = gfx.getfont()
  font_size = font_size or 18  -- 默认字体大小

  -- 设置行高和安全边距
  local line_height = math.max(font_size + 2, math.floor(font_size * 1.2))  -- 行高至少为字体大小+2，或者字体大小的1.2倍

  -- 设置默认的可见区域边界
  min_y = min_y or 0
  max_y = max_y or 9999

  -- 处理标签
  local has_color_tag = text:find("%[#") ~= nil
  local has_bg_color_tag = text:find("%[bg#") ~= nil
  local has_strike_tag = text:find("%[x%]") ~= nil

  -- 使用缓存检查是否有预处理过的行信息
  local cache_key = "wrap_" .. utils_module.hash_string(text .. max_width .. font_size)
  local cached_lines = cache_module.get(cache_key)

  local lines = nil
  if cached_lines then
    lines = cached_lines
  else
    -- 首先移除所有标签，测量实际的清洁文本宽度
    local clean_text = text:gsub("%[#[0-9A-Fa-f]+%]", ""):gsub("%[#%]", "")
                          :gsub("%[bg#[0-9A-Fa-f]+%]", ""):gsub("%[bg#%]", "")
                          :gsub("%[x%]", ""):gsub("%[/x%]", "")

    local clean_text_width = gfx.measurestr(clean_text)

    -- 调整最大宽度以增加安全边距
    local font_size_for_margin = gfx.getfont() -- 获取字体大小用于边距计算
    local dynamic_margin = math.max(4, math.floor(font_size_for_margin * 0.2)) -- 动态计算边距，最小为4，约为字体大小的20%
    local adjusted_max_width = max_width - dynamic_margin  -- 使用动态计算的安全边距

    -- 快速路径：如果清洁文本宽度完全适合一行，直接处理
    if clean_text_width <= adjusted_max_width then
      lines = {{text = text, is_single_line = true}}
    else
      -- 对于需要换行的文本，使用改进的处理逻辑
      lines = {}

      -- 预处理标签和字符
      local segments = {}
      local i = 1

      -- 分析文本，将其分解为标签和文本段
      while i <= #text do
        -- 检查是否是标签开始
        if text:sub(i, i) == "[" then
          -- 检查各种标签类型
          local tag_type = nil
          local tag_end = nil

          -- 颜色标签 [#RRGGBB]
          if text:sub(i, i+1) == "[#" then
            tag_end = text:find("%]", i+2)
            if tag_end then
              tag_type = "color"
            end
          -- 背景色标签 [bg#RRGGBB]
          elseif text:sub(i, i+3) == "[bg#" then
            tag_end = text:find("%]", i+4)
            if tag_end then
              tag_type = "bg_color"
            end
          -- 删除线开始标签 [x]
          elseif text:sub(i, i+2) == "[x]" then
            tag_end = i + 2
            tag_type = "strike_start"
          -- 删除线结束标签 [/x]
          elseif text:sub(i, i+3) == "[/x]" then
            tag_end = i + 3
            tag_type = "strike_end"
          end

          -- 如果找到了有效标签
          if tag_type and tag_end then
            local tag = text:sub(i, tag_end)
            table.insert(segments, {type = "tag", content = tag, width = 0})
            i = tag_end + 1
          else
            -- 不是有效标签，作为普通字符处理
            local char = text:sub(i, i)
            table.insert(segments, {type = "char", content = char, width = gfx.measurestr(char)})
            i = i + 1
          end
        else
          -- 处理普通字符
          local byte = text:byte(i)
          local char_len = 1

          -- 处理UTF-8多字节字符
          if byte >= 0xC0 and byte <= 0xDF then char_len = 2
          elseif byte >= 0xE0 and byte <= 0xEF then char_len = 3
          elseif byte >= 0xF0 and byte <= 0xF7 then char_len = 4
          end

          -- 确保不会超出字符串范围
          if i + char_len - 1 > #text then
            char_len = #text - i + 1
          end

          local char = text:sub(i, i + char_len - 1)
          local width = gfx.measurestr(char)

          table.insert(segments, {type = "char", content = char, width = width})
          i = i + char_len
        end
      end

      -- 使用分段信息构建行
      local current_line = ""
      local current_width = 0
      local active_tags = {
        bg_color = nil,
        color = nil,
        strike = false
      }

      for _, segment in ipairs(segments) do
        if segment.type == "tag" then
          -- 处理标签
          current_line = current_line .. segment.content

          -- 更新活动标签状态
          if segment.content:match("^%[bg#[0-9A-Fa-f]+%]$") then
            active_tags.bg_color = segment.content:match("^%[bg#([0-9A-Fa-f]+)%]$")
          elseif segment.content == "[bg#]" then
            active_tags.bg_color = nil
          elseif segment.content:match("^%[#[0-9A-Fa-f]+%]$") then
            active_tags.color = segment.content:match("^%[#([0-9A-Fa-f]+)%]$")
          elseif segment.content == "[#]" then
            active_tags.color = nil
          elseif segment.content == "[x]" then
            active_tags.strike = true
          elseif segment.content == "[/x]" then
            active_tags.strike = false
          end
        else
          -- 处理字符
          if current_width + segment.width > adjusted_max_width then
            -- 需要换行
            table.insert(lines, {text = current_line})

            -- 开始新行，保持活动的标签
            current_line = ""
            if active_tags.bg_color then
              current_line = current_line .. "[bg#" .. active_tags.bg_color .. "]"
            end
            if active_tags.color then
              current_line = current_line .. "[#" .. active_tags.color .. "]"
            end
            if active_tags.strike then
              current_line = current_line .. "[x]"
            end

            current_line = current_line .. segment.content
            current_width = segment.width
          else
            -- 继续当前行
            current_line = current_line .. segment.content
            current_width = current_width + segment.width
          end
        end
      end

      -- 添加最后一行
      if current_line ~= "" then
        table.insert(lines, {text = current_line})
      end
    end

    -- 缓存处理结果
    cache_module.add(cache_key, lines)
  end

  -- 如果只需要计算高度，不需要绘制
  if not draw then
    return #lines * line_height
  end

  -- 绘制文本
  local total_height = 0
  local last_visible_line = 0

  -- 首先检查第一行是否已经超出可见区域底部
  if y > max_y then
    return 0  -- 完全不可见，直接返回
  end

  -- 检查原始文本是否是章节标题 (这个检查逻辑需要与 draw_sentence 中的一致)
  local is_chapter_title_check = false
  if text:match("^%s*第%s*[%d一二三四五六七八九十百千]+%s*[章节集话回]") or
     text:match("^%s*章节%s*[%d一二三四五六七八九十百千]+") then
     is_chapter_title_check = true
  end

  for i, line_data in ipairs(lines) do
    local line_text = line_data.text
    local line_y = y + (i - 1) * line_height

    -- 检查是否已经超出可见区域底部
    -- 并且确保当前行加上行高不会超出 max_y
    if line_y > max_y or (line_y + line_height) > max_y + 1 then -- 增加 +1 允许绘制最后一行完整显示
      break  -- 超出可见区域底部，停止绘制
    end

    -- 只绘制可见区域内的行
    if line_y >= min_y - line_height and line_y <= max_y then
      local current_draw_x = x
      if is_chapter_title_check then
        -- 为章节标题的每一行计算居中位置
        local clean_line_text = line_text:gsub("%[.-%]", "") -- 移除标签后测量
        local line_text_width = gfx.measurestr(clean_line_text)
        if line_text_width < max_width then -- 使用 wrap_text 内部的 max_width
          current_draw_x = x + (max_width - line_text_width) / 2
        end
      end
      gfx.x, gfx.y = current_draw_x, line_y

      -- 如果是单行且没有标签，直接绘制
      if line_data.is_single_line and not (has_color_tag or has_bg_color_tag or has_strike_tag) then
        gfx.drawstr(line_text)
      else
        -- 处理带标签的文本行
        text_utils.draw_tagged_line(line_text, current_draw_x, line_y, line_height, gfx, search_keyword, highlight_color)
      end
      last_visible_line = i
    end

    total_height = total_height + line_height
  end

  return total_height
end

-- 辅助函数：绘制带标签的文本行（优化版）
function text_utils.draw_tagged_line(line, x, y, line_height, gfx, search_keyword, highlight_color)
  -- 检查特定的标签类型
  local has_color = line:find("%[#")
  local has_bg_color = line:find("%[bg#")
  local has_strike = line:find("%[x%]")

  -- 如果没有标签，直接绘制
  if not has_color and not has_bg_color and not has_strike then
    gfx.x, gfx.y = x, y
    gfx.drawstr(line)
    return
  end

  -- 预处理：将文本分解为标签和文本段
  local segments = {}
  local i = 1

  while i <= #line do
    -- 检查是否是标签开始
    if line:sub(i, i) == "[" then
      -- 检查各种标签类型
      local tag_type = nil
      local tag_end = nil

      -- 颜色标签 [#RRGGBB]
      if line:sub(i, i+1) == "[#" then
        tag_end = line:find("%]", i+2)
        if tag_end then
          if line:sub(i+2, tag_end-1) == "" then
            tag_type = "color_end"
          else
            tag_type = "color_start"
          end
        end
      -- 背景色标签 [bg#RRGGBB]
      elseif line:sub(i, i+3) == "[bg#" then
        tag_end = line:find("%]", i+4)
        if tag_end then
          if line:sub(i+4, tag_end-1) == "" then
            tag_type = "bg_color_end"
          else
            tag_type = "bg_color_start"
          end
        end
      -- 删除线开始标签 [x]
      elseif line:sub(i, i+2) == "[x]" then
        tag_end = i + 2
        tag_type = "strike_start"
      -- 删除线结束标签 [/x]
      elseif line:sub(i, i+3) == "[/x]" then
        tag_end = i + 3
        tag_type = "strike_end"
      end

      -- 如果找到了有效标签
      if tag_type and tag_end then
        local tag = line:sub(i, tag_end)
        -- 提取颜色代码（如果有）
        local color_code = nil
        if tag_type == "color_start" then
          color_code = line:sub(i+2, tag_end-1)
        elseif tag_type == "bg_color_start" then
          color_code = line:sub(i+4, tag_end-1)
        end

        table.insert(segments, {
          type = tag_type,
          content = tag,
          color_code = color_code
        })
        i = tag_end + 1
      else
        -- 不是有效标签，作为普通字符处理
        local char = line:sub(i, i)
        if #segments > 0 and segments[#segments].type == "text" then
          segments[#segments].content = segments[#segments].content .. char
        else
          table.insert(segments, {type = "text", content = char})
        end
        i = i + 1
      end
    else
      -- 处理普通文本，尝试批量处理连续文本
      local next_tag = line:find("%[", i)
      if next_tag then
        -- 添加从当前位置到下一个标签之前的所有文本
        local text = line:sub(i, next_tag - 1)
        if #segments > 0 and segments[#segments].type == "text" then
          segments[#segments].content = segments[#segments].content .. text
        else
          table.insert(segments, {type = "text", content = text})
        end
        i = next_tag
      else
        -- 添加剩余所有文本
        local text = line:sub(i)
        if #segments > 0 and segments[#segments].type == "text" then
          segments[#segments].content = segments[#segments].content .. text
        else
          table.insert(segments, {type = "text", content = text})
        end
        i = #line + 1
      end
    end
  end

  -- 渲染预处理后的段落
  local current_x = x
  local current_color = {r = gfx.r, g = gfx.g, b = gfx.b, a = gfx.a}
  local current_bg_color = nil
  local strike_start_x = nil

  for _, segment in ipairs(segments) do
    if segment.type == "text" then
      -- 绘制文本
      gfx.x, gfx.y = current_x, y

      -- 检查是否需要搜索高亮
      if search_keyword and search_keyword ~= "" and highlight_color then
        -- 在文本中查找搜索关键词并高亮显示
        local text = segment.content
        local clean_text = text:gsub("%[#[0-9A-Fa-f]*%]", ""):gsub("%[bg#[0-9A-Fa-f]*%]", ""):gsub("%[/?x%]", "")
        local search_lower = search_keyword:lower()
        local text_lower = clean_text:lower()

        local start_pos = 1
        local found_pos = text_lower:find(search_lower, start_pos, true)

        if found_pos then
          -- 找到匹配，分段绘制
          while found_pos do
            -- 绘制匹配前的文本
            if found_pos > start_pos then
              local before_text = clean_text:sub(start_pos, found_pos - 1)
              gfx.x, gfx.y = current_x, y

              -- 如果有背景色，先绘制背景
              if current_bg_color then
                local text_width = gfx.measurestr(before_text)
                local text_r, text_g, text_b, text_a = gfx.r, gfx.g, gfx.b, gfx.a
                gfx.set(current_bg_color.r, current_bg_color.g, current_bg_color.b, current_bg_color.a)
                gfx.rect(current_x, y, text_width, line_height)
                gfx.set(text_r, text_g, text_b, text_a)
              end

              gfx.drawstr(before_text)
              current_x = current_x + gfx.measurestr(before_text)
            end

            -- 绘制高亮的匹配文本
            local match_text = clean_text:sub(found_pos, found_pos + #search_keyword - 1)
            local text_width = gfx.measurestr(match_text)

            -- 保存当前颜色
            local saved_r, saved_g, saved_b, saved_a = gfx.r, gfx.g, gfx.b, gfx.a

            -- 绘制高亮背景
            gfx.set(highlight_color.r, highlight_color.g, highlight_color.b, highlight_color.a)
            gfx.rect(current_x, y, text_width, line_height)

            -- 恢复文本颜色并绘制匹配文本
            gfx.set(saved_r, saved_g, saved_b, saved_a)
            gfx.x, gfx.y = current_x, y
            gfx.drawstr(match_text)
            current_x = current_x + text_width

            -- 查找下一个匹配
            start_pos = found_pos + #search_keyword
            found_pos = text_lower:find(search_lower, start_pos, true)
          end

          -- 绘制剩余文本
          if start_pos <= #clean_text then
            local remaining_text = clean_text:sub(start_pos)
            gfx.x, gfx.y = current_x, y

            -- 如果有背景色，先绘制背景
            if current_bg_color then
              local text_width = gfx.measurestr(remaining_text)
              local text_r, text_g, text_b, text_a = gfx.r, gfx.g, gfx.b, gfx.a
              gfx.set(current_bg_color.r, current_bg_color.g, current_bg_color.b, current_bg_color.a)
              gfx.rect(current_x, y, text_width, line_height)
              gfx.set(text_r, text_g, text_b, text_a)
            end

            gfx.drawstr(remaining_text)
            current_x = current_x + gfx.measurestr(remaining_text)
          end
        else
          -- 没有匹配，正常绘制
          -- 如果有背景色，先绘制背景
          if current_bg_color then
            local text_width = gfx.measurestr(segment.content)
            local text_r, text_g, text_b, text_a = gfx.r, gfx.g, gfx.b, gfx.a
            gfx.set(current_bg_color.r, current_bg_color.g, current_bg_color.b, current_bg_color.a)
            gfx.rect(current_x, y, text_width, line_height)
            gfx.set(text_r, text_g, text_b, text_a)
          end

          gfx.drawstr(segment.content)
          current_x = current_x + gfx.measurestr(segment.content)
        end
      else
        -- 没有搜索关键词，正常绘制
        -- 如果有背景色，先绘制背景
        if current_bg_color then
          local text_width = gfx.measurestr(segment.content)
          local text_r, text_g, text_b, text_a = gfx.r, gfx.g, gfx.b, gfx.a
          gfx.set(current_bg_color.r, current_bg_color.g, current_bg_color.b, current_bg_color.a)
          gfx.rect(current_x, y, text_width, line_height)
          gfx.set(text_r, text_g, text_b, text_a)
        end

        gfx.drawstr(segment.content)
        current_x = current_x + gfx.measurestr(segment.content)
      end

      -- 如果有删除线，记录起始位置或绘制删除线
      if strike_start_x then
        local text_width = gfx.measurestr(segment.content)
        local strike_y = y + line_height / 2
        gfx.line(strike_start_x, strike_y, current_x, strike_y)
      end

    elseif segment.type == "color_start" then
      -- 保存当前颜色
      current_color = {r = gfx.r, g = gfx.g, b = gfx.b, a = gfx.a}

      -- 设置新颜色
      if segment.color_code and #segment.color_code == 6 then
        local r = tonumber(segment.color_code:sub(1, 2), 16) / 255
        local g = tonumber(segment.color_code:sub(3, 4), 16) / 255
        local b = tonumber(segment.color_code:sub(5, 6), 16) / 255
        gfx.set(r, g, b, 1.0)
      end

    elseif segment.type == "color_end" then
      -- 恢复原始颜色
      gfx.set(current_color.r, current_color.g, current_color.b, current_color.a)

    elseif segment.type == "bg_color_start" then
      -- 设置背景色
      if segment.color_code and #segment.color_code == 6 then
        local r = tonumber(segment.color_code:sub(1, 2), 16) / 255
        local g = tonumber(segment.color_code:sub(3, 4), 16) / 255
        local b = tonumber(segment.color_code:sub(5, 6), 16) / 255
        current_bg_color = {r = r, g = g, b = b, a = 1}  -- 使用半透明背景

        -- 根据背景色亮度自动设置文本颜色
        local brightness = (r * 0.299 + g * 0.587 + b * 0.114)
        if brightness < 0.7 then
          -- 深色背景使用白色文字
          gfx.set(1, 1, 1, 1)
        else
          -- 浅色背景使用黑色文字
          gfx.set(0, 0, 0, 1)
        end
      end

    elseif segment.type == "bg_color_end" then
      -- 清除背景色
      current_bg_color = nil

      -- 恢复原始文本颜色
      gfx.set(current_color.r, current_color.g, current_color.b, current_color.a)

    elseif segment.type == "strike_start" then
      -- 记录删除线起始位置
      strike_start_x = current_x

    elseif segment.type == "strike_end" then
      -- 清除删除线起始位置
      strike_start_x = nil
    end
  end
end

-- 优化版计算文本高度函数
function text_utils.calculate_text_height(text, max_width, gfx)
  -- 快速路径：空字符串或无效参数
  if not text or text == "" or not max_width or max_width <= 0 then
    return 0
  end

  -- 获取当前字体信息
  local _, _, font_size = gfx.getfont()
  font_size = font_size or 18  -- 默认字体大小

  -- 设置行高和安全边距
  local line_height = math.max(font_size + 2, math.floor(font_size * 1.2))  -- 行高至少为字体大小+2，或者字体大小的1.2倍

  -- 使用缓存检查是否有预处理过的高度信息
  local cache_key = "height_" .. utils_module.hash_string(text .. max_width .. font_size)
  local cached_height = cache_module.get(cache_key)

  if cached_height then
    return cached_height
  end

  -- 移除所有标签，测量实际的清洁文本宽度
  local clean_text = text:gsub("%[#[0-9A-Fa-f]+%]", ""):gsub("%[#%]", "")
                        :gsub("%[bg#[0-9A-Fa-f]+%]", ""):gsub("%[bg#%]", "")
                        :gsub("%[x%]", ""):gsub("%[/x%]", "")

  local clean_text_width = gfx.measurestr(clean_text)

  -- 调整最大宽度以增加安全边距
  local font_size_for_margin_calc = gfx.getfont() -- 获取字体大小用于边距计算
  local dynamic_margin_calc = math.max(4, math.floor(font_size_for_margin_calc * 0.2)) -- 动态计算边距
  local adjusted_max_width = max_width - dynamic_margin_calc  -- 使用动态计算的安全边距

  -- 快速路径：如果清洁文本宽度完全适合一行
  if clean_text_width <= adjusted_max_width then
    cache_module.add(cache_key, line_height)
    return line_height  -- 只有一行高度
  end

  -- 优化：使用预先计算的字符宽度表加速处理
  local char_width_cache = {}

  -- 分析文本，计算行数
  local lines_count = 1
  local current_width = 0
  local i = 1

  -- 跳过标签
  local function skip_tag()
    if text:sub(i, i+1) == "[#" then
      local tag_end = text:find("%]", i+2)
      if tag_end then
        i = tag_end + 1
        return true
      end
    elseif text:sub(i, i+3) == "[bg#" then
      local tag_end = text:find("%]", i+4)
      if tag_end then
        i = tag_end + 1
        return true
      end
    elseif text:sub(i, i+2) == "[x]" then
      i = i + 3
      return true
    elseif text:sub(i, i+3) == "[/x]" then
      i = i + 4
      return true
    end
    return false
  end

  while i <= #text do
    -- 检查是否是标签
    if text:sub(i, i) == "[" and skip_tag() then
      -- 已经处理了标签
      goto continue
    end

    -- 处理普通字符
    local byte = text:byte(i)
    local char_len = 1

    -- 处理UTF-8多字节字符
    if byte >= 0xC0 and byte <= 0xDF then char_len = 2
    elseif byte >= 0xE0 and byte <= 0xEF then char_len = 3
    elseif byte >= 0xF0 and byte <= 0xF7 then char_len = 4
    end

    -- 确保不会超出字符串范围
    if i + char_len - 1 > #text then
      char_len = #text - i + 1
    end

    local char = text:sub(i, i + char_len - 1)

    -- 获取字符宽度（使用缓存）
    local width = char_width_cache[char]
    if not width then
      width = gfx.measurestr(char)
      char_width_cache[char] = width
    end

    -- 检查是否需要换行
    if current_width + width > adjusted_max_width then
      lines_count = lines_count + 1
      current_width = width
    else
      current_width = current_width + width
    end

    i = i + char_len
    ::continue::
  end

  -- 计算总高度
  local total_height = lines_count * line_height

  -- 缓存结果
  cache_module.add(cache_key, total_height)

  return total_height
end

-- 添加文本清理函数
function text_utils.clean_text(text)
    if not text or type(text) ~= "string" then
        return ""
    end

    -- 移除多余的空白字符
    text = text:gsub("%s+", " ")
    -- 移除特殊字符
    text = text:gsub("[%z\1-\8\11-\12\14-\31\127]", "")
    -- 移除首尾空白
    text = text:match("^%s*(.-)%s*$")

    return text
end

-- 添加文本验证函数
function text_utils.validate_text(text)
    if not text or type(text) ~= "string" then
        return false, "文本为空或类型错误"
    end

    if #text > 10000 then
        return false, "文本过长"
    end

    return true, "文本有效"
end

-- 添加安全的文本测量函数，确保不会返回nil值
function text_utils.safe_measurestr(text, gfx)
  if not text or not gfx then
    return 0  -- 返回安全的默认值
  end

  -- 尝试测量文本
  local w = gfx.measurestr(text)

  -- 确保返回值不为nil
  if w == nil then
    return 0  -- 返回安全的默认值
  end

  return w
end

-- 计算句子高度（使用缓存提高性能）
function text_utils.calculate_sentence_height(sentence, content_font_size, font_name, content_area_width, cache_mod, gfx)
  if not sentence then return content_font_size + 4 end  -- 增加基础高度

  -- 对于特别长的句子，使用更精确的缓存键
  local cache_key
  if #sentence > 500 then
    -- 对于长句子，使用长度和哈希值作为缓存键的一部分
    local hash_value = 0
    for i = 1, #sentence, 5 do  -- 每5个字符采样一次，增加采样频率
      local char = sentence:sub(i, i)
      hash_value = hash_value + string.byte(char)
    end
    cache_key = "long_" .. #sentence .. "_" .. hash_value .. "_" .. content_font_size .. "_" .. content_area_width
  else
    -- 对于普通句子，使用完整句子作为缓存键
    cache_key = sentence .. "_" .. content_font_size .. "_" .. content_area_width
  end

  -- 使用cache_module的get_or_compute方法
  return cache_mod.get_or_compute(cache_key, function()
    gfx.setfont(1, font_name, content_font_size)

    -- 对于长句子，增加额外的安全边距
    local base_font_size_for_safety = content_font_size or 18
    local safety_margin = math.max(10, math.floor(base_font_size_for_safety * 0.5)) -- 基础安全边距，至少为10，或字体大小的一半

    -- 根据句子长度增加边距
    if #sentence > 300 then
      safety_margin = safety_margin + math.floor(base_font_size_for_safety * 0.3)
    end
    if #sentence > 500 then
      safety_margin = safety_margin + math.floor(base_font_size_for_safety * 0.5)
    end
    if #sentence > 800 then
      safety_margin = safety_margin + math.floor(base_font_size_for_safety * 0.8)
    end

    -- 计算高度时考虑安全边距
    local height = text_utils.calculate_text_height(sentence, content_area_width - safety_margin, gfx)

    -- 对于特别长的句子，额外增加空间
    local extra_padding = 0
    if #sentence > 300 then
      extra_padding = math.ceil(content_font_size * 0.5)  -- 增加额外的安全边距
    end
    if #sentence > 500 then
      extra_padding = extra_padding + math.ceil(content_font_size * 0.7)  -- 进一步大幅增加安全边距
    end
    if #sentence > 800 then
      extra_padding = extra_padding + math.ceil(content_font_size * 1.0)  -- 对于特别长的句子，再显著增加安全边距
    end

    return height + extra_padding
  end)
end

-- 优化版绘制单个句子函数
function text_utils.draw_sentence(sentence_idx, sentences, content_area, y_offset, is_selected, is_hover, content_font_size, font_name, style_module, gfx)
  -- 安全检查
  if not sentences or not sentences[sentence_idx] then
    return y_offset
  end

  -- 获取句子文本
  local display_text = sentences[sentence_idx]

  -- 计算可见区域的边界
  local visible_area_bottom = content_area.y + content_area.h

  -- 设置安全边距
  local base_font_size_for_list_margin = content_font_size or 18
  local safety_margin = math.max(3, math.floor(base_font_size_for_list_margin * 0.15)) -- 调整列表项的动态安全边距比例

  -- 设置字体
  gfx.setfont(1, font_name, content_font_size)

  -- 计算最大宽度，减少安全边距
  local font_size_for_draw_margin = content_font_size or 18
  local draw_safety_margin = math.max(5, math.floor(font_size_for_draw_margin * 0.25)) -- 动态计算绘制时的安全边距
  local max_width = content_area.w - draw_safety_margin  -- 使用动态安全边距

  -- 计算句子高度
  local sentence_height = text_utils.calculate_text_height(display_text, max_width, gfx)

  -- 计算句子的Y坐标
  local sentence_y = content_area.y + y_offset

  -- 计算句子底部位置
  local sentence_bottom = sentence_y + sentence_height

  -- 如果句子完全在可见区域外，则跳过绘制
  if sentence_bottom < content_area.y or sentence_y > visible_area_bottom then
    return y_offset + sentence_height + safety_margin
  end

  -- 检查是否为章节标题（优先检查，因为章节有特殊渲染）
  local is_chapter = false
  local original_font_name = font_name
  local original_font_size = content_font_size
  local chapter_font_size = content_font_size + 2  -- 章节标题字体增大2号
  local chapter_display_text = display_text

  -- 首先检查是否包含章节标记
  local chapter_content = display_text:match("%[CHAPTER%](.-)%[/CHAPTER%]")
  if chapter_content then
    is_chapter = true
    chapter_display_text = chapter_content  -- 用于显示的文本，去除标记
  elseif display_text:match("^%s*第%s*[%d一二三四五六七八九十百千]+%s*[章节集话回]") or
         display_text:match("^%s*章节%s*[%d一二三四五六七八九十百千]+") then
    is_chapter = true
    chapter_display_text = display_text  -- 保持原文本
  end

  -- 绘制背景（章节背景优先于选中背景）
  if is_chapter then
    -- 为章节标题绘制蓝色高亮背景，参照鼠标高亮的宽度
    local highlight_width = content_area.w - 12  -- 参照鼠标高亮的宽度
    local highlight_x = content_area.x + 1  -- 向右移动2像素
    local highlight_y = sentence_y - 4  -- 向上扩展2像素
    local highlight_h = sentence_height + 8  -- 增加高度到8像素

    -- 限制高亮区域不超出可见区域边界
    highlight_h = math.min(highlight_h, visible_area_bottom - highlight_y)

    -- 只有当高亮区域有效时才绘制
    if highlight_h > 0 then
      style_module.draw_metallic_highlight(
        highlight_x, highlight_y,
        highlight_width, highlight_h,
        style_module.highlight_colors.blue.selected, true)

      -- 为章节标题添加白色边框
      gfx.set(1, 1, 1, 1)  -- 白色边框
      -- 绘制边框（上、下、左、右）
      gfx.rect(highlight_x, highlight_y, highlight_width, 1)  -- 上边框
      gfx.rect(highlight_x, highlight_y + highlight_h - 1, highlight_width, 1)  -- 下边框
      gfx.rect(highlight_x, highlight_y, 1, highlight_h)  -- 左边框
      gfx.rect(highlight_x + highlight_width - 1, highlight_y, 1, highlight_h)  -- 右边框
    end

    -- 设置章节标题的白色文字
    gfx.set(1, 1, 1, 1)  -- 白色文本用于章节标题

    -- 设置章节标题的字体大小
    gfx.setfont(1, font_name, chapter_font_size)
  elseif is_selected or is_hover then
    -- 非章节的普通背景绘制
    if is_selected then
      style_module.draw_metallic_highlight(
        content_area.x, sentence_y - 2,
        content_area.w - 12, math.min(sentence_height + 4, visible_area_bottom - sentence_y + 2),
        style_module.highlight_colors.blue.selected, true)
    else
      style_module.draw_metallic_highlight(
        content_area.x, sentence_y - 2,
        content_area.w - 12, math.min(sentence_height + 4, visible_area_bottom - sentence_y + 2),
        style_module.highlight_colors.green.hover, false)
    end

    style_module.set_color(style_module.colors.content_text)  -- 黑色文本用于普通句子
    -- 设置普通句子的字体
    gfx.setfont(1, font_name, content_font_size)
  else
    style_module.set_color(style_module.colors.content_text)  -- 黑色文本用于普通句子
    -- 设置普通句子的字体
    gfx.setfont(1, font_name, content_font_size)
  end

  local draw_x = content_area.x + 5
  local final_display_text = display_text

  if is_chapter then
    -- 使用去除标记的章节文本
    final_display_text = chapter_display_text
    -- 重新计算文本宽度（使用章节标题的字体大小）
    local text_width_clean = gfx.measurestr(final_display_text:gsub("%[.-%]", "")) -- 移除标签后测量
    if text_width_clean < max_width - safety_margin then
      draw_x = content_area.x + (content_area.w - text_width_clean) / 2
    end
  end

  -- 绘制文本，确保传递正确的可见区域边界
  text_utils.wrap_text(
    final_display_text,
    max_width - safety_margin,
    draw_x, -- 使用计算后的x坐标
    sentence_y,
    true,
    content_area.y,
    visible_area_bottom,
    gfx
  )

  -- 返回下一个句子的起始位置
  return y_offset + sentence_height + safety_margin
end

-- 优化版绘制句子列表函数
function text_utils.draw_sentences_list(sentences, content_area, sentence_scroll_pos, hover_sentence_idx, selected_indices, content_font_size, font_name, cached_sentence_heights, cached_total_content_height, style_module, gfx, search_keyword, highlight_color)
  if #sentences == 0 then return 0 end

  -- 计算可见区域高度
  local visible_area_height = content_area.h - 10
  local font_size_for_draw_margin = content_font_size or 18
  local draw_safety_margin = math.max(5, math.floor(font_size_for_draw_margin * 0.25)) -- 动态计算绘制时的安全边距
  local max_width = content_area.w - draw_safety_margin  -- 使用动态安全边距

  -- 定义可见区域的明确边界
  local visible_top = content_area.y
  local visible_bottom = content_area.y + content_area.h

  -- 设置字体（只设置一次）
  gfx.setfont(1, font_name, content_font_size)

  -- 设置安全边距
  local base_font_size_for_list_margin = content_font_size or 18
  local safety_margin = math.max(3, math.floor(base_font_size_for_list_margin * 0.15)) -- 调整列表项的动态安全边距比例

  -- 计算可见项目数（估计值）
  local estimated_line_height = content_font_size + safety_margin
  local visible_items_count = math.floor(visible_area_height / estimated_line_height)
  visible_items_count = math.max(1, visible_items_count)

  -- 计算最大滚动位置
  local extra_scroll_buffer = math.floor(visible_items_count * 0.8)
  local max_scroll = math.max(0, #sentences - visible_items_count + extra_scroll_buffer)

  -- 确保滚动位置在有效范围内
  sentence_scroll_pos = math.max(0, math.min(max_scroll, sentence_scroll_pos))

  -- 计算总内容高度（使用缓存）
  local total_content_height = cached_total_content_height

  -- 如果没有缓存的总高度，计算它
  if not total_content_height then
    -- 使用缓存键
    local cache_key = "total_height_" .. #sentences .. "_" .. content_font_size
    local cached_height = cache_module.get(cache_key)

    if cached_height then
      total_content_height = cached_height
    else
      -- 计算总高度
      total_content_height = 0

      -- 预加载可见区域的句子高度
      local start_idx = math.floor(sentence_scroll_pos) + 1
      local end_idx = math.min(#sentences, start_idx + visible_items_count * 2)

      -- 首先计算可见区域的高度
      for i = start_idx, end_idx do
        if sentences[i] and sentences[i] ~= "__SKIP_THIS_SENTENCE__" then
          -- 获取或计算句子高度
          if not cached_sentence_heights[i] then
            cached_sentence_heights[i] = text_utils.calculate_text_height(
              sentences[i], max_width, gfx)
          end
          total_content_height = total_content_height + cached_sentence_heights[i] + safety_margin
        end
      end

      -- 估算剩余句子的高度
      local avg_height = 0
      local count = 0

      -- 计算已知句子的平均高度
      for i, height in pairs(cached_sentence_heights) do
        if height > 0 then
          avg_height = avg_height + height
          count = count + 1
        end
      end

      -- 如果有已知高度，使用平均值估算剩余句子
      if count > 0 then
        avg_height = avg_height / count

        -- 估算未计算句子的高度
        local remaining_sentences = #sentences - (end_idx - start_idx + 1)
        if remaining_sentences > 0 then
          total_content_height = total_content_height +
            remaining_sentences * (avg_height + safety_margin)
        end
      else
        -- 如果没有已知高度，使用默认估计
        local default_height = content_font_size + 10
        total_content_height = #sentences * (default_height + safety_margin)
      end

      -- 缓存计算结果
      cache_module.add(cache_key, total_content_height)
    end
  end

  -- 绘制滚动条
  if total_content_height > visible_area_height then
    local scrollbar = {
      x = content_area.x + content_area.w - 10,
      y = content_area.y,
      w = 10,
      h = content_area.h
    }

    -- 使用style_module的滚动条绘制函数
    style_module.draw_scrollbar(scrollbar, sentence_scroll_pos, max_scroll, visible_items_count, #sentences)
  end

  -- 计算起始索引和偏移量
  local start_idx = math.floor(sentence_scroll_pos) + 1
  local y_offset = math.max(2, math.floor(base_font_size_for_list_margin * 0.1)) - (sentence_scroll_pos % 1) * estimated_line_height -- 动态计算初始y_offset

  -- 绘制可见句子
  local current_y = y_offset
  local last_visible_idx = 0

  for i = start_idx, #sentences do
    -- 检查是否超出可见区域底部
    if content_area.y + current_y > visible_bottom then
      break  -- 超出可见区域底部，停止绘制
    end

    -- 跳过标记为不显示的句子
    if not sentences[i] or sentences[i] == "__SKIP_THIS_SENTENCE__" then
      goto continue
    end

    -- 检查是否被选中或悬停
    local is_selected = false
    if selected_indices then
      for _, idx in ipairs(selected_indices) do
        if idx == i then
          is_selected = true
          break
        end
      end
    end
    local is_hover = (i == hover_sentence_idx)

    -- 获取句子高度（使用缓存）
    if not cached_sentence_heights[i] then
      cached_sentence_heights[i] = text_utils.calculate_text_height(
        sentences[i], max_width, gfx)
    end
    local sentence_height = cached_sentence_heights[i]

    -- 绘制句子
    local sentence_y = content_area.y + current_y

    -- 计算句子底部位置，用于可见性检查
    local sentence_bottom = sentence_y + sentence_height

    -- 只有当句子至少部分可见时才绘制
    if sentence_bottom >= visible_top and sentence_y <= visible_bottom then
      -- 绘制背景（如果需要）
      if is_selected or is_hover then
        if is_selected then
          style_module.draw_metallic_highlight(
            content_area.x, sentence_y - 2,
            content_area.w - 12, math.min(sentence_height + 4, visible_bottom - sentence_y + 2),
            style_module.highlight_colors.blue.selected, true)
        elseif is_hover then
          style_module.draw_metallic_highlight(
            content_area.x, sentence_y - 2,
            content_area.w - 12, math.min(sentence_height + 4, visible_bottom - sentence_y + 2),
            style_module.highlight_colors.green.hover, false)
        end
      end

      -- 检查是否为章节标题
      local is_chapter = false
      local chapter_font_size = content_font_size + 2  -- 章节标题字体增大2号
      local chapter_display_text = sentences[i]

      -- 首先检查是否包含章节标记
      local chapter_content = sentences[i]:match("%[CHAPTER%](.-)%[/CHAPTER%]")
      if chapter_content then
        is_chapter = true
        chapter_display_text = chapter_content  -- 用于显示的文本，去除标记
      elseif sentences[i]:match("^%s*第%s*[%d一二三四五六七八九十百千]+%s*[章节集话回]") or
             sentences[i]:match("^%s*章节%s*[%d一二三四五六七八九十百千]+") then
        is_chapter = true
      end

      if is_chapter then

        -- 为章节标题绘制蓝色高亮背景，参照鼠标高亮的宽度
          local highlight_width = content_area.w - 12  -- 参照鼠标高亮的宽度
          local highlight_x = content_area.x + 2  -- 向右移动2像素
          local highlight_y = sentence_y - 4  -- 向上扩展2像素
          local highlight_h = sentence_height + safety_margin + 4  -- 增加高度

          -- 限制高亮区域不超出可见区域边界
          highlight_h = math.min(highlight_h, visible_bottom - highlight_y)

          -- 只有当高亮区域有效时才绘制
          if highlight_h > 0 then
            style_module.draw_metallic_highlight(
              highlight_x, highlight_y,
              highlight_width, highlight_h,
              style_module.highlight_colors.blue.selected, true)

            -- 为章节标题添加白色边框
            gfx.set(1, 1, 1, 1)  -- 白色边框
            -- 绘制边框（上、下、左、右）
            gfx.rect(highlight_x, highlight_y, highlight_width, 1)  -- 上边框
            gfx.rect(highlight_x, highlight_y + highlight_h - 1, highlight_width, 1)  -- 下边框
            gfx.rect(highlight_x, highlight_y, 1, highlight_h)  -- 左边框
            gfx.rect(highlight_x + highlight_width - 1, highlight_y, 1, highlight_h)  -- 右边框
          end

        -- 设置章节标题的白色文字和字体大小
        gfx.set(1, 1, 1, 1)  -- 白色文本用于章节标题
        gfx.setfont(1, font_name, chapter_font_size)
      else
        style_module.set_color(style_module.colors.content_text)  -- 黑色文本用于普通句子
        gfx.setfont(1, font_name, content_font_size)
      end

      -- 绘制文本，确保传递正确的可见区域边界
      local display_text = is_chapter and chapter_display_text or sentences[i]
      text_utils.wrap_text(
        display_text,
        max_width - safety_margin,
        content_area.x + 5,
        sentence_y,
        true,
        visible_top,
        visible_bottom,
        gfx,
        search_keyword,
        highlight_color
      )

      last_visible_idx = i
    end

    -- 更新Y偏移
    current_y = current_y + sentence_height + safety_margin

    ::continue::
  end

  -- 返回总内容高度（用于其他函数）
  return total_content_height
end

-- 清理角色CV文本中的标签
function text_utils.clean_cv_role_tags(text)
  if not text or text == "" then
    return text
  end

  -- 清理[bg#XXXXXX]和[bg#]标签
  local cleaned_text = text

  -- 移除背景色标签
  cleaned_text = cleaned_text:gsub("%[bg#[0-9A-Fa-f]+%]", "")
  cleaned_text = cleaned_text:gsub("%[bg#%]", "")

  -- 移除前景色标签
  cleaned_text = cleaned_text:gsub("%[#[0-9A-Fa-f]+%]", "")
  cleaned_text = cleaned_text:gsub("%[#%]", "")

  -- 移除删除线标签
  cleaned_text = cleaned_text:gsub("%[x%]", "")
  cleaned_text = cleaned_text:gsub("%[/x%]", "")

  return cleaned_text
end

-- 为了保持与原text_module.lua接口兼容，提供相同的返回格式
return {
  -- 原text_module.lua的函数
  get_clipboard = function()
    local clipboard, err = text_utils.get_clipboard()
    if clipboard then
      return true, clipboard
    else
      return false, err or "剪贴板内容为空"
    end
  end,
  read_text_file = text_utils.read_text_file,
  parse_sentences = text_utils.parse_sentences,
  extract_cv_role_pairs = text_utils.extract_cv_role_pairs,
  extract_cv_role_from_text = text_utils.extract_cv_role_from_text,
  format_time = text_utils.format_time,
  calculate_text_height_simple = text_utils.calculate_text_height_simple,
  wrap_text_simple = text_utils.wrap_text_simple,
  wrap_text = text_utils.wrap_text,
  calculate_text_height = text_utils.calculate_text_height,
  clean_text = text_utils.clean_text,
  validate_text = text_utils.validate_text,
  safe_measurestr = text_utils.safe_measurestr,  -- 添加安全测量函数
  find_cv_role_index = text_utils.find_cv_role_index,
  handle_cv_role_selection = text_utils.handle_cv_role_selection,

  -- 新增的句子处理函数
  calculate_sentence_height = text_utils.calculate_sentence_height,
  draw_sentence = text_utils.draw_sentence,
  draw_sentences_list = text_utils.draw_sentences_list,
  clean_cv_role_tags = text_utils.clean_cv_role_tags
}