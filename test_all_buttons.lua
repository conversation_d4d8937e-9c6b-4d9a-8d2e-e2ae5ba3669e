-- 测试所有按钮显示的脚本
-- 这个脚本可以在REAPER中直接运行

-- 导入必要的库
local r = reaper

-- 获取脚本路径
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"

-- 安全加载函数
local function safe_load_module(file_path)
  local success, result = pcall(dofile, file_path)
  if not success then
    r.ShowConsoleMsg("模块加载失败: " .. file_path .. " - " .. tostring(result) .. "\n")
    return {}
  end
  return result
end

-- 测试函数
local function test_all_buttons()
  r.ShowConsoleMsg("=== 测试所有按钮显示 ===\n")
  
  -- 加载所有模块
  local utils_module = safe_load_module(script_dir .. "utils_module.lua")
  local style_module = safe_load_module(script_dir .. "style_module.lua")
  local text_utils = safe_load_module(script_dir .. "text_utils.lua")
  local button_module = safe_load_module(script_dir .. "button_module.lua")
  local ui_module = safe_load_module(script_dir .. "ui_module.lua")
  local event_module = safe_load_module(script_dir .. "event_module.lua")
  
  if not utils_module or not style_module or not text_utils or not button_module or not ui_module or not event_module then
    r.ShowConsoleMsg("✗ 模块加载失败\n")
    return false
  end
  r.ShowConsoleMsg("✓ 所有模块加载成功\n")
  
  -- 初始化模块
  style_module.init({utils_module = utils_module})
  
  local deps = {
    style_module = style_module,
    text_utils = text_utils,
    button_module = button_module,
    utils_module = utils_module,
    gfx = gfx,
    r = r
  }
  
  ui_module.init(deps)
  deps.ui_module = ui_module
  event_module.init(deps)
  
  if button_module and button_module.init then
    button_module.init({
      utils_module = utils_module,
      text_utils = text_utils,
      style_module = style_module
    })
  end
  
  r.ShowConsoleMsg("✓ 所有模块初始化成功\n")
  
  -- 创建应用状态
  local app_state = utils_module.app_state.create()
  r.ShowConsoleMsg("✓ 应用状态创建成功\n")
  
  -- 获取UI元素
  local ui_elements = style_module.init_ui_elements()
  r.ShowConsoleMsg("✓ UI元素创建成功\n")
  
  -- 检查所有按钮是否存在
  local expected_buttons = {
    -- 主要功能按钮
    "play_button",
    "block_mark_button", 
    "region_mark_button",
    "excel_button",
    
    -- 速率控制按钮
    "rate_minus_button",
    "rate_display_area",
    "rate_plus_button", 
    "rate_reset_button",
    
    -- 文档和剪贴板按钮
    "document_button",
    "clipboard_button",
    
    -- 字体控制按钮
    "font_decrease_button",
    "font_increase_button",
    "font_size_display",
    "font_size_label",
    
    -- 工具按钮
    "open_csv_button",
    "au_button",
    "region_name_button",
    "file_name_button", 
    "track_color_button",
    "track_split_button",
    
    -- 特殊功能按钮
    "track_align_button",
    "chapter_button"
  }
  
  local missing_buttons = {}
  local existing_buttons = {}
  
  for _, button_name in ipairs(expected_buttons) do
    if ui_elements[button_name] then
      table.insert(existing_buttons, button_name)
      r.ShowConsoleMsg("✓ " .. button_name .. " 存在\n")
    else
      table.insert(missing_buttons, button_name)
      r.ShowConsoleMsg("✗ " .. button_name .. " 缺失\n")
    end
  end
  
  r.ShowConsoleMsg("\n=== 按钮统计 ===\n")
  r.ShowConsoleMsg("存在的按钮: " .. #existing_buttons .. " 个\n")
  r.ShowConsoleMsg("缺失的按钮: " .. #missing_buttons .. " 个\n")
  
  if #missing_buttons > 0 then
    r.ShowConsoleMsg("\n缺失的按钮列表:\n")
    for _, button_name in ipairs(missing_buttons) do
      r.ShowConsoleMsg("- " .. button_name .. "\n")
    end
  end
  
  -- 测试按钮绘制函数是否存在
  r.ShowConsoleMsg("\n=== 测试按钮绘制函数 ===\n")
  
  local button_functions = {
    "draw_play_button",
    "draw_block_mark_button",
    "draw_region_mark_button", 
    "draw_excel_button",
    "draw_rate_buttons",
    "draw_font_buttons",
    "draw_document_buttons",
    "draw_open_au_buttons",
    "draw_track_align_button",
    "draw_chapter_button"
  }
  
  local missing_functions = {}
  local existing_functions = {}
  
  for _, func_name in ipairs(button_functions) do
    if button_module[func_name] then
      table.insert(existing_functions, func_name)
      r.ShowConsoleMsg("✓ " .. func_name .. " 存在\n")
    else
      table.insert(missing_functions, func_name)
      r.ShowConsoleMsg("✗ " .. func_name .. " 缺失\n")
    end
  end
  
  r.ShowConsoleMsg("\n=== 绘制函数统计 ===\n")
  r.ShowConsoleMsg("存在的绘制函数: " .. #existing_functions .. " 个\n")
  r.ShowConsoleMsg("缺失的绘制函数: " .. #missing_functions .. " 个\n")
  
  if #missing_functions > 0 then
    r.ShowConsoleMsg("\n缺失的绘制函数列表:\n")
    for _, func_name in ipairs(missing_functions) do
      r.ShowConsoleMsg("- " .. func_name .. "\n")
    end
  end
  
  -- 测试实际绘制
  r.ShowConsoleMsg("\n=== 测试实际绘制 ===\n")
  
  local render_success, render_error = pcall(function()
    ui_module.init_window()
    ui_module.render(app_state)
  end)
  
  if render_success then
    r.ShowConsoleMsg("✓ UI渲染成功 - 所有按钮应该都能正确显示\n")
  else
    r.ShowConsoleMsg("✗ UI渲染失败: " .. tostring(render_error) .. "\n")
    return false
  end
  
  return #missing_buttons == 0 and #missing_functions == 0
end

-- 主函数
local function main()
  local test_success = test_all_buttons()
  
  if test_success then
    r.ShowConsoleMsg("\n🎉 所有按钮测试通过！\n")
    r.ShowConsoleMsg("所有按钮都已正确定义和实现\n")
    r.ShowConsoleMsg("现在mark_new.lua应该显示所有功能按钮\n")
  else
    r.ShowConsoleMsg("\n❌ 按钮测试失败\n")
    r.ShowConsoleMsg("有按钮或绘制函数缺失，需要补充\n")
  end
end

-- 运行测试
main()
