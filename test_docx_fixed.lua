-- 修复版DOCX文件支持测试脚本
-- 验证修复后的文档读取功能是否支持.docx文件

-- 导入必要的库
local r = reaper

-- 获取脚本路径
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"

-- 安全加载函数
local function safe_load_module(file_path)
  local success, result = pcall(dofile, file_path)
  if not success then
    r.ShowConsoleMsg("模块加载失败: " .. file_path .. " - " .. tostring(result) .. "\n")
    return nil
  end
  return result
end

-- 字符串trim函数
local function trim(s)
  if not s then return "" end
  return s:match("^%s*(.-)%s*$") or ""
end

-- 直接测试文件对话框
local function test_file_dialog()
  r.ShowConsoleMsg("=== 测试文件对话框 ===\n")
  
  -- 测试不同的文件过滤器格式
  local filters = {
    -- 格式1: 标准Windows格式
    "所有支持的文件\0*.txt;*.docx\0文本文件 (*.txt)\0*.txt\0Word文档 (*.docx)\0*.docx\0所有文件 (*.*)\0*.*\0\0",
    -- 格式2: 简化格式
    "Word文档 (*.docx)\0*.docx\0文本文件 (*.txt)\0*.txt\0所有文件 (*.*)\0*.*\0\0",
    -- 格式3: 最简格式
    "*.docx\0*.docx\0*.txt\0*.txt\0*.*\0*.*\0\0"
  }
  
  for i, filter in ipairs(filters) do
    r.ShowConsoleMsg("测试过滤器格式 " .. i .. "...\n")
    
    local retval, file_path = r.GetUserFileNameForRead("", "选择文档文件 (格式" .. i .. ")", filter)
    
    if retval and file_path and file_path ~= "" then
      r.ShowConsoleMsg("✓ 成功选择文件: " .. file_path .. "\n")
      
      -- 检查文件类型
      if file_path:lower():match("%.docx$") then
        r.ShowConsoleMsg("✓ 检测到DOCX文件\n")
      elseif file_path:lower():match("%.txt$") then
        r.ShowConsoleMsg("✓ 检测到TXT文件\n")
      else
        r.ShowConsoleMsg("✓ 检测到其他文件类型\n")
      end
      
      return file_path
    else
      r.ShowConsoleMsg("✗ 用户取消或未选择文件\n")
    end
  end
  
  return nil
end

-- 主函数
local function main()
  r.ShowConsoleMsg("=== 修复版DOCX文件支持测试 ===\n")
  
  -- 首先测试文件对话框
  local selected_file = test_file_dialog()
  
  if not selected_file then
    r.ShowConsoleMsg("没有选择文件，退出测试\n")
    return
  end
  
  -- 加载必要的模块
  local button_module = safe_load_module(script_dir .. "button_module.lua")
  
  if not button_module then
    r.ShowConsoleMsg("✗ button_module加载失败\n")
    return
  end
  
  r.ShowConsoleMsg("✓ button_module加载成功\n")
  
  -- 检查关键函数是否存在
  if not button_module.handle_document_button then
    r.ShowConsoleMsg("✗ handle_document_button函数不存在\n")
    return
  end
  
  r.ShowConsoleMsg("✓ handle_document_button函数存在\n")
  
  -- 测试文档读取功能
  r.ShowConsoleMsg("测试文档读取功能...\n")
  
  -- 创建测试回调函数
  local test_content = ""
  local test_sentences = {}
  local test_cv_pairs = {}
  
  local callbacks = {
    handle_text_content = function(content)
      test_content = content or ""
      r.ShowConsoleMsg("✓ 接收到文档内容，长度: " .. #test_content .. " 字符\n")
    end,
    
    parse_sentences = function()
      -- 简单的句子分割测试
      if test_content and test_content ~= "" then
        -- 使用简单的分割方法
        local sentences = {}
        for line in test_content:gmatch("[^\r\n]+") do
          local trimmed = trim(line)
          if trimmed ~= "" then
            table.insert(sentences, trimmed)
          end
        end
        
        -- 如果没有换行，尝试按标点分割
        if #sentences == 0 then
          for sentence in test_content:gmatch("[^。！？]+[。！？]?") do
            local trimmed = trim(sentence)
            if trimmed ~= "" then
              table.insert(sentences, trimmed)
            end
          end
        end
        
        test_sentences = sentences
        r.ShowConsoleMsg("✓ 解析了 " .. #test_sentences .. " 个句子\n")
      end
    end,
    
    extract_cv_role_pairs = function()
      -- 简单的CV角色对提取测试
      if test_content and test_content ~= "" then
        local pairs = {}
        for cv_role in test_content:gmatch("【(.-)】") do
          if cv_role:find("-") or cv_role:find("－") then
            local cv, role = cv_role:match("(.-)[-－](.+)")
            if cv and role then
              table.insert(pairs, {cv = trim(cv), role = trim(role)})
            end
          end
        end
        test_cv_pairs = pairs
        r.ShowConsoleMsg("✓ 提取了 " .. #test_cv_pairs .. " 个CV角色对\n")
      end
    end
  }
  
  -- 直接测试选中的文件
  r.ShowConsoleMsg("直接处理选中的文件: " .. selected_file .. "\n")
  
  -- 检查文件类型并处理
  local content = ""
  local success = false
  
  if selected_file:lower():match("%.docx$") then
    r.ShowConsoleMsg("处理DOCX文件...\n")
    
    -- 尝试加载word_module
    local word_module = safe_load_module(script_dir .. "word_module.lua")
    
    if word_module and word_module.parse_docx then
      r.ShowConsoleMsg("✓ word_module加载成功\n")
      
      local parsed_content = word_module.parse_docx(selected_file)
      
      if parsed_content and type(parsed_content) == "string" and not parsed_content:match("^无法") then
        content = parsed_content
        success = true
        r.ShowConsoleMsg("✓ DOCX文件解析成功\n")
      else
        r.ShowConsoleMsg("✗ DOCX文件解析失败: " .. (parsed_content or "未知错误") .. "\n")
      end
    else
      r.ShowConsoleMsg("✗ word_module加载失败或parse_docx函数不存在\n")
    end
  else
    r.ShowConsoleMsg("处理文本文件...\n")
    
    local file = io.open(selected_file, "r")
    if file then
      content = file:read("*all") or ""
      file:close()
      success = true
      r.ShowConsoleMsg("✓ 文本文件读取成功\n")
    else
      r.ShowConsoleMsg("✗ 无法打开文件: " .. selected_file .. "\n")
    end
  end
  
  if success and content ~= "" then
    -- 调用回调函数处理内容
    callbacks.handle_text_content(content)
    callbacks.parse_sentences()
    callbacks.extract_cv_role_pairs()
    
    -- 显示测试结果
    r.ShowConsoleMsg("\n=== 测试结果 ===\n")
    r.ShowConsoleMsg("✓ 文档内容读取成功\n")
    r.ShowConsoleMsg("✓ 内容长度: " .. #test_content .. " 字符\n")
    r.ShowConsoleMsg("✓ 句子数量: " .. #test_sentences .. " 个\n")
    r.ShowConsoleMsg("✓ CV角色对数量: " .. #test_cv_pairs .. " 个\n")
    
    -- 显示前几个句子作为示例
    if #test_sentences > 0 then
      r.ShowConsoleMsg("\n前3个句子示例:\n")
      for i = 1, math.min(3, #test_sentences) do
        r.ShowConsoleMsg(i .. ". " .. test_sentences[i] .. "\n")
      end
    end
    
    -- 显示CV角色对作为示例
    if #test_cv_pairs > 0 then
      r.ShowConsoleMsg("\nCV角色对示例:\n")
      for i = 1, math.min(3, #test_cv_pairs) do
        r.ShowConsoleMsg(i .. ". CV: " .. test_cv_pairs[i].cv .. ", 角色: " .. test_cv_pairs[i].role .. "\n")
      end
    end
    
    -- 显示内容预览
    local preview = test_content:sub(1, 200)
    if #test_content > 200 then
      preview = preview .. "..."
    end
    r.ShowConsoleMsg("\n内容预览:\n" .. preview .. "\n")
    
    -- 检查是否包含格式标签
    local has_color_tags = test_content:find("%[#") ~= nil
    local has_strike_tags = test_content:find("%[x%]") ~= nil
    local has_chapter_tags = test_content:find("%[CHAPTER%]") ~= nil
    
    r.ShowConsoleMsg("\n格式标签检查:\n")
    r.ShowConsoleMsg("颜色标签: " .. (has_color_tags and "✓ 发现" or "✗ 未发现") .. "\n")
    r.ShowConsoleMsg("删除线标签: " .. (has_strike_tags and "✓ 发现" or "✗ 未发现") .. "\n")
    r.ShowConsoleMsg("章节标签: " .. (has_chapter_tags and "✓ 发现" or "✗ 未发现") .. "\n")
    
    r.ShowConsoleMsg("\n🎉 DOCX文件支持测试成功！\n")
    r.ShowConsoleMsg("✅ 文档读取功能正常工作\n")
    r.ShowConsoleMsg("✅ 内容解析功能正常\n")
    r.ShowConsoleMsg("✅ CV角色对提取功能正常\n")
    
  else
    r.ShowConsoleMsg("\n✗ 文档处理失败\n")
  end
  
  r.ShowConsoleMsg("\n=== 功能验证说明 ===\n")
  r.ShowConsoleMsg("1. 文件对话框应该能显示.docx文件\n")
  r.ShowConsoleMsg("2. DOCX文件应该能正确解析内容和格式标签\n")
  r.ShowConsoleMsg("3. TXT文件应该能正确读取纯文本内容\n")
  r.ShowConsoleMsg("4. CV角色对应该能正确提取【角色-CV】格式\n")
  r.ShowConsoleMsg("5. 句子应该能正确分割和解析\n")
  r.ShowConsoleMsg("6. 格式标签（颜色、删除线等）应该被保留\n")
  
  r.ShowConsoleMsg("\n=== 测试完成 ===\n")
end

-- 运行测试
main()
