-- 事件模块 - 负责所有事件处理逻辑
-- 从mark.lua中提取的事件相关功能

local event_module = {}

-- 字符串trim函数
local function trim(s)
  if not s then return "" end
  return s:match("^%s*(.-)%s*$") or ""
end

-- 依赖的模块（通过init函数注入）
local ui_module, button_module, text_utils, utils_module, style_module, gfx, r

-- 事件处理状态
local event_state = {
  last_mouse_x = 0,
  last_mouse_y = 0,
  last_mouse_state = 0,
  last_click_time = 0,
  last_wheel_time = 0,
  click_cooldown = 0.01,
  wheel_cooldown = 0.005,  -- 减少滚轮冷却时间，提高响应性
  is_dragging_sentence_scrollbar = false,
  is_dragging_cv_role_scrollbar = false,
  is_dragging_chapter_scrollbar = false,
  is_dragging_selection_scrollbar = false,
  is_mouse_down = false,
  is_ctrl_down = false,

  -- 音频块选择变化监听
  last_selected_item = nil,
  last_selected_item_name = "",
  last_selection_check_time = 0,
  item_selection_change_cooldown = 0.1,  -- 选择变化检查冷却时间

  -- 平滑滚动状态
  smooth_scroll = {
    content_target = 0,
    content_current = 0,
    cv_role_target = 0,
    cv_role_current = 0,
    chapter_target = 0,
    chapter_current = 0,
    selection_target = 0,
    selection_current = 0,
    smoothing_factor = 0.3,   -- 平滑系数，进一步提高响应速度
    min_delta = 0.1   -- 最小变化量，进一步减少微小变化
  }
}

-- 初始化事件模块
function event_module.init(deps)
  -- 注入依赖
  ui_module = deps.ui_module
  button_module = deps.button_module
  text_utils = deps.text_utils
  utils_module = deps.utils_module
  style_module = deps.style_module
  gfx = deps.gfx
  r = deps.r

  return event_module
end

-- 主事件处理函数
function event_module.handle_events(app_state)
  local status, err = pcall(function()
    -- 处理鼠标事件
    event_module.handle_mouse_events(app_state)

    -- 处理键盘事件
    event_module.handle_keyboard_events(app_state)

    -- 处理窗口事件
    event_module.handle_window_events(app_state)

    -- 更新按钮状态
    event_module.update_button_states(app_state)

    -- 检查音频块选择变化并自动更新集数
    event_module.check_selection_changed(app_state)
  end)

  if not status then
    utils_module.error_handler:add("事件处理失败: " .. tostring(err), "error", "event_module.handle_events")
    r.ShowConsoleMsg("Event handling error: " .. tostring(err) .. "\n")
  end
end

-- 处理鼠标事件
function event_module.handle_mouse_events(app_state)
  local mouse_x, mouse_y = gfx.mouse_x, gfx.mouse_y
  local mouse_cap = gfx.mouse_cap
  local wheel = gfx.mouse_wheel
  local current_time = r.time_precise()

  -- 更新鼠标位置记录
  event_state.last_mouse_x, event_state.last_mouse_y = mouse_x, mouse_y

  -- 更新平滑滚动（每帧都更新）
  event_module.update_smooth_scroll(app_state)

  -- 处理滚轮事件
  if wheel ~= 0 and (current_time - event_state.last_wheel_time) > event_state.wheel_cooldown then
    event_module.handle_wheel_events(app_state, mouse_x, mouse_y, wheel)
    event_state.last_wheel_time = current_time
    gfx.mouse_wheel = 0  -- 重置滚轮值
  end

  -- 处理鼠标点击事件
  if mouse_cap ~= event_state.last_mouse_state and (current_time - event_state.last_click_time) > event_state.click_cooldown then
    event_module.handle_click_events(app_state, mouse_x, mouse_y, mouse_cap)
    event_state.last_click_time = current_time
  end

  -- 处理鼠标拖拽事件
  event_module.handle_drag_events(app_state, mouse_x, mouse_y, mouse_cap)

  -- 处理鼠标悬停事件
  event_module.handle_hover_events(app_state, mouse_x, mouse_y)

  -- 更新鼠标状态
  event_state.last_mouse_state = mouse_cap
  event_state.is_mouse_down = (mouse_cap & 1) == 1
  event_state.is_ctrl_down = (mouse_cap & 4) == 4
end

-- 处理滚轮事件
function event_module.handle_wheel_events(app_state, mouse_x, mouse_y, wheel)
  local ui = ui_module.get_ui_elements()
  if not ui then return end

  -- 改进的滚动量计算 - 大幅减少滚动速度
  local base_scroll_amount = wheel * 0.2   -- 基础滚动量（大幅减少）
  local smooth_scroll_amount = wheel * 0.15 -- 平滑滚动量（大幅减少）

  -- 检查鼠标在哪个区域
  if event_module.is_point_in_rect(mouse_x, mouse_y, ui.content_area) then
    -- 内容区域滚动
    local line_height = style_module.content_font_size + 5
    local visible_items = math.floor((ui.content_area.h - 10) / line_height)
    local max_scroll = math.max(0, #app_state.sentences - visible_items)

    -- 使用平滑滚动
    event_state.smooth_scroll.content_target = math.max(0, math.min(max_scroll,
      event_state.smooth_scroll.content_target - smooth_scroll_amount))

    app_state.force_redraw = true

  elseif event_module.is_point_in_rect(mouse_x, mouse_y, ui.cv_role_list) then
    -- CV角色列表滚动
    local line_height = style_module.font_size + 3
    local visible_items = math.floor((ui.cv_role_list.h - 30) / line_height)
    local max_scroll = math.max(0, #app_state.cv_role_pairs - visible_items)

    -- 使用平滑滚动
    event_state.smooth_scroll.cv_role_target = math.max(0, math.min(max_scroll,
      event_state.smooth_scroll.cv_role_target - smooth_scroll_amount))

    app_state.force_redraw = true

  elseif ui.chapter_list and event_module.is_point_in_rect(mouse_x, mouse_y, ui.chapter_list) then
    -- 章节列表滚动
    local line_height = style_module.font_size + 5
    local visible_items = math.floor((ui.chapter_list.h - 10) / line_height)
    local max_scroll = math.max(0, #app_state.chapters - visible_items)

    -- 使用平滑滚动
    event_state.smooth_scroll.chapter_target = math.max(0, math.min(max_scroll,
      event_state.smooth_scroll.chapter_target - smooth_scroll_amount))

    app_state.force_redraw = true

  elseif event_module.is_point_in_rect(mouse_x, mouse_y, ui.selection_area) then
    -- 选择区域滚动 - 进一步减少倍数
    event_state.smooth_scroll.selection_target = math.max(0,
      event_state.smooth_scroll.selection_target - smooth_scroll_amount * 1)

    app_state.force_redraw = true
  end
end

-- 处理点击事件
function event_module.handle_click_events(app_state, mouse_x, mouse_y, mouse_cap)
  local ui = ui_module.get_ui_elements()
  if not ui then return end

  -- 检查是否是鼠标按下事件
  if (mouse_cap & 1) == 1 and event_state.last_mouse_state == 0 then
    -- 左键按下
    event_module.handle_left_click(app_state, mouse_x, mouse_y)
  elseif (mouse_cap & 2) == 2 and (event_state.last_mouse_state & 2) == 0 then
    -- 右键按下
    event_module.handle_right_click(app_state, mouse_x, mouse_y)
  end
end

-- 处理左键点击
function event_module.handle_left_click(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()

  -- 检查处理建议下拉菜单点击
  if app_state.show_suggestion_dropdown and ui.suggestion_dropdown and
     event_module.is_point_in_rect(mouse_x, mouse_y, ui.suggestion_dropdown) then
    event_module.handle_suggestion_dropdown_click(app_state, mouse_x, mouse_y)
    return
  end

  -- 检查输入框点击
  if ui.error_input and event_module.is_point_in_rect(mouse_x, mouse_y, ui.error_input) then
    event_module.handle_error_input_click(app_state)
    return
  elseif ui.correct_input and event_module.is_point_in_rect(mouse_x, mouse_y, ui.correct_input) then
    event_module.handle_correct_input_click(app_state)
    return
  elseif ui.episode_input and event_module.is_point_in_rect(mouse_x, mouse_y, ui.episode_input) then
    event_module.handle_episode_input_click(app_state)
    return
  elseif ui.suggestion_input and event_module.is_point_in_rect(mouse_x, mouse_y, ui.suggestion_input) then
    -- 处理建议输入框有特殊处理：显示/隐藏下拉菜单
    app_state.show_suggestion_dropdown = not app_state.show_suggestion_dropdown
    app_state.force_redraw = true
    return
  end

  -- 检查搜索框点击
  if ui.search_box and event_module.is_point_in_rect(mouse_x, mouse_y, ui.search_box) then
    app_state.search_input_active = true
    app_state.force_redraw = true
    return
  else
    app_state.search_input_active = false
  end

  -- 检查搜索导航按钮
  if ui.search_prev_btn and event_module.is_point_in_rect(mouse_x, mouse_y, ui.search_prev_btn) then
    event_module.goto_prev_search_result(app_state)
    return
  end

  if ui.search_next_btn and event_module.is_point_in_rect(mouse_x, mouse_y, ui.search_next_btn) then
    event_module.goto_next_search_result(app_state)
    return
  end

  -- 检查内容区域点击（句子选择）
  if event_module.is_point_in_rect(mouse_x, mouse_y, ui.content_area) then
    event_module.handle_sentence_click(app_state, mouse_x, mouse_y)
    return
  end

  -- 检查CV角色列表点击
  if event_module.is_point_in_rect(mouse_x, mouse_y, ui.cv_role_list) then
    event_module.handle_cv_role_click(app_state, mouse_x, mouse_y)
    return
  end

  -- 检查章节列表点击
  if ui.chapter_list and event_module.is_point_in_rect(mouse_x, mouse_y, ui.chapter_list) then
    event_module.handle_chapter_click(app_state, mouse_x, mouse_y)
    return
  end

  -- 检查按钮点击
  event_module.handle_button_clicks(app_state, mouse_x, mouse_y)

  -- 点击其他地方时隐藏下拉菜单
  if app_state.show_suggestion_dropdown then
    app_state.show_suggestion_dropdown = false
    app_state.force_redraw = true
  end
end

-- 处理右键点击
function event_module.handle_right_click(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()

  -- 检查内容区域右键点击
  if event_module.is_point_in_rect(mouse_x, mouse_y, ui.content_area) then
    event_module.handle_sentence_right_click(app_state, mouse_x, mouse_y)
    return
  end
end

-- 处理拖拽事件
function event_module.handle_drag_events(app_state, mouse_x, mouse_y, mouse_cap)
  -- 处理滚动条拖拽
  if (mouse_cap & 1) == 1 then  -- 左键按下
    event_module.handle_scrollbar_drag(app_state, mouse_x, mouse_y)

    -- 处理内容区域拖拽调整大小
    event_module.handle_content_resize_drag(app_state, mouse_x, mouse_y)
  else
    -- 重置拖拽状态
    event_state.is_dragging_sentence_scrollbar = false
    event_state.is_dragging_cv_role_scrollbar = false
    event_state.is_dragging_chapter_scrollbar = false
    event_state.is_dragging_selection_scrollbar = false

    -- 结束内容区域拖拽调整大小
    style_module.end_content_resize()
  end
end

-- 处理悬停事件
function event_module.handle_hover_events(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()
  if not ui then return end

  -- 重置悬停状态
  local old_hover_sentence = app_state.hover_sentence_idx
  local old_hover_chapter = app_state.hover_chapter_idx
  local old_hover_cv_role = app_state.hover_cv_role
  local old_hover_suggestion = app_state.hover_suggestion_idx

  app_state.hover_sentence_idx = -1
  app_state.hover_chapter_idx = -1
  app_state.hover_cv_role = {cv = "", role = "", is_cv = false}
  app_state.hover_suggestion_idx = -1

  -- 检查处理建议下拉菜单悬停
  if app_state.show_suggestion_dropdown and ui.suggestion_dropdown and
     event_module.is_point_in_rect(mouse_x, mouse_y, ui.suggestion_dropdown) then
    local relative_y = mouse_y - ui.suggestion_dropdown.y
    local option_index = math.floor(relative_y / 25) + 1
    if option_index > 0 and option_index <= #app_state.suggestion_options then
      app_state.hover_suggestion_idx = option_index
    end
  end

  -- 检查内容区域悬停
  if event_module.is_point_in_rect(mouse_x, mouse_y, ui.content_area) then
    app_state.hover_sentence_idx = event_module.get_sentence_index_at_position(app_state, mouse_x, mouse_y)
  end

  -- 检查CV角色列表悬停
  if event_module.is_point_in_rect(mouse_x, mouse_y, ui.cv_role_list) then
    local cv_role = event_module.get_cv_role_at_position(app_state, mouse_x, mouse_y)
    if cv_role then
      app_state.hover_cv_role = cv_role
    end
  end

  -- 检查章节列表悬停
  if ui.chapter_list and event_module.is_point_in_rect(mouse_x, mouse_y, ui.chapter_list) then
    app_state.hover_chapter_idx = event_module.get_chapter_index_at_position(app_state, mouse_x, mouse_y)
  end

  -- 检查是否需要重绘
  if old_hover_sentence ~= app_state.hover_sentence_idx or
     old_hover_chapter ~= app_state.hover_chapter_idx or
     old_hover_cv_role.cv ~= app_state.hover_cv_role.cv or
     old_hover_cv_role.role ~= app_state.hover_cv_role.role or
     old_hover_suggestion ~= app_state.hover_suggestion_idx then
    app_state.force_redraw = true
  end
end

-- 处理键盘事件
function event_module.handle_keyboard_events(app_state)
  -- 键盘事件处理在主循环中进行
  -- 这里主要处理快捷键

  -- Ctrl+F 激活搜索
  if event_state.is_ctrl_down and gfx.getchar() == 102 then  -- 'f' key
    app_state.search_input_active = true
    app_state.force_redraw = true
  end
end

-- 处理窗口事件
function event_module.handle_window_events(app_state)
  -- 检查窗口大小变化
  if gfx.w ~= event_state.last_window_w or gfx.h ~= event_state.last_window_h then
    event_state.last_window_w = gfx.w
    event_state.last_window_h = gfx.h
    app_state.force_redraw = true
  end
end

-- 更新平滑滚动
function event_module.update_smooth_scroll(app_state)
  local smooth = event_state.smooth_scroll
  local needs_redraw = false

  -- 更新内容区域滚动
  local content_delta = smooth.content_target - smooth.content_current
  if math.abs(content_delta) > smooth.min_delta then
    smooth.content_current = smooth.content_current + content_delta * smooth.smoothing_factor
    app_state.sentence_scroll_pos = math.floor(smooth.content_current + 0.5)
    needs_redraw = true
  else
    smooth.content_current = smooth.content_target
    app_state.sentence_scroll_pos = smooth.content_target
  end

  -- 更新CV角色列表滚动
  local cv_role_delta = smooth.cv_role_target - smooth.cv_role_current
  if math.abs(cv_role_delta) > smooth.min_delta then
    smooth.cv_role_current = smooth.cv_role_current + cv_role_delta * smooth.smoothing_factor
    app_state.cv_role_scroll_pos = math.floor(smooth.cv_role_current + 0.5)
    needs_redraw = true
  else
    smooth.cv_role_current = smooth.cv_role_target
    app_state.cv_role_scroll_pos = smooth.cv_role_target
  end

  -- 更新章节列表滚动
  local chapter_delta = smooth.chapter_target - smooth.chapter_current
  if math.abs(chapter_delta) > smooth.min_delta then
    smooth.chapter_current = smooth.chapter_current + chapter_delta * smooth.smoothing_factor
    app_state.chapter_scroll_pos = math.floor(smooth.chapter_current + 0.5)
    needs_redraw = true
  else
    smooth.chapter_current = smooth.chapter_target
    app_state.chapter_scroll_pos = smooth.chapter_target
  end

  -- 更新选择区域滚动
  local selection_delta = smooth.selection_target - smooth.selection_current
  if math.abs(selection_delta) > smooth.min_delta then
    smooth.selection_current = smooth.selection_current + selection_delta * smooth.smoothing_factor
    app_state.selection_scroll_pos = math.floor(smooth.selection_current + 0.5)
    needs_redraw = true
  else
    smooth.selection_current = smooth.selection_target
    app_state.selection_scroll_pos = smooth.selection_target
  end

  if needs_redraw then
    app_state.force_redraw = true
  end
end

-- 初始化平滑滚动状态
function event_module.init_smooth_scroll(app_state)
  local smooth = event_state.smooth_scroll
  smooth.content_target = app_state.sentence_scroll_pos or 0
  smooth.content_current = smooth.content_target
  smooth.cv_role_target = app_state.cv_role_scroll_pos or 0
  smooth.cv_role_current = smooth.cv_role_target
  smooth.chapter_target = app_state.chapter_scroll_pos or 0
  smooth.chapter_current = smooth.chapter_target
  smooth.selection_target = app_state.selection_scroll_pos or 0
  smooth.selection_current = smooth.selection_target
end

-- 更新按钮状态
function event_module.update_button_states(app_state)
  local mouse_x, mouse_y = gfx.mouse_x, gfx.mouse_y
  local mouse_cap = gfx.mouse_cap

  -- 获取所有按钮
  local ui = ui_module.get_ui_elements()
  if not ui then return end

  local buttons = {
    ui.play_button,
    ui.block_mark_button,
    ui.region_mark_button,
    ui.excel_button,
    ui.rate_minus_button,
    ui.rate_plus_button,
    ui.font_decrease_button,
    ui.font_increase_button
  }

  -- 更新按钮状态
  local button_state_changed = style_module.update_button_states(buttons, mouse_x, mouse_y, mouse_cap)
  style_module.update_button_animations()

  if button_state_changed then
    app_state.force_redraw = true
  end
end

-- 辅助函数：检查点是否在矩形内
function event_module.is_point_in_rect(x, y, rect)
  if not rect then return false end
  return x >= rect.x and x <= rect.x + rect.w and y >= rect.y and y <= rect.y + rect.h
end

-- 处理句子点击
function event_module.handle_sentence_click(app_state, mouse_x, mouse_y)
  local sentence_idx = event_module.get_sentence_index_at_position(app_state, mouse_x, mouse_y)
  if sentence_idx > 0 and sentence_idx <= #app_state.sentences then
    local sentence = app_state.sentences[sentence_idx]

    if event_state.is_ctrl_down then
      -- Ctrl+点击：多选
      if not app_state.selected_indices then
        app_state.selected_indices = {}
        app_state.selected_texts = {}
      end

      -- 检查是否已选中
      local already_selected = false
      for i, idx in ipairs(app_state.selected_indices) do
        if idx == sentence_idx then
          -- 取消选择
          table.remove(app_state.selected_indices, i)
          table.remove(app_state.selected_texts, i)
          already_selected = true
          break
        end
      end

      if not already_selected then
        -- 添加到选择
        table.insert(app_state.selected_indices, sentence_idx)
        table.insert(app_state.selected_texts, sentence)
      end

      -- 多选时，从第一个选中的句子提取CV角色信息
      if #app_state.selected_texts > 0 then
        app_state.selected_text = table.concat(app_state.selected_texts, "\n")

        -- 提取CV和角色信息（从第一个选中的句子获取）
        if text_utils and text_utils.handle_cv_role_selection then
          app_state.selected_role, app_state.selected_cv = text_utils.handle_cv_role_selection(
            app_state.sentences[app_state.selected_indices[1]],
            app_state.cv_role_pairs,
            app_state.selected_role,
            app_state.selected_cv,
            app_state.is_cv_role_reversed
          )
          -- 自动跳转到对应的角色CV位置
          event_module.scroll_to_cv_role(app_state, app_state.selected_role, app_state.selected_cv)
        end
      else
        app_state.selected_text = ""
      end
    else
      -- 普通点击：单选
      app_state.selected_text = sentence
      app_state.selected_texts = {sentence}
      app_state.selected_indices = {sentence_idx}

      -- 提取CV和角色信息
      if text_utils and text_utils.handle_cv_role_selection then
        app_state.selected_role, app_state.selected_cv = text_utils.handle_cv_role_selection(
          sentence,
          app_state.cv_role_pairs,
          app_state.selected_role,
          app_state.selected_cv,
          app_state.is_cv_role_reversed
        )
        -- 自动跳转到对应的角色CV位置
        event_module.scroll_to_cv_role(app_state, app_state.selected_role, app_state.selected_cv)

        -- 检查对轨开关是否开启（左键对轨功能）
        if app_state.is_track_align_enabled and app_state.selected_cv and app_state.selected_cv ~= "" then
          -- 执行对轨脚本（左键操作）
          if button_module and button_module.run_track_align_script then
            button_module.run_track_align_script(app_state.selected_cv, app_state.selected_role, false)
          end
        end
      end
    end

    app_state.force_redraw = true
  end
end

-- 处理句子右键点击
function event_module.handle_sentence_right_click(app_state, mouse_x, mouse_y)
  local sentence_idx = event_module.get_sentence_index_at_position(app_state, mouse_x, mouse_y)
  if sentence_idx > 0 and sentence_idx <= #app_state.sentences then
    local sentence = app_state.sentences[sentence_idx]

    -- 检查对轨开关是否开启
    if app_state.is_track_align_enabled then
      -- 对轨开关开启时，先选中句子，然后执行empty_media脚本
      app_state.selected_text = sentence

      -- 清空多选列表，设置为单选
      app_state.selected_indices = {sentence_idx}
      app_state.selected_texts = {sentence}
      event_state.is_ctrl_down = false

      -- 提取CV和角色信息（右键选择时重新提取，不保留之前的值）
      if text_utils and text_utils.handle_cv_role_selection then
        app_state.selected_role, app_state.selected_cv = text_utils.handle_cv_role_selection(
          sentence,
          app_state.cv_role_pairs,
          "",  -- 传入空字符串，强制重新提取
          "",  -- 传入空字符串，强制重新提取
          app_state.is_cv_role_reversed
        )
        -- 识别到CV和角色信息后跳转
        event_module.scroll_to_cv_role(app_state, app_state.selected_role, app_state.selected_cv)
      end

      -- 执行对轨脚本（右键操作）
      if button_module and button_module.run_track_align_script then
        button_module.run_track_align_script(app_state.selected_cv, app_state.selected_role, true)
      end
    else
      -- 对轨开关关闭时，只是选择句子
      app_state.selected_text = sentence
      app_state.selected_texts = {sentence}
      app_state.selected_indices = {sentence_idx}

      -- 提取CV和角色信息
      if text_utils and text_utils.handle_cv_role_selection then
        app_state.selected_role, app_state.selected_cv = text_utils.handle_cv_role_selection(
          sentence,
          app_state.cv_role_pairs,
          app_state.selected_role,
          app_state.selected_cv,
          app_state.is_cv_role_reversed
        )
        -- 自动跳转到对应的角色CV位置
        event_module.scroll_to_cv_role(app_state, app_state.selected_role, app_state.selected_cv)
      end
    end

    app_state.force_redraw = true
  end
end

-- 处理CV角色点击
function event_module.handle_cv_role_click(app_state, mouse_x, mouse_y)
  local cv_role = event_module.get_cv_role_at_position(app_state, mouse_x, mouse_y)
  if cv_role then
    -- 更新选中的CV和角色
    app_state.selected_cv = cv_role.cv
    app_state.selected_role = cv_role.role

    -- 使用text_utils处理CV角色选择
    if text_utils and text_utils.handle_cv_role_selection then
      local clicked_text = cv_role.cv .. ":" .. cv_role.role
      app_state.selected_role, app_state.selected_cv = text_utils.handle_cv_role_selection(
        clicked_text,
        app_state.cv_role_pairs,
        app_state.selected_role,
        app_state.selected_cv,
        app_state.is_cv_role_reversed
      )
    end

    -- 跳转到对应的句子
    event_module.scroll_to_cv_role_sentence(app_state, app_state.selected_role, app_state.selected_cv)

    app_state.force_redraw = true
  end
end

-- 处理章节点击
function event_module.handle_chapter_click(app_state, mouse_x, mouse_y)
  local chapter_idx = event_module.get_chapter_index_at_position(app_state, mouse_x, mouse_y)
  if chapter_idx > 0 and chapter_idx <= #app_state.chapters then
    local chapter = app_state.chapters[chapter_idx]
    if chapter then
      -- 跳转到章节对应的句子
      ui_module.scroll_to_sentence(app_state, chapter.sentence_idx)
    end
  end
end

-- 处理内容区域拖拽调整大小
function event_module.handle_content_resize_drag(app_state, mouse_x, mouse_y)
  -- 处理内容区域拖拽调整大小
  if style_module.handle_content_resize(mouse_y) then
    -- 内容区域高度发生变化，需要重新初始化UI元素
    ui_module.reinit_ui_elements()
    app_state.force_redraw = true
  end
end

-- 处理按钮点击
function event_module.handle_button_clicks(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()
  if not ui then return end

  -- 检查内容区域拖拽调整大小区域点击
  if ui.content_resize_handle and style_module.is_mouse_in_resize_handle(mouse_x, mouse_y, ui.content_resize_handle) then
    style_module.start_content_resize(mouse_y)
    return -- 直接返回，开始拖拽调整大小
  end

  -- 检查各种按钮点击
  if ui.play_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.play_button) then
    event_module.handle_play_button_click(app_state)
  elseif ui.block_mark_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.block_mark_button) then
    event_module.handle_block_mark_button_click(app_state)
  elseif ui.region_mark_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.region_mark_button) then
    event_module.handle_region_mark_button_click(app_state)
  elseif ui.excel_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.excel_button) then
    event_module.handle_excel_button_click(app_state)
  elseif ui.rate_minus_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.rate_minus_button) then
    event_module.handle_rate_minus_click(app_state)
  elseif ui.rate_plus_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.rate_plus_button) then
    event_module.handle_rate_plus_click(app_state)
  elseif ui.rate_reset_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.rate_reset_button) then
    event_module.handle_rate_reset_click(app_state)
  elseif ui.document_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.document_button) then
    event_module.handle_document_button_click(app_state)
  elseif ui.clipboard_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.clipboard_button) then
    event_module.handle_clipboard_button_click(app_state)
  elseif ui.open_csv_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.open_csv_button) then
    event_module.handle_open_csv_button_click(app_state)
  elseif ui.au_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.au_button) then
    event_module.handle_au_button_click(app_state)
  elseif ui.region_name_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.region_name_button) then
    event_module.handle_region_name_button_click(app_state)
  elseif ui.file_name_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.file_name_button) then
    event_module.handle_file_name_button_click(app_state)
  elseif ui.track_color_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.track_color_button) then
    event_module.handle_track_color_button_click(app_state)
  elseif ui.track_split_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.track_split_button) then
    event_module.handle_track_split_button_click(app_state)
  elseif ui.track_align_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.track_align_button) then
    event_module.handle_track_align_button_click(app_state)
  elseif ui.chapter_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.chapter_button) then
    event_module.handle_chapter_button_click(app_state)
  elseif ui.font_decrease_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.font_decrease_button) then
    event_module.handle_font_decrease_click(app_state)
  elseif ui.font_increase_button and event_module.is_point_in_rect(mouse_x, mouse_y, ui.font_increase_button) then
    event_module.handle_font_increase_click(app_state)
  elseif ui.cv_role_reverse_checkbox and event_module.is_point_in_rect(mouse_x, mouse_y, ui.cv_role_reverse_checkbox) then
    event_module.handle_cv_role_reverse_checkbox_click(app_state)
  elseif ui.search_prev_btn and event_module.is_point_in_rect(mouse_x, mouse_y, ui.search_prev_btn) then
    event_module.goto_prev_search_result(app_state)
  elseif ui.search_next_btn and event_module.is_point_in_rect(mouse_x, mouse_y, ui.search_next_btn) then
    event_module.goto_next_search_result(app_state)
  end
end

-- 处理播放按钮点击
function event_module.handle_play_button_click(app_state)
  app_state.is_playing = utils_module.toggle_play()
  app_state.force_redraw = true
end

-- 处理块标按钮点击
function event_module.handle_block_mark_button_click(app_state)
  -- 检查条件
  local has_selected_item = r.GetSelectedMediaItem(0, 0) ~= nil
  local role_cv_selected = app_state.selected_cv ~= "" and app_state.selected_role ~= ""
  local has_process_suggestion = app_state.process_suggestion ~= ""

  if has_selected_item and role_cv_selected and has_process_suggestion then
    -- 执行块标操作
    if button_module and button_module.handle_block_mark_button_click then
      local message, formatted_time, relative_pos = button_module.handle_block_mark_button_click(app_state)

      -- 保存标记时间信息
      if formatted_time and formatted_time ~= "" then
        app_state.marked_relative_time = formatted_time
        app_state.marked_relative_pos = relative_pos or 0
      end

      -- 显示结果消息
      if message and message ~= "" then
        utils_module.info(message)
      end

      app_state.force_redraw = true
    else
      utils_module.info("块标功能不可用")
    end
  else
    -- 显示缺少条件的提示
    if not has_selected_item then
      utils_module.info("请先选择一个音频块!")
    elseif not role_cv_selected then
      utils_module.info("请先选择角色和CV!")
    elseif not has_process_suggestion then
      utils_module.info("请先填写处理建议!")
    end
  end
end

-- 处理区标按钮点击
function event_module.handle_region_mark_button_click(app_state)
  -- 检查条件
  local role_cv_selected = app_state.selected_cv ~= "" and app_state.selected_role ~= ""
  local has_process_suggestion = app_state.process_suggestion ~= ""

  if role_cv_selected and has_process_suggestion then
    -- 执行区标操作
    if button_module and button_module.handle_region_mark_button_click then
      local message, formatted_time, relative_pos = button_module.handle_region_mark_button_click(app_state)

      -- 保存标记时间信息
      if formatted_time and formatted_time ~= "" then
        app_state.marked_relative_time = formatted_time
        app_state.marked_relative_pos = relative_pos or 0
      end

      -- 显示结果消息
      if message and message ~= "" then
        utils_module.info(message)
      end

      app_state.force_redraw = true
    else
      utils_module.info("区标功能不可用")
    end
  else
    -- 显示缺少条件的提示
    if not role_cv_selected then
      utils_module.info("请先选择角色和CV!")
    elseif not has_process_suggestion then
      utils_module.info("请先填写处理建议!")
    end
  end
end

-- 处理Excel按钮点击
function event_module.handle_excel_button_click(app_state)
  if #app_state.selected_texts > 0 or app_state.selected_text ~= "" then
    -- 导出到Excel
    if button_module and button_module.handle_excel_button_click then
      local message = button_module.handle_excel_button_click(app_state)

      -- 显示结果消息
      if message and message ~= "" then
        utils_module.info(message)
      end

      app_state.force_redraw = true
    else
      utils_module.info("Excel导出功能不可用")
    end
  else
    utils_module.info("请先选择要导出的内容!")
  end
end

-- 处理速率减少按钮点击
function event_module.handle_rate_minus_click(app_state)
  app_state.current_playrate = utils_module.adjust_playrate(-0.1)
  app_state.force_redraw = true
end

-- 处理速率增加按钮点击
function event_module.handle_rate_plus_click(app_state)
  app_state.current_playrate = utils_module.adjust_playrate(0.1)
  app_state.force_redraw = true
end

-- 处理速率重置按钮点击
function event_module.handle_rate_reset_click(app_state)
  app_state.current_playrate = utils_module.reset_playrate()
  app_state.force_redraw = true
end

-- 处理读取文档按钮点击
function event_module.handle_document_button_click(app_state)
  local callbacks = {
    handle_text_content = function(text_content)
      app_state.clipboard_text = text_content
      event_module.parse_sentences(app_state)
      event_module.extract_cv_role_pairs(app_state)
      event_module.extract_chapters(app_state)  -- 添加章节提取
    end,
    set_clipboard_text = function(text)
      app_state.clipboard_text = text
    end,
    parse_sentences = function()
      event_module.parse_sentences(app_state)
    end,
    extract_cv_role_pairs = function()
      event_module.extract_cv_role_pairs(app_state)
    end
  }

  local result = button_module.handle_document_button(callbacks)
  app_state.force_redraw = true
  utils_module.info(result)
end

-- 处理读取剪贴板按钮点击
function event_module.handle_clipboard_button_click(app_state)
  local callbacks = {
    get_clipboard = function()
      app_state.clipboard_text = utils_module.get_clipboard()
    end,
    parse_sentences = function()
      event_module.parse_sentences(app_state)
    end,
    extract_cv_role_pairs = function()
      event_module.extract_cv_role_pairs(app_state)
    end
  }

  local result = button_module.handle_clipboard_button(callbacks)
  app_state.force_redraw = true
  utils_module.info(result)
end

-- 处理打开CSV按钮点击
function event_module.handle_open_csv_button_click(app_state)
  -- 调用style_module的函数，与原脚本一致
  if style_module and style_module.open_csv_file then
    local result = style_module.open_csv_file()
    utils_module.info(result)
  else
    utils_module.info("打开CSV功能不可用")
  end
  app_state.force_redraw = true
end

-- 处理AU按钮点击
function event_module.handle_au_button_click(app_state)
  -- 调用style_module的函数，与原脚本一致
  if style_module and style_module.run_au_script then
    local result = style_module.run_au_script()
    utils_module.info(result)
  else
    utils_module.info("AU脚本功能不可用")
  end
  app_state.force_redraw = true
end

-- 处理区名按钮点击
function event_module.handle_region_name_button_click(app_state)
  local result = button_module.handle_region_name_button_click(app_state.chapters)
  app_state.force_redraw = true
  utils_module.info(result)
end

-- 处理文名按钮点击
function event_module.handle_file_name_button_click(app_state)
  local result = button_module.handle_file_name_button_click(app_state.chapters)
  app_state.force_redraw = true
  utils_module.info(result)
end

-- 处理轨色按钮点击
function event_module.handle_track_color_button_click(app_state)
  -- 传递正确的参数，与原脚本一致：sentences, cv_role_pairs, is_cv_role_reversed
  if button_module and button_module.handle_track_color_button_click then
    local result = button_module.handle_track_color_button_click(
      app_state.sentences,
      app_state.cv_role_pairs,
      app_state.is_cv_role_reversed
    )
    utils_module.info(result)
  else
    utils_module.info("轨色功能不可用")
  end
  app_state.force_redraw = true
end

-- 处理分轨按钮点击
function event_module.handle_track_split_button_click(app_state)
  -- 调用无参数的函数，与原脚本一致
  if button_module and button_module.handle_track_split_button_click then
    local result = button_module.handle_track_split_button_click()
    utils_module.info(result)
  else
    utils_module.info("分轨功能不可用")
  end
  app_state.force_redraw = true
end

-- 处理对轨按钮点击
function event_module.handle_track_align_button_click(app_state)
  app_state.is_track_align_enabled = not app_state.is_track_align_enabled
  app_state.force_redraw = true
  local status = app_state.is_track_align_enabled and "开启" or "关闭"
  utils_module.info("对轨功能已" .. status)
end

-- 处理章节按钮点击（完整版本，与原脚本一致）
function event_module.handle_chapter_button_click(app_state)
  -- 切换章节列表的显示状态
  app_state.is_chapter_list_visible = not app_state.is_chapter_list_visible

  -- 获取UI元素
  local ui = ui_module.get_ui_elements()
  if not ui then
    app_state.force_redraw = true
    local status = app_state.is_chapter_list_visible and "显示" or "隐藏"
    utils_module.info("章节列表已" .. status)
    return
  end

  -- 开始章节列表动画
  utils_module.start_chapter_animation(app_state, ui, app_state.is_chapter_list_visible)

  local status = app_state.is_chapter_list_visible and "显示" or "隐藏"
  utils_module.info("章节列表已" .. status)
end

-- 处理CV角色交换位置复选框点击（完整版本，与原脚本一致）
function event_module.handle_cv_role_reverse_checkbox_click(app_state)
  -- 切换复选框状态
  app_state.is_cv_role_reversed = not app_state.is_cv_role_reversed

  -- 交换当前选中的CV和角色
  -- 这是原脚本中的核心逻辑：交换位置复选框不影响列表显示，但影响选择状态的含义
  if app_state.selected_cv ~= "" and app_state.selected_role ~= "" then
    local temp_cv = app_state.selected_cv
    app_state.selected_cv = app_state.selected_role
    app_state.selected_role = temp_cv
  end

  -- 强制重绘界面
  app_state.force_redraw = true

  local status = app_state.is_cv_role_reversed and "已交换" or "未交换"
  utils_module.info("CV角色位置" .. status)
end

-- 处理错误描述输入框点击
function event_module.handle_error_input_click(app_state)
  if button_module and button_module.handle_input_click then
    local new_value = button_module.handle_input_click("error", app_state.error_note)
    app_state.error_note = new_value
    app_state.force_redraw = true
    utils_module.info("错误描述已更新")
  end
end

-- 处理正确表达输入框点击
function event_module.handle_correct_input_click(app_state)
  if button_module and button_module.handle_input_click then
    local new_value = button_module.handle_input_click("correct", app_state.correct_note)
    app_state.correct_note = new_value
    app_state.force_redraw = true
    utils_module.info("正确表达已更新")
  end
end

-- 处理集数输入框点击
function event_module.handle_episode_input_click(app_state)
  if button_module and button_module.handle_input_click then
    -- 尝试从选中的音频块获取集数
    local auto_episode = utils_module.get_episode_from_selected_item and utils_module.get_episode_from_selected_item() or ""
    local new_value = button_module.handle_input_click("episode", app_state.episode_number, {auto_episode = auto_episode})
    app_state.episode_number = new_value
    app_state.force_redraw = true
    utils_module.info("集数已更新")
  end
end

-- 处理处理建议下拉菜单点击
function event_module.handle_suggestion_dropdown_click(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()
  if not ui.suggestion_dropdown then return end

  local dropdown = ui.suggestion_dropdown
  local relative_y = mouse_y - dropdown.y
  local option_index = math.floor(relative_y / 25) + 1

  if option_index > 0 and option_index <= #app_state.suggestion_options then
    local selected_option = app_state.suggestion_options[option_index]
    app_state.process_suggestion = selected_option
    app_state.last_selected_suggestion = selected_option
    app_state.show_suggestion_dropdown = false
    app_state.force_redraw = true
    utils_module.info("选择处理建议: " .. selected_option)
  end
end

-- 跳转到CV角色对应的句子
function event_module.scroll_to_cv_role_sentence(app_state, role_name, cv_name)
  if not role_name or not cv_name or role_name == "" or cv_name == "" then
    return
  end

  -- 检查是否为章节内容，如果是则不执行滚动
  if cv_name:match("%[CHAPTER%](.-)%[/CHAPTER%]") or
     cv_name:match("^第[%d一二三四五六七八九十百千]+[章节集话回]") or
     cv_name:match("^章节[%d一二三四五六七八九十百千]+") then
    return  -- 直接返回，不执行滚动
  end

  -- 在句子中查找包含该CV角色的句子
  local target_sentence_idx = -1

  for i, sentence in ipairs(app_state.sentences) do
    if sentence and sentence ~= "__SKIP_THIS_SENTENCE__" then
      -- 查找【CV-角色】格式
      local cv_role_pattern = "【" .. cv_name .. "[-－]" .. role_name .. "】"
      if sentence:find(cv_role_pattern) then
        target_sentence_idx = i
        break
      end

      -- 也查找【角色-CV】格式
      local role_cv_pattern = "【" .. role_name .. "[-－]" .. cv_name .. "】"
      if sentence:find(role_cv_pattern) then
        target_sentence_idx = i
        break
      end

      -- 查找简单的角色：格式
      if sentence:find(role_name .. "：") then
        target_sentence_idx = i
        break
      end
    end
  end

  -- 如果找到了对应的句子，跳转到该句子
  if target_sentence_idx > 0 then
    ui_module.scroll_to_sentence(app_state, target_sentence_idx)
    utils_module.info("跳转到 " .. cv_name .. "-" .. role_name .. " 的句子")
  end
end

-- 跳转到CV角色列表中的指定位置
function event_module.scroll_to_cv_role(app_state, role_name, cv_name)
  if not role_name or not cv_name or role_name == "" or cv_name == "" then
    return
  end

  -- 检查是否为章节内容，如果是则不执行滚动
  if cv_name:match("%[CHAPTER%](.-)%[/CHAPTER%]") or
     cv_name:match("^第[%d一二三四五六七八九十百千]+[章节集话回]") or
     cv_name:match("^章节[%d一二三四五六七八九十百千]+") then
    return  -- 直接返回，不执行滚动
  end

  local cv_role_pairs = app_state.cv_role_pairs
  if #cv_role_pairs == 0 then return end

  -- 按CV分类整理角色列表
  local cv_categories, cv_order = ui_module.get_cv_role_categories(cv_role_pairs, app_state.is_cv_role_reversed)

  -- 查找目标CV角色在分类列表中的位置
  local target_position = -1
  local current_item = 1

  for _, cv_name_in_list in ipairs(cv_order) do
    local roles = cv_categories[cv_name_in_list]

    -- 跳过CV分类标题
    current_item = current_item + 1

    -- 检查该CV下的所有角色
    for _, role_info in ipairs(roles) do
      if cv_name_in_list == cv_name and role_info.role == role_name then
        target_position = current_item - 1  -- 减1因为滚动位置是从0开始的
        break
      end
      current_item = current_item + 1
    end

    if target_position >= 0 then
      break
    end
  end

  -- 如果找到了目标位置，滚动到该位置
  if target_position >= 0 then
    local ui = ui_module.get_ui_elements()
    if ui and ui.cv_role_list then
      -- 计算可见项目数
      local cv_role_line_height = math.max(20, style_module.font_size + 2)
      local visible_items = math.floor((ui.cv_role_list.h - 10) / cv_role_line_height)

      -- 调整滚动位置，使目标项在视图中间
      local target_scroll = math.max(0, target_position - math.floor(visible_items / 2))

      -- 计算总项目数以限制滚动范围
      local total_items = 0
      for _, roles in pairs(cv_categories) do
        total_items = total_items + 1 + #roles
      end

      local max_scroll = math.max(0, total_items - visible_items)
      app_state.cv_role_scroll_pos = math.min(target_scroll, max_scroll)

      -- 更新平滑滚动目标
      if event_state.smooth_scroll then
        event_state.smooth_scroll.cv_role_target = app_state.cv_role_scroll_pos
      end

      app_state.force_redraw = true
    end
  end
end

-- 处理字体缩小按钮点击
function event_module.handle_font_decrease_click(app_state)
  if style_module.font_size > 12 then
    style_module.font_size = style_module.font_size - 1
    app_state.force_redraw = true
    utils_module.info("字体大小: " .. style_module.font_size)
  end
end

-- 处理字体放大按钮点击
function event_module.handle_font_increase_click(app_state)
  if style_module.font_size < 32 then
    style_module.font_size = style_module.font_size + 1
    app_state.force_redraw = true
    utils_module.info("字体大小: " .. style_module.font_size)
  end
end

-- 处理滚动条拖拽
function event_module.handle_scrollbar_drag(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()
  if not ui then return end

  -- 检查各个滚动条
  local content_scrollbar = {
    x = ui.content_area.x + ui.content_area.w - 10,
    y = ui.content_area.y,
    w = 10,
    h = ui.content_area.h
  }

  if event_module.is_point_in_rect(mouse_x, mouse_y, content_scrollbar) then
    event_state.is_dragging_sentence_scrollbar = true
    -- 计算新的滚动位置
    local relative_y = (mouse_y - content_scrollbar.y) / content_scrollbar.h
    local line_height = style_module.content_font_size + 5
    local visible_items = math.floor((ui.content_area.h - 10) / line_height)
    local max_scroll = math.max(0, #app_state.sentences - visible_items)

    app_state.sentence_scroll_pos = math.max(0, math.min(max_scroll, math.floor(relative_y * max_scroll)))
    app_state.force_redraw = true
  end

  -- 类似地处理其他滚动条...
end

-- 搜索功能
function event_module.perform_search(app_state, keyword)
  if not keyword or keyword == "" then
    app_state.search_results = {}
    app_state.current_search_index = 0
    return
  end

  app_state.search_results = {}
  local keyword_lower = keyword:lower()

  -- 在所有句子中搜索
  for i, sentence in ipairs(app_state.sentences) do
    if sentence and sentence ~= "__SKIP_THIS_SENTENCE__" then
      -- 清除标签后搜索
      local clean_sentence = ui_module.clean_text_tags(sentence):lower()
      if clean_sentence:find(keyword_lower, 1, true) then
        table.insert(app_state.search_results, i)
      end
    end
  end

  -- 重置当前搜索索引
  app_state.current_search_index = #app_state.search_results > 0 and 1 or 0
  app_state.force_redraw = true
end

-- 跳转到下一个搜索结果
function event_module.goto_next_search_result(app_state)
  if #app_state.search_results == 0 then return end

  app_state.current_search_index = app_state.current_search_index + 1
  if app_state.current_search_index > #app_state.search_results then
    app_state.current_search_index = 1
  end

  -- 滚动到当前结果
  local target_sentence = app_state.search_results[app_state.current_search_index]
  ui_module.scroll_to_sentence(app_state, target_sentence)
end

-- 跳转到上一个搜索结果
function event_module.goto_prev_search_result(app_state)
  if #app_state.search_results == 0 then return end

  app_state.current_search_index = app_state.current_search_index - 1
  if app_state.current_search_index < 1 then
    app_state.current_search_index = #app_state.search_results
  end

  -- 滚动到当前结果
  local target_sentence = app_state.search_results[app_state.current_search_index]
  ui_module.scroll_to_sentence(app_state, target_sentence)
end

-- 获取鼠标位置对应的句子索引
function event_module.get_sentence_index_at_position(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()
  if not ui or not ui.content_area then return -1 end

  local content_area = ui.content_area
  if not event_module.is_point_in_rect(mouse_x, mouse_y, content_area) then
    return -1
  end

  local line_height = style_module.content_font_size + 5
  local relative_y = mouse_y - content_area.y - 5
  local line_index = math.floor(relative_y / line_height)

  return app_state.sentence_scroll_pos + line_index + 1
end

-- 获取鼠标位置对应的CV角色（按分类处理）
function event_module.get_cv_role_at_position(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()
  if not ui or not ui.cv_role_list then return nil end

  local cv_role_list = ui.cv_role_list
  if not event_module.is_point_in_rect(mouse_x, mouse_y, cv_role_list) then
    return nil
  end

  local cv_role_pairs = app_state.cv_role_pairs
  if #cv_role_pairs == 0 then return nil end

  -- 使用基于字体大小的动态行高
  local cv_role_line_height = math.max(20, style_module.font_size + 2)

  -- 按CV分类整理角色列表
  local cv_categories, cv_order = ui_module.get_cv_role_categories(cv_role_pairs, app_state.is_cv_role_reversed)

  -- 计算所有分类项的总数（包括CV分类标题）
  local total_items = 0
  for _, roles in pairs(cv_categories) do
    total_items = total_items + 1 + #roles
  end

  -- 计算可见项目数
  local visible_items = math.floor((cv_role_list.h - 10) / cv_role_line_height)

  -- 计算点击位置对应的项目索引
  local relative_y = mouse_y - cv_role_list.y - 5
  local clicked_line = math.floor(relative_y / cv_role_line_height)
  local clicked_item = app_state.cv_role_scroll_pos + clicked_line + 1

  -- 遍历分类项，找到对应的CV和角色
  local current_item = 1
  for _, cv_name in ipairs(cv_order) do
    local roles = cv_categories[cv_name]

    -- 检查是否点击了CV分类标题
    if current_item == clicked_item then
      -- 点击了CV分类标题，不返回具体角色
      return nil
    end
    current_item = current_item + 1

    -- 检查是否点击了该CV下的某个角色
    for _, role_info in ipairs(roles) do
      if current_item == clicked_item then
        -- 点击了具体角色
        return {cv = cv_name, role = role_info.role, is_cv = false}
      end
      current_item = current_item + 1
    end
  end

  return nil
end

-- 获取鼠标位置对应的章节索引
function event_module.get_chapter_index_at_position(app_state, mouse_x, mouse_y)
  local ui = ui_module.get_ui_elements()
  if not ui or not ui.chapter_list then return -1 end

  local chapter_list = ui.chapter_list
  if not event_module.is_point_in_rect(mouse_x, mouse_y, chapter_list) then
    return -1
  end

  local line_height = style_module.font_size + 5
  local relative_y = mouse_y - chapter_list.y - 30
  local line_index = math.floor(relative_y / line_height)

  return app_state.chapter_scroll_pos + line_index + 1
end

-- 解析句子
function event_module.parse_sentences(app_state)
  if not app_state.clipboard_text or app_state.clipboard_text == "" then
    app_state.sentences = {}
    return
  end

  -- 使用text_utils解析句子
  if text_utils and text_utils.parse_sentences then
    app_state.sentences = text_utils.parse_sentences(app_state.clipboard_text)
  else
    -- 改进的句子分割逻辑 - 确保完整性
    app_state.sentences = {}
    local content = app_state.clipboard_text

    -- 首先按行分割，保持所有非空行
    for line in content:gmatch("[^\r\n]+") do
      local trimmed_line = trim(line)
      if trimmed_line and trimmed_line ~= "" then
        -- 检查是否是特殊格式（章节标记、CV对话等）
        local is_special_format = trimmed_line:find("%[CHAPTER%]") or
                                 trimmed_line:find("【.-】") or
                                 trimmed_line:find("第%d+章")

        if is_special_format then
          -- 特殊格式行直接作为一个句子
          table.insert(app_state.sentences, trimmed_line)
        elseif trimmed_line:find("[。！？]") then
          -- 包含句子结束符的行，进一步分割
          local temp_sentences = {}
          for sentence in trimmed_line:gmatch("[^。！？]+[。！？]?") do
            local trimmed_sentence = trim(sentence)
            if trimmed_sentence and trimmed_sentence ~= "" then
              table.insert(temp_sentences, trimmed_sentence)
            end
          end

          -- 如果分割后有内容，添加到句子列表
          if #temp_sentences > 0 then
            for _, s in ipairs(temp_sentences) do
              table.insert(app_state.sentences, s)
            end
          else
            -- 如果分割失败，保留原行
            table.insert(app_state.sentences, trimmed_line)
          end
        else
          -- 不包含句子结束符的行，直接作为一个句子
          table.insert(app_state.sentences, trimmed_line)
        end
      end
    end

    -- 确保至少有一些内容
    if #app_state.sentences == 0 and content and content ~= "" then
      -- 如果按行分割失败，尝试简单分割
      table.insert(app_state.sentences, content)
    end
  end

  app_state.force_redraw = true
end

-- 提取CV角色对
function event_module.extract_cv_role_pairs(app_state)
  if not app_state.sentences or #app_state.sentences == 0 then
    app_state.cv_role_pairs = {}
    return
  end

  -- 使用text_utils提取CV角色对
  if text_utils and text_utils.extract_cv_role_pairs then
    app_state.cv_role_pairs = text_utils.extract_cv_role_pairs(app_state.sentences)
  else
    -- 改进的CV角色对提取
    app_state.cv_role_pairs = {}
    local cv_pairs = {}  -- 用于去重

    for _, sentence in ipairs(app_state.sentences) do
      if sentence then
        -- 查找【角色-CV】格式
        for cv_role in sentence:gmatch("【(.-)】") do
          if cv_role:find("-") or cv_role:find("－") then
            local cv, role = cv_role:match("(.-)[-－](.+)")
            if cv and role then
              local trimmed_cv = trim(cv)
              local trimmed_role = trim(role)
              local key = trimmed_cv .. "-" .. trimmed_role
              if not cv_pairs[key] then
                cv_pairs[key] = true
                table.insert(app_state.cv_role_pairs, {cv = trimmed_cv, role = trimmed_role})
              end
            end
          end
        end

        -- 也查找简单的角色：CV格式
        local role, cv = sentence:match("([^：]+)：([^，。！？]+)")
        if role and cv then
          local trimmed_role = trim(role)
          local trimmed_cv = trim(cv)
          local key = trimmed_cv .. "-" .. trimmed_role
          if not cv_pairs[key] then
            cv_pairs[key] = true
            table.insert(app_state.cv_role_pairs, {cv = trimmed_cv, role = trimmed_role})
          end
        end
      end
    end
  end

  app_state.force_redraw = true
end

-- 提取章节信息
function event_module.extract_chapters(app_state)
  if not app_state.sentences or #app_state.sentences == 0 then
    app_state.chapters = {}
    return
  end

  app_state.chapters = {}

  -- 遍历所有句子，查找章节标记
  for i, sentence in ipairs(app_state.sentences) do
    if sentence then
      -- 查找章节标记：[CHAPTER]章节标题[/CHAPTER]
      local chapter_title = sentence:match("%[CHAPTER%](.-)%[/CHAPTER%]")
      if chapter_title then
        table.insert(app_state.chapters, {
          title = trim(chapter_title),
          sentence_idx = i
        })
      end

      -- 也查找简单的章节格式：第X章
      if not chapter_title then
        local simple_chapter = sentence:match("第%d+章[^。！？]*")
        if simple_chapter then
          table.insert(app_state.chapters, {
            title = trim(simple_chapter),
            sentence_idx = i
          })
        end
      end
    end
  end

  app_state.force_redraw = true
end

-- 检查音频块选择变化并自动更新集数
function event_module.check_selection_changed(app_state)
  local current_time = r.time_precise()

  -- 防止频繁检查，设置冷却时间
  if current_time - event_state.last_selection_check_time < event_state.item_selection_change_cooldown then
    return false
  end
  event_state.last_selection_check_time = current_time

  -- 获取当前选中的项目
  local selected_item = r.GetSelectedMediaItem(0, 0)

  -- 判断选择是否变更
  if selected_item ~= event_state.last_selected_item then
    event_state.last_selected_item = selected_item

    -- 如果有新选中的项目，检查是否需要更新集数
    if selected_item then
      local take = r.GetActiveTake(selected_item)
      if take then
        local item_name = r.GetTakeName(take)
        -- 检查项目名称是否变更
        if item_name ~= event_state.last_selected_item_name then
          event_state.last_selected_item_name = item_name

          -- 自动获取集数并更新
          if utils_module and utils_module.get_episode_from_selected_item then
            local auto_episode = utils_module.get_episode_from_selected_item()
            if auto_episode and auto_episode ~= "" and (app_state.episode_number == "" or auto_episode ~= app_state.episode_number) then
              app_state.episode_number = auto_episode
              app_state.force_redraw = true  -- 强制重绘界面以立即显示新集数
              utils_module.info("自动更新集数: " .. auto_episode)
              return true
            end
          end
        end
      end
    else
      -- 清除上次记录的项目名称
      event_state.last_selected_item_name = ""
    end
  end

  return false
end

-- 字符串trim函数
if not string.trim then
  function string.trim(s)
    return s:match("^%s*(.-)%s*$")
  end
end

return event_module