-- 样式模块 - 处理界面样式相关的逻辑
local r = reaper

-- 初始化样式模块
local style_module = {}

-- 获取模块加载器
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"
local module_loader = dofile(script_dir .. "module_loader.lua")
local utils_module = {}

-- 模块初始化函数
function style_module.init(deps)
  -- 使用依赖注入获取工具模块
  if deps and deps.utils_module then
    utils_module = deps.utils_module
  else
    -- 如果未提供依赖，则尝试直接加载
    utils_module = module_loader.load("utils_module") or {}
  end

  -- 初始化UI元素
  style_module.ui_elements = style_module.init_ui_elements()

  return style_module
end

-- 窗口设置
style_module.window_w = 800
style_module.window_h = 850

-- 章节设置
style_module.chapter_width = 150  -- 将章节宽度定义为模块的属性

-- 输入区域设置
style_module.input_spacing = 10  -- 输入元素之间的间距
style_module.input_height = 20   -- 输入元素的高度

-- 字体设置
style_module.font_size = 20  -- 界面字体大小（放大一个尺寸）
style_module.content_font_size = 20  -- 文本内容区域字体大小
style_module.font_name = "Microsoft YaHei" -- 使用微软雅黑作为中文字体

-- 统一的高亮颜色
style_module.highlight_color = {r = 0.4, g = 0.8, b = 0.5, a = 0.3}

-- 按钮状态跟踪
style_module.button_states = {}
style_module.hover_buttons = {}
style_module.pressed_buttons = {}
style_module.last_mouse_state = 0  -- 保留这个变量用于其他可能的状态跟踪
style_module.button_press_timers = {}  -- 添加按钮按下状态的计时器

-- 按钮动画系统
style_module.button_animations = {}  -- 按钮动画状态跟踪
style_module.pending_button_actions = {}  -- 待执行的按钮动作

-- 添加常量
style_module.BUTTON_PRESS_DURATION = 0.15  -- 按钮按下状态持续时间，控制动画速度
style_module.VISUAL_FEEDBACK_STRENGTH = 1.2  -- 增加视觉反馈强度系数

-- 按钮动画状态常量
style_module.BUTTON_ANIM_IDLE = 0
style_module.BUTTON_ANIM_PRESSING = 1
style_module.BUTTON_ANIM_PRESSED = 2
style_module.BUTTON_ANIM_RELEASING = 3

-- 内容区域拖拽调整大小相关变量
style_module.content_area_height = 500  -- 默认内容区域高度
style_module.is_resizing_content = false  -- 是否正在调整内容区域大小
style_module.resize_start_y = 0  -- 开始拖拽时的Y坐标
style_module.resize_start_height = 0  -- 开始拖拽时的内容区域高度
style_module.min_content_height = 500  -- 内容区域最小高度
style_module.max_content_height = 950  -- 内容区域最大高度
style_module.chapter_list_height = 500  -- 章节列表高度，与内容区域同步

-- 添加缓动函数
local function ease_out_quad(t)
  return 1 - (1 - t) * (1 - t)
end

local function ease_in_out_quad(t)
  return t < 0.5 and 2 * t * t or 1 - (-2 * t + 2)^2 / 2
end

-- 导出缓动函数
local M = {
  ease_out_quad = ease_out_quad,
  ease_in_out_quad = ease_in_out_quad
}

-- 界面元素位置和大小
function style_module.init_ui_elements()
  local window_w, window_h = style_module.window_w, style_module.window_h

  -- 实时获取当前窗口尺寸，如果gfx.w存在且不为0则使用，否则使用默认值
  if gfx and gfx.w and gfx.w > 0 then
    window_w = gfx.w
  end
  if gfx and gfx.h and gfx.h > 0 then
    window_h = gfx.h
  end

  local ui = {}

  -- 章节列表区域（添加在左侧）
  local chapter_width = style_module.chapter_width -- 使用模块的chapter_width属性
  local chapter_height = style_module.chapter_list_height or 500 -- 使用模块的章节列表高度
  ui.chapter_list = {x = 20, y = 50, w = chapter_width, h = chapter_height}

  -- 内容区域（调整宽度以适应章节列表）
  -- 使用全局变量来保存内容区域的高度，如果不存在则使用默认值
  local content_height = style_module.content_area_height or 500
  ui.content_area = {x = 20 + chapter_width + 10, y = 50, w = window_w - 40 - chapter_width - 10, h = content_height} -- 合并文本区域和句子列表为一个内容区域

  -- 添加垂直间距
  local vertical_margin = 20 -- 增加垂直间距，使布局更加宽松

  -- 标签和复选框的水平位置
  local label_x = 20
  local label_width = 150

  -- 将标签文本作为模块属性导出
  style_module.cv_role_label_text = "角色-CV"

  -- 先定义CV角色列表位置
  -- 增加与上方内容区域的间距
  local content_bottom_margin = 75 -- 调整内容区域与下方元素的间距为更合适的值
  ui.cv_role_list = {x = 20, y = ui.content_area.y + ui.content_area.h + content_bottom_margin, w = (window_w - 60) / 3, h = 200} -- 调整CV角色列表位置和宽度
  ui.cv_role_scrollbar = {x = ui.cv_role_list.x + ui.cv_role_list.w - 10, y = ui.cv_role_list.y, w = 10, h = ui.cv_role_list.h}

  -- 添加角色CV位置切换复选框（移到角色列表标签右边）
  local checkbox_size = style_module.font_size * 0.8  -- 根据字体大小调整复选框大小
  -- 计算标签文本宽度，用于定位复选框
  style_module.label_text_width = 80  -- 估算标签文本宽度
  ui.cv_role_reverse_checkbox = {x = ui.cv_role_list.x + style_module.label_text_width + 10, y = ui.cv_role_list.y - 20, w = checkbox_size, h = checkbox_size} -- 复选框
  ui.cv_role_reverse_label = {x = ui.cv_role_reverse_checkbox.x + checkbox_size + 5, y = ui.cv_role_list.y - 20, w = label_width, h = 20} -- 标签

  -- 定义按钮与画本框之间的固定间距
  local button_spacing = 20  -- 按钮之间的间距
  local button_width = 100   -- 标准按钮宽度
  local small_button_width = 30  -- 小按钮宽度
  local button_height = 30   -- 按钮高度
  local top_margin = 40      -- 按钮与画本框顶部的间距，增加间距避免重叠

  -- 内容区域底部的拖拽调整大小区域
  ui.content_resize_handle = {
    x = ui.content_area.x,
    y = ui.content_area.y + ui.content_area.h,
    w = ui.content_area.w,
    h = 8  -- 拖拽区域高度
  }

  -- 按钮布局从左到右按顺序定义，避免相互引用导致的nil值错误
  local buttons_y = ui.content_area.y + ui.content_area.h + ui.content_resize_handle.h + 10 -- 按钮的Y坐标，在内容区域和拖拽区域下方
  local button_spacing = 15 -- 按钮之间的间距
  local start_x = 20 -- 第一个按钮的X坐标

  -- 从左到右按顺序定义所有按钮
  -- 1. 播放按钮
  ui.play_button = {x = start_x, y = buttons_y, w = 100, h = 30} -- 播放/暂停按钮

  -- 2. 速率控制按钮
  ui.rate_minus_button = {x = ui.play_button.x + ui.play_button.w + button_spacing, y = buttons_y, w = 30, h = 30} -- 减速按钮
  ui.rate_display_area = {x = ui.rate_minus_button.x + ui.rate_minus_button.w + 5, y = buttons_y, w = 30, h = 30} -- 速率显示区域
  ui.rate_plus_button = {x = ui.rate_display_area.x + ui.rate_display_area.w + 5, y = buttons_y, w = 30, h = 30} -- 加速按钮
  ui.rate_reset_button = {x = ui.rate_plus_button.x + ui.rate_plus_button.w + 5, y = buttons_y, w = 30, h = 30} -- 重置速率按钮

  -- 3. 块标、区标和写入报告按钮
  ui.block_mark_button = {x = ui.rate_reset_button.x + ui.rate_reset_button.w + button_spacing, y = buttons_y, w = 50, h = 30} -- 块标按钮
  ui.region_mark_button = {x = ui.block_mark_button.x + ui.block_mark_button.w + 5, y = buttons_y, w = 50, h = 30} -- 区标按钮
  ui.excel_button = {x = ui.region_mark_button.x + ui.region_mark_button.w + button_spacing, y = buttons_y, w = 100, h = 30} -- 写入报告按钮

  -- 4. 开、AU、区名、文名和轨色按钮
  ui.open_csv_button = {x = ui.excel_button.x + ui.excel_button.w + button_spacing, y = buttons_y, w = 30, h = 30} -- 打开CSV文件按钮
  ui.au_button = {x = ui.open_csv_button.x + ui.open_csv_button.w + 10, y = buttons_y, w = 30, h = 30} -- AU脚本按钮
  ui.region_name_button = {x = ui.au_button.x + ui.au_button.w + 10, y = buttons_y, w = 30, h = 30} -- 区名按钮
  ui.file_name_button = {x = ui.region_name_button.x + ui.region_name_button.w + 10, y = buttons_y, w = 30, h = 30} -- 文名按钮
  ui.track_color_button = {x = ui.file_name_button.x + ui.file_name_button.w + 10, y = buttons_y, w = 30, h = 30} -- 轨色按钮
  ui.track_split_button = {x = ui.track_color_button.x + ui.track_color_button.w + 10, y = buttons_y, w = 30, h = 30} -- 分轨按钮

  -- 定义剪贴板和读取文档按钮位置 - 保持在原来的位置
  ui.clipboard_button = {x = ui.content_area.x + ui.content_area.w - button_width, y = ui.content_area.y - top_margin, w = button_width, h = button_height} -- 剪贴板按钮
  ui.document_button = {x = ui.clipboard_button.x - button_width - button_spacing, y = ui.content_area.y - top_margin, w = button_width, h = button_height} -- 读取文档按钮

  -- 字体调整按钮位置 - 保持在原来的位置
  ui.font_size_display = {x = ui.document_button.x - small_button_width - button_spacing, y = ui.content_area.y - top_margin, w = small_button_width, h = button_height} -- 字体大小显示区域
  ui.font_increase_button = {x = ui.font_size_display.x - small_button_width - 5, y = ui.content_area.y - top_margin, w = small_button_width, h = button_height} -- 字体放大按钮
  ui.font_decrease_button = {x = ui.font_increase_button.x - small_button_width - 5, y = ui.content_area.y - top_margin, w = small_button_width, h = button_height} -- 字体缩小按钮
  ui.font_size_label = {x = ui.font_decrease_button.x - 70, y = ui.content_area.y - top_margin, w = 70, h = button_height} -- 字体大小标签

  -- 添加对轨按钮定义，放在字体大小标签左边
  ui.track_align_button = {x = ui.font_size_label.x - 80, y = ui.content_area.y - top_margin, w = 30, h = 30}  -- 对轨按钮

  -- 添加章节按钮定义，放在对轨按钮右边
  ui.chapter_button = {x = ui.track_align_button.x + 35, y = ui.content_area.y - top_margin, w = 30, h = 30}  -- 章节按钮

  -- 调整输入区域布局 - 基于按钮区域底部位置
  local input_area_y = buttons_y + 57  -- 在按钮区域下方，给按钮留出足够空间
  local input_area_h = 150
  local input_area_w = (window_w - 60) / 4
  local input_spacing = style_module.input_spacing  -- 使用模块定义的间距
  local input_height = style_module.input_height  -- 使用模块定义的高度

  -- 垂直排列四个输入框
  local input_x = ui.cv_role_list.x + ui.cv_role_list.w + input_spacing
  local input_w = input_area_w * 0.8  -- 缩短宽度为原来的2/3
  local input_gap = 25  -- 增加输入框之间的间距

  -- 第一行：集数
  ui.episode_input = {x = input_x, y = input_area_y, w = input_w, h = input_height}

  -- 第二行：错误描述
  ui.error_input = {x = input_x, y = ui.episode_input.y + input_height + input_gap, w = input_w, h = input_height}

  -- 第三行：正确表达
  ui.correct_input = {x = input_x, y = ui.error_input.y + input_height + input_gap, w = input_w, h = input_height}

  -- 第四行：处理建议
  ui.suggestion_input = {x = input_x, y = ui.correct_input.y + input_height + input_gap, w = input_w, h = input_height}

  -- 滚动条相关
  ui.sentence_scrollbar = {x = ui.content_area.x + ui.content_area.w - 10, y = ui.content_area.y, w = 10, h = ui.content_area.h}

  -- 选择区域
  -- 计算选择区域的宽度，使其右边缘与内容框对齐
  local selection_width = (ui.content_area.x + ui.content_area.w) - (ui.episode_input.x + ui.episode_input.w + input_spacing)
  ui.selection_area = {x = ui.episode_input.x + ui.episode_input.w + input_spacing, y = input_area_y, w = selection_width, h = ui.cv_role_list.h}

  -- 添加内容区域调整大小的拖拽区域（在内容区域底部边缘）
  ui.content_resize_handle = {
    x = ui.content_area.x,
    y = ui.content_area.y + ui.content_area.h - 5,  -- 在底部边缘
    w = ui.content_area.w,
    h = 10  -- 拖拽区域高度
  }

  return ui
end

-- 颜色定义
style_module.colors = {
  background = {r = 0.2, g = 0.2, b = 0.2, a = 1},  -- 深灰背景色

  -- 主要按钮颜色
  button = {r = 0.25, g = 0.6, b = 0.35, a = 1},     -- 金属绿按钮
  button_disabled = {r = 0.5, g = 0.5, b = 0.5, a = 1}, -- 灰色禁用按钮

  -- 播放控制按钮
  button_play = {r = 0.2, g = 0.65, b = 0.3, a = 1},   -- 翡翠绿播放按钮
  button_pause = {r = 0.75, g = 0.2, b = 0.2, a = 1},  -- 朱红暂停按钮

  -- 速率控制按钮
  button_rate = {r = 0.25, g = 0.4, b = 0.75, a = 1},   -- 宝蓝速率按钮
  button_rate_minus = {r = 0.2, g = 0.5, b = 0.8, a = 1}, -- 浅蓝减速按钮
  button_rate_plus = {r = 0.2, g = 0.5, b = 0.8, a = 1},  -- 浅蓝加速按钮

  -- 工具按钮
  button_font = {r = 0.4, g = 0.4, b = 0.7, a = 1},     -- 紫蓝字体按钮
  button_excel = {r = 0.1, g = 0.6, b = 0.4, a = 1},     -- 绿松石Excel按钮
  button_mark = {r = 0.7, g = 0.5, b = 0.2, a = 1},      -- 金黄打标按钮
  button_document = {r = 0.3, g = 0.6, b = 0.7, a = 1},  -- 海蓝文档按钮
  button_clipboard = {r = 0.3, g = 0.6, b = 0.7, a = 1},  -- 海蓝剪贴板按钮

  -- 其他功能按钮
  button_open_csv = {r = 0.6, g = 0.3, b = 0.3, a = 1},      -- 红棕色打开CSV按钮
  button_au = {r = 0.3, g = 0.3, b = 0.6, a = 1},            -- 深蓝AU按钮
  button_region_name = {r = 0.5, g = 0.4, b = 0.6, a = 1},   -- 紫灰色区名按钮
  button_file_name = {r = 0.6, g = 0.5, b = 0.4, a = 1},     -- 棕色文名按钮
  button_track_color = {r = 0.7, g = 0.3, b = 0.7, a = 1},   -- 紫色轨色按钮
  button_track_split = {r = 0.3, g = 0.7, b = 0.3, a = 1},   -- 绿色分轨按钮
  text = {r = 1, g = 1, b = 1, a = 1},                -- 白色文本
  content_text = {r = 0, g = 0, b = 0, a = 1},        -- 修改为黑色文本
  highlight = {r = 0.4, g = 0.8, b = 0.5, a = 0.3},   -- 半透明浅绿色高亮
  time = {r = 1, g = 1, b = 0, a = 1},                -- 黄色时间信息
  scrollbar_bg = {r = 0.5, g = 0.5, b = 0.5, a = 1},  -- 灰色滚动条背景
  scrollbar_fg = {r = 0.8, g = 0.8, b = 0.8, a = 1},  -- 亮灰色滚动条前景
  input_bg = {r = 0.2, g = 0.2, b = 0.2, a = 1},      -- 深灰色输入框背景
  dropdown_bg = {r = 0.25, g = 0.25, b = 0.25, a = 1}, -- 深灰色下拉菜单背景
  dropdown_hover = {r = 0.3, g = 0.5, b = 0.7, a = 1},  -- 蓝色下拉菜单悬停
  button_highlight = {r = 0.3, g = 0.8, b = 0.3, a = 1}, -- 按钮高亮颜色
  button_shadow = {r = 0.1, g = 0.3, b = 0.1, a = 1},    -- 按钮阴影颜色
  button_dark_shadow = {r = 0.05, g = 0.15, b = 0.05, a = 1}, -- 按钮深色阴影
  bg = {r = 0.15, g = 0.15, b = 0.15, a = 1},            -- 窗口背景色
  content_bg = {r = 0.98, g = 0.92, b = 0.84, a = 1},    -- 羊皮纸色背景
  content_border = {r = 0.4, g = 0.4, b = 0.4, a = 1},   -- 内容区域边框色
  text_light = {r = 0.8, g = 0.8, b = 0.8, a = 1},        -- 亮色文本
}

-- 设置颜色
function style_module.set_color(color)
  -- 检查utils_module是否可用
  if utils_module and utils_module.set_color then
    utils_module.set_color(color)
  else
    -- 如果utils_module不可用，使用内部实现
    if not color then
      if r and r.ShowConsoleMsg then
        r.ShowConsoleMsg("set_color: 无效的颜色参数\n")
      end
      gfx.set(1, 1, 1, 1)  -- 默认白色
      return
    end

    if type(color) == "table" then
      local r_val = color.r or 1
      local g_val = color.g or 1
      local b_val = color.b or 1
      local a_val = color.a or 1

      -- 确保颜色值在有效范围内
      r_val = math.max(0, math.min(1, r_val))
      g_val = math.max(0, math.min(1, g_val))
      b_val = math.max(0, math.min(1, b_val))
      a_val = math.max(0, math.min(1, a_val))

      gfx.set(r_val, g_val, b_val, a_val)
    elseif type(color) == "number" then
      -- 处理整数颜色值
      local r_val = ((color >> 16) & 0xFF) / 255
      local g_val = ((color >> 8) & 0xFF) / 255
      local b_val = (color & 0xFF) / 255
      gfx.set(r_val, g_val, b_val, 1)
    else
      gfx.set(1, 1, 1, 1)  -- 默认白色
    end
  end
end

-- 生成按钮唯一ID
function style_module.get_button_id(button)
  -- 使用缓存优化，避免重复计算
  if not button._id then
    button._id = string.format("%d_%d_%d_%d", button.x, button.y, button.w, button.h)
  end
  return button._id
end

-- 检查按钮状态
function style_module.is_button_hovered(button)
  local id = style_module.get_button_id(button)
  return style_module.hover_buttons[id] or false
end

function style_module.is_button_pressed(button)
  local id = style_module.get_button_id(button)
  -- 只检查pressed状态，移除toggle状态检查
  return style_module.pressed_buttons[id] or false
end

-- 设置按钮状态
function style_module.set_button_hover(button, is_hover)
  local id = style_module.get_button_id(button)
  style_module.hover_buttons[id] = is_hover
end

function style_module.set_button_pressed(button, is_pressed)
  local id = style_module.get_button_id(button)
  style_module.pressed_buttons[id] = is_pressed
end

-- 绘制按钮
function style_module.draw_button(button, text, color)
  -- 直接使用增强金属质感按钮函数
  style_module.draw_enhanced_metal_button(button, text, color)
end

-- 绘制输入框
function style_module.draw_input(input, text, placeholder)
  -- 绘制输入框背景
  style_module.set_color(style_module.colors.input_bg)
  gfx.rect(input.x, input.y, input.w, input.h)

  -- 绘制黑色边框
  style_module.set_color({r = 0, g = 0, b = 0, a = 1})
  gfx.rect(input.x, input.y, input.w, input.h, false)

  -- 绘制文本
  style_module.set_color(style_module.colors.text)

  -- 设置输入框文字字体
  gfx.setfont(1, style_module.font_name, style_module.font_size)

  -- 估算文字高度（通常字体高度约为当前字体大小的0.9）
  -- 添加更强的防御性编程
  local _, _, raw_font_size = gfx.getfont()
  -- 确保font_size有一个有效值，如果gfx.getfont()失败则使用默认值20
  local font_size = raw_font_size or style_module.font_size or 20
  local text_h = font_size * 0.9  -- 增加系数，使文字更加居中

  -- 计算垂直居中位置
  local center_y = input.y + (input.h - text_h) / 2 - 1  -- 微调垂直位置，使文字更加居中

  gfx.x, gfx.y = input.x + 5, center_y
  gfx.drawstr(text ~= "" and text or placeholder)
end

-- 绘制滚动条
function style_module.draw_scrollbar(scrollbar, scroll_pos, max_scroll, visible_items, total_items)
  -- 如果内容不需要滚动，则不显示滚动条
  if total_items <= visible_items then return end

  -- 计算滚动条高度，确保最小高度为20像素
  local scroll_bar_height = math.max(20, scrollbar.h * visible_items / total_items)

  -- 计算滚动位置比例
  local scroll_pos_ratio = 0
  if max_scroll > 0 then
    scroll_pos_ratio = scroll_pos / max_scroll
  end

  -- 计算滚动条Y坐标
  local scroll_bar_y = scrollbar.y + (scrollbar.h - scroll_bar_height) * scroll_pos_ratio

  -- 绘制滚动条背景
  style_module.set_color(style_module.colors.scrollbar_bg)
  gfx.rect(scrollbar.x, scrollbar.y, scrollbar.w, scrollbar.h)

  -- 绘制滚动条前景
  style_module.set_color(style_module.colors.scrollbar_fg)
  gfx.rect(scrollbar.x, scroll_bar_y, scrollbar.w, scroll_bar_height)
end

-- 判断点是否在矩形内的辅助函数
function style_module.is_point_in_rect(x, y, rect)
  -- 检查utils_module是否可用
  if utils_module and utils_module.is_point_in_rect then
    return utils_module.is_point_in_rect(x, y, rect)
  end

  -- 如果utils_module不可用，使用内部实现
  if not rect then return false end
  return x >= rect.x and x <= rect.x + rect.w and y >= rect.y and y <= rect.y + rect.h
end

-- 更新按钮状态
function style_module.update_button_states(buttons, mouse_x, mouse_y, mouse_cap)
  -- 使用更精确的时间计算方式
  local current_time = r.time_precise()  -- 使用REAPER的高精度时间函数

  -- 检测鼠标点击事件（从按下到释放的变化）
  local mouse_clicked = (style_module.last_mouse_state == 1 and mouse_cap == 0)
  -- 检测鼠标按下事件（从未按下到按下的变化）
  local mouse_pressed = (style_module.last_mouse_state == 0 and mouse_cap == 1)

  -- 缓存常用值，减少重复计算
  local button_press_duration = style_module.BUTTON_PRESS_DURATION
  local button_press_timers = style_module.button_press_timers

  for _, button in pairs(buttons) do
    local id = style_module.get_button_id(button)
    local is_hovering = style_module.is_point_in_rect(mouse_x, mouse_y, button)
    style_module.set_button_hover(button, is_hovering)

    -- 如果按钮正在动画中，跳过旧的状态管理逻辑
    if style_module.button_animations[id] then
      -- 按钮在动画中，不处理旧的状态逻辑
    else
      -- 优化按钮状态逻辑，使凹陷效果更加直接
      if is_hovering and mouse_cap == 1 then
        -- 鼠标在按钮上按下时，立即设置按下状态
        style_module.set_button_pressed(button, true)

        -- 更新计时器（仅在按下瞬间或计时器不存在时）
        if mouse_pressed or not button_press_timers[id] then
          button_press_timers[id] = current_time + button_press_duration
        end
      elseif button_press_timers[id] then
        -- 已有计时器，检查是否过期
        if current_time > button_press_timers[id] then
          -- 计时器过期，清除计时器并重置按钮状态
          button_press_timers[id] = nil
          style_module.set_button_pressed(button, false)
        else
          -- 计时器未过期，保持按钮按下状态
          style_module.set_button_pressed(button, true)
        end
      else
        -- 没有计时器且鼠标未按在按钮上，设置为未按下状态
        style_module.set_button_pressed(button, false)
      end
    end
  end

  -- 返回标志，表示是否有鼠标状态变化需要立即更新
  return mouse_pressed or mouse_clicked
end

-- 开始按钮动画并注册回调
function style_module.start_button_animation(button, callback)
  local id = style_module.get_button_id(button)
  local current_time = r.time_precise()

  -- 设置按钮动画状态
  style_module.button_animations[id] = {
    state = style_module.BUTTON_ANIM_PRESSING,
    start_time = current_time,
    press_end_time = current_time + style_module.BUTTON_PRESS_DURATION * 0.7,  -- 按下阶段占70%时间
    release_end_time = current_time + style_module.BUTTON_PRESS_DURATION,  -- 回弹阶段占30%时间
    callback = callback
  }

  -- 立即设置按钮为按下状态
  style_module.set_button_pressed(button, true)
  style_module.button_press_timers[id] = current_time + style_module.BUTTON_PRESS_DURATION
end

-- 更新按钮动画状态
function style_module.update_button_animations()
  local current_time = r.time_precise()
  local completed_animations = {}

  for button_id, anim in pairs(style_module.button_animations) do
    if anim.state == style_module.BUTTON_ANIM_PRESSING then
      -- 检查按下动画是否完成
      if current_time >= anim.press_end_time then
        anim.state = style_module.BUTTON_ANIM_RELEASING
        -- 重置按钮状态，开始回弹
        style_module.pressed_buttons[button_id] = false
        style_module.button_press_timers[button_id] = nil
      end
    elseif anim.state == style_module.BUTTON_ANIM_RELEASING then
      -- 检查回弹动画是否完成
      if current_time >= anim.release_end_time then
        -- 动画完成，标记为待清理
        table.insert(completed_animations, button_id)

        -- 执行回调函数
        if anim.callback then
          anim.callback()
        end
      end
    end
  end

  -- 清理完成的动画
  for _, button_id in ipairs(completed_animations) do
    style_module.button_animations[button_id] = nil
  end
end

-- 检查按钮是否正在动画中
function style_module.is_button_animating(button)
  local id = style_module.get_button_id(button)
  return style_module.button_animations[id] ~= nil
end

-- 处理滚轮滚动
function style_module.handle_wheel_scroll(area, scroll_pos, items_count, visible_items, mouse_x, mouse_y, item_height)
  -- 防止nil值：如果item_height未提供，尝试使用style_module.font_size，如果也是nil则使用默认值18
  if not item_height then
    -- 缓存计算结果，避免重复计算
    if not style_module._default_item_height then
      local default_font_size = 18
      local font_size = style_module.font_size or default_font_size
      style_module._default_item_height = font_size + 2  -- 默认使用字体大小+2作为行高
    end
    item_height = style_module._default_item_height
  end

  -- 检查鼠标是否在区域内
  if style_module.is_point_in_rect(mouse_x, mouse_y, area) then
    local max_scroll = math.max(0, items_count - visible_items)

    -- 获取鼠标滚轮值
    local wheel_value = gfx.mouse_wheel

    -- 使用缓动函数计算滚动量
    local base_scroll = wheel_value < 0 and 3 or -3
    -- 使用简单的二次缓动函数
    local abs_base_scroll = math.abs(base_scroll)
    -- 直接使用二次方程，不依赖M.ease_out_quad
    local eased_scroll = (abs_base_scroll * abs_base_scroll) / 3 * (base_scroll > 0 and 1 or -1)

    -- 限制滚动范围
    local new_scroll_pos = math.max(0, math.min(max_scroll, scroll_pos + eased_scroll))

    -- 确保滚动位置为整数
    return math.floor(new_scroll_pos)
  end
  return scroll_pos
end

-- 处理滚动条拖动
function style_module.handle_scrollbar_drag(scrollbar, mouse_y, items_count, visible_items, callback)
  -- 如果没有需要滚动的内容，直接返回0
  if items_count <= visible_items then
    return 0
  end

  -- 计算最大滚动位置
  local max_scroll = items_count - visible_items

  -- 计算鼠标相对位置和滚动比例（合并计算步骤）
  local scroll_ratio = math.max(0, math.min(1, (mouse_y - scrollbar.y) / scrollbar.h))

  -- 计算新的滚动位置
  local scroll_pos = math.floor(scroll_ratio * max_scroll)

  -- 如果提供了回调函数，调用它
  if callback then
    callback(scroll_pos)
  end

  return scroll_pos
end

-- 绘制增强金属质感按钮（带悬浮阴影效果）
function style_module.draw_enhanced_metal_button(button, text, color)
  -- 验证参数
  if not button then return end
  if not text then text = "" end
  if not color then
    color = style_module.colors.button or {r = 0.25, g = 0.6, b = 0.35, a = 1}
  end

  local id = style_module.get_button_id(button)
  local is_hovered = style_module.is_button_hovered(button)
  local is_pressed = style_module.is_button_pressed(button)

  -- 调整按钮位置和颜色
  local btn_x, btn_y, btn_w, btn_h = button.x, button.y, button.w, button.h
  local text_offset_x, text_offset_y = 0, 0

  -- 阴影偏移量，创造悬浮效果
  local shadow_offset = 5  -- 适度的阴影偏移量，提供悬浮感但不过分大

  -- 根据按钮状态调整渲染方式
  if is_pressed then
    -- 按下状态：按钮整体向下偏移，与阴影重合
    local offset = shadow_offset - 2  -- 按钮偏移量，与阴影偏移量相关

    -- 调整文字偏移量，使文字与按钮一起偏移
    text_offset_x = offset
    text_offset_y = offset

    -- 绘制阴影（位置不变，按钮会偏移到阴影位置）
    style_module.set_color({r = 0, g = 0, b = 0, a = 0.2})
    gfx.rect(button.x + offset, button.y + offset, btn_w, btn_h)

    -- 直接绘制按钮主体（偏移后的位置）
    local pressed_color = {
      r = math.max(color.r - 0.15, 0),  -- 按下时颜色稍暗
      g = math.max(color.g - 0.15, 0),
      b = math.max(color.b - 0.15, 0),
      a = color.a
    }
    style_module.set_color(pressed_color)
    gfx.rect(button.x + offset, button.y + offset, btn_w, btn_h)

    -- 绘制金属效果的深色渐变（上半部分）
    local gradient_height = btn_h / 2
    for i = 0, gradient_height do
      local alpha = 0.2 * (1 - i / gradient_height)  -- 增强深色效果
      style_module.set_color({r = 0, g = 0, b = 0, a = alpha})
      gfx.rect(button.x + offset, button.y + offset + i, btn_w, 1)
    end

    -- 绘制底部的微弱高光（增强金属感）
    local highlight_height = btn_h / 4
    local highlight_y = button.y + offset + btn_h - highlight_height
    for i = 0, highlight_height do
      local alpha = 0.1 * (i / highlight_height)  -- 从底部开始逐渐增强
      style_module.set_color({r = 1, g = 1, b = 1, a = alpha})
      gfx.rect(button.x + offset + 1, highlight_y + i, btn_w - 2, 1)
    end
  else
    -- 正常/悬停状态：增强金属效果和悬浮阴影
    local base_color = color
    if is_hovered then
      -- 悬停时使用更亮的颜色和更大的阴影
      base_color = {
        r = math.min(color.r + 0.3, 1),  -- 显著增强高亮效果
        g = math.min(color.g + 0.3, 1),
        b = math.min(color.b + 0.3, 1),
        a = color.a
      }
      shadow_offset = 7  -- 悬停时适度增大阴影偏移，增强悬浮感但不过分
    end

    -- 绘制统一的偏移阴影（创造悬浮效果）
    -- 阴影向右下角偏移，显著增强悬浮感

    -- 阴影外扩范围，适度增大阴影范围
    local shadow_expand = 2  -- 阴影外扩像素数，适度调整

    -- 绘制统一的阴影（右下方偏移）
    for i = 1, shadow_offset do
      -- 计算阴影透明度，从按钮边缘开始逐渐减小
      -- 使用平方函数使透明度过渡更加柔和
      local progress = (i - 1) / shadow_offset
      local alpha = 0.15 * (1 - progress * progress)  -- 显著降低阴影透明度，使阴影更淡
      style_module.set_color({r = 0, g = 0, b = 0, a = alpha})

      -- 计算右侧偏移，使阴影向右下方偏移
      local right_offset = math.floor(i * 1.3)  -- 适度的右侧偏移系数

      -- 计算阴影宽度和高度，使阴影向外适度扩展
      local shadow_width = btn_w - right_offset + shadow_expand
      local shadow_height = btn_h - i + shadow_expand

      -- 使用多层绘制使阴影更加柔和
      -- 第一层：主要阴影
      gfx.rect(button.x + right_offset, button.y + i, shadow_width, shadow_height)

      -- 第二层：添加柔和过渡边缘
      if i < shadow_offset then
        local blur_alpha = alpha * 0.3  -- 显著降低边缘模糊层的透明度
        style_module.set_color({r = 0, g = 0, b = 0, a = blur_alpha})
        -- 右侧模糊边缘
        gfx.rect(button.x + right_offset + shadow_width, button.y + i, 1, shadow_height)
        -- 底部模糊边缘
        gfx.rect(button.x + right_offset, button.y + i + shadow_height, shadow_width, 1)
      end

      -- 在阴影左侧添加小部分阴影，使阴影看起来更加自然
      if i <= shadow_offset / 4 then  -- 只在阴影前四分之一添加，进一步减少左侧阴影
        -- 使用平方函数使左侧阴影过渡更加柔和
        local left_progress = i / (shadow_offset / 4)
        local left_alpha = alpha * 0.2 * (1 - left_progress * left_progress)  -- 显著降低左侧阴影透明度
        style_module.set_color({r = 0, g = 0, b = 0, a = left_alpha})
        local left_width = math.floor(i * 0.25)  -- 进一步减少左侧阴影宽度
        gfx.rect(button.x - left_width, button.y + i, left_width, shadow_height)
      end
    end

    -- 悬停时添加柔和的额外阴影，增强悬浮感
    if is_hovered then
      -- 使用渐变效果使边缘阴影更加柔和
      for j = 1, 2 do
        local edge_alpha = 0.12 * (3 - j) / 2  -- 显著降低边缘阴影透明度
        style_module.set_color({r = 0, g = 0, b = 0, a = edge_alpha})
        -- 底部阴影渐变
        gfx.rect(button.x + shadow_offset, button.y + shadow_offset + j - 1, btn_w - shadow_offset + shadow_expand, 1)
        -- 右侧阴影渐变
        gfx.rect(button.x + shadow_offset + j - 1, button.y + shadow_offset, 1, btn_h - shadow_offset + shadow_expand)
      end
    end

    -- 直接绘制按钮主体，不再绘制黑色边框
    style_module.set_color(base_color)
    gfx.rect(button.x, button.y, btn_w, btn_h)

    -- 绘制金属效果的高光渐变（上半部分）
    local gradient_height = btn_h / 2
    local highlight_intensity = is_hovered and 0.7 or 0.5  -- 悬停时高光更强
    for i = 0, gradient_height do
      local alpha = highlight_intensity * (1 - i / gradient_height)  -- 增强高光效果
      style_module.set_color({r = 1, g = 1, b = 1, a = alpha})
      gfx.rect(button.x, button.y + i, btn_w, 1)
    end

    -- 悬停时添加额外的高光边缘，增强悬停反馈
    if is_hovered then
      style_module.set_color({r = 1, g = 1, b = 1, a = 0.4})
      gfx.rect(button.x, button.y, btn_w, 2)  -- 上边缘加宽
      gfx.rect(button.x, button.y, 2, btn_h)  -- 左边缘加宽
    end

    -- 绘制金属效果的暗色渐变（下半部分）
    local shadow_height = btn_h / 3
    local shadow_y = button.y + btn_h - shadow_height
    for i = 0, shadow_height do
      local alpha = 0.3 * (i / shadow_height)  -- 增强暗色效果
      style_module.set_color({r = 0, g = 0, b = 0, a = alpha})
      gfx.rect(button.x, shadow_y + i, btn_w, 1)
    end

    -- 绘制底部的微弱高光线（增强金属边缘感）
    style_module.set_color({r = 1, g = 1, b = 1, a = 0.2})
    gfx.rect(button.x + 1, button.y + btn_h - 1, btn_w - 2, 1)

    -- 移除上边缘额外高光，按照用户要求
  end

  -- 设置按钮文字字体
  gfx.setfont(1, style_module.font_name, style_module.font_size)

  -- 计算文字尺寸用于居中
  local text_w = gfx.measurestr(text)

  -- 估算文字高度（通常字体高度稍大于字体大小）
  local _, _, font_size = gfx.getfont()
  font_size = font_size or style_module.font_size or 20
  local text_h = font_size * 0.9  -- 增加文字高度系数，使文字在按钮中更居中

  -- 计算居中位置（水平和垂直都居中）
  local center_x = btn_x + (btn_w - text_w) / 2 + text_offset_x
  local center_y = btn_y + (btn_h - text_h) / 2 + text_offset_y - 1  -- 微调垂直位置，使文字更居中

  -- 根据按钮颜色确定文字颜色
  local base_text_color = style_module.colors.text  -- 默认白色文字

  -- 计算按钮颜色的亮度
  local brightness = (color.r * 0.299 + color.g * 0.587 + color.b * 0.114)

  -- 根据按钮亮度调整文字颜色
  local tinted_text_color = {}
  if brightness < 0.6 then
    -- 深色按钮使用白色文字
    tinted_text_color = {
      r = base_text_color.r,
      g = base_text_color.g,
      b = base_text_color.b,
      a = base_text_color.a
    }
  else
    -- 浅色按钮使用深色文字
    tinted_text_color = {
      r = 0.1,
      g = 0.1,
      b = 0.1,
      a = base_text_color.a
    }
  end

  if is_pressed then
    -- 按下状态下的文字效果

    -- 绘制实际文字（按下时颜色稍暗）
    local pressed_text_color = {
      r = math.max(tinted_text_color.r - 0.1, 0),
      g = math.max(tinted_text_color.g - 0.1, 0),
      b = math.max(tinted_text_color.b - 0.1, 0),
      a = tinted_text_color.a
    }
    style_module.set_color(pressed_text_color)
    gfx.x, gfx.y = center_x, center_y
    gfx.drawstr(text)
  else
    -- 正常/悬停状态下的文字效果

    -- 绘制实际文字
    if is_hovered then
      -- 悬停时文字颜色更亮
      local hover_text_color = {
        r = math.min(tinted_text_color.r + 0.1, 1),
        g = math.min(tinted_text_color.g + 0.1, 1),
        b = math.min(tinted_text_color.b + 0.1, 1),
        a = tinted_text_color.a
      }
      style_module.set_color(hover_text_color)
    else
      style_module.set_color(tinted_text_color)
    end
    gfx.x, gfx.y = center_x, center_y
    gfx.drawstr(text)
  end
end

-- 绘制按钮轮廓（窗口拖动时使用的简化版本）
function style_module.draw_button_outline(button)
  -- 检查按钮是否有效
  if not button then return end

  -- 设置轮廓颜色
  style_module.set_color({r = 0.5, g = 0.5, b = 0.5, a = 0.8})

  -- 仅绘制轮廓
  gfx.rect(button.x, button.y, button.w, button.h, false)
end

-- 在style_module.draw_button_outline函数之前添加draw_rect函数
-- 绘制矩形函数
function style_module.draw_rect(x, y, w, h, color)
  -- 设置矩形颜色
  style_module.set_color(color)

  -- 绘制矩形
  gfx.rect(x, y, w, h, false)
end

-- 给UI元素添加黑色描边的函数
function style_module.draw_black_outline(x, y, w, h, line_width)
    -- 设置默认线宽为1像素
    line_width = line_width or 1

    -- 保存当前颜色状态
    local r, g, b, a = gfx.r, gfx.g, gfx.b, gfx.a

    -- 设置为黑色
    gfx.set(0, 0, 0, 1)

    -- 绘制描边（四条边框线）
    for i = 0, line_width - 1 do
        -- 顶部边框
        gfx.line(x - i, y - i, x + w + i, y - i)
        -- 左侧边框
        gfx.line(x - i, y - i, x - i, y + h + i)
        -- 底部边框
        gfx.line(x - i, y + h + i, x + w + i, y + h + i)
        -- 右侧边框
        gfx.line(x + w + i, y - i, x + w + i, y + h + i)
    end

    -- 恢复原始颜色状态
    gfx.set(r, g, b, a)
end

-- 绘制现代化滑动按钮（替代复选框）
function style_module.draw_toggle_switch(toggle, label_text, is_on)
  -- 滑动按钮尺寸
  local switch_width = toggle.w * 2.2  -- 滑动按钮宽度
  local switch_height = toggle.h * 0.7  -- 滑动按钮高度
  local knob_size = switch_height * 0.9  -- 滑块大小

  -- 计算滑块位置，根据开关状态决定
  local track_padding = 2  -- 轨道内边距
  local track_width = switch_width - track_padding * 2  -- 轨道实际宽度
  local knob_travel = track_width - knob_size  -- 滑块可移动距离
  local knob_x = toggle.x + track_padding + (is_on and knob_travel or 0)  -- 滑块X位置

  -- 计算垂直位置，使滑块垂直居中
  local knob_y = toggle.y + (toggle.h - knob_size) / 2

  -- 绘制轨道背景（圆角矩形）
  local track_radius = switch_height / 2
  local track_y = toggle.y + (toggle.h - switch_height) / 2

  -- 设置轨道颜色（根据开关状态使用不同颜色）
  local track_color
  if is_on then
    -- 开启状态使用渐变效果，从浅蓝到深蓝
    track_color = {r=0.3, g=0.6, b=0.9, a=1}
  else
    -- 关闭状态使用深灰色
    track_color = {r=0.25, g=0.25, b=0.25, a=1}
  end

  -- 绘制轨道背景
  style_module.set_color(track_color)

  -- 绘制圆角矩形轨道
  -- 先绘制中间矩形
  gfx.rect(toggle.x + track_radius, track_y, switch_width - track_radius * 2, switch_height)

  -- 绘制左右两侧半圆
  for i=0, track_radius do
    local y_offset = math.sqrt(track_radius*track_radius - i*i)
    -- 左半圆
    gfx.rect(toggle.x + track_radius - i, track_y + track_radius - y_offset, 1, y_offset * 2)
    -- 右半圆
    gfx.rect(toggle.x + switch_width - track_radius + i, track_y + track_radius - y_offset, 1, y_offset * 2)
  end

  -- 如果开启状态，添加轨道内部高光效果
  if is_on then
    style_module.set_color({r=0.4, g=0.7, b=1.0, a=0.3})
    gfx.rect(toggle.x + track_radius, track_y + 1, switch_width - track_radius * 2, 2)
  end

  -- 绘制滑块（圆形，带高光和阴影效果）
  local knob_radius = knob_size / 2

  -- 绘制滑块阴影（先绘制阴影，让滑块看起来有立体感）
  style_module.set_color({r=0, g=0, b=0, a=0.2})
  for i=0, knob_radius do
    local y_offset = math.sqrt(knob_radius*knob_radius - i*i)
    for j=1, 2 do  -- 阴影偏移量
      gfx.rect(knob_x + knob_radius - i, knob_y + knob_radius - y_offset + j, 1, y_offset * 2)
      gfx.rect(knob_x + knob_radius + i, knob_y + knob_radius - y_offset + j, 1, y_offset * 2)
    end
  end

  -- 绘制滑块主体（白色圆形）
  style_module.set_color({r=0.95, g=0.95, b=0.95, a=1})
  for i=0, knob_radius do
    local y_offset = math.sqrt(knob_radius*knob_radius - i*i)
    gfx.rect(knob_x + knob_radius - i, knob_y + knob_radius - y_offset, 1, y_offset * 2)
    gfx.rect(knob_x + knob_radius + i, knob_y + knob_radius - y_offset, 1, y_offset * 2)
  end

  -- 绘制滑块顶部高光，增强立体感
  style_module.set_color({r=1, g=1, b=1, a=0.7})
  for i=0, knob_radius * 0.7 do
    local y_offset = math.sqrt((knob_radius * 0.7)*(knob_radius * 0.7) - i*i) * 0.5
    gfx.rect(knob_x + knob_radius - i, knob_y + knob_radius * 0.5 - y_offset, 1, y_offset * 2)
    gfx.rect(knob_x + knob_radius + i, knob_y + knob_radius * 0.5 - y_offset, 1, y_offset * 2)
  end

  -- 绘制标签文本，调整垂直位置使其居中
  style_module.set_color(style_module.colors.text)

  -- 估算文字高度
  local _, _, font_size = gfx.getfont()
  -- 确保font_size有一个有效值，如果gfx.getfont()失败则使用默认值
  font_size = font_size or style_module.font_size or 18
  local text_h = font_size * 0.8

  -- 计算垂直居中位置
  local center_y = toggle.y + (toggle.h - text_h) / 2

  gfx.x, gfx.y = toggle.x + switch_width + 10, center_y
  gfx.drawstr(label_text)
end

-- 绘制复选框
function style_module.draw_checkbox(checkbox, label_text, is_checked)
  -- 绘制复选框背景
  style_module.set_color(style_module.colors.input_bg)
  gfx.rect(checkbox.x, checkbox.y, checkbox.w, checkbox.h)

  -- 绘制边框
  style_module.set_color(style_module.colors.text)
  gfx.rect(checkbox.x, checkbox.y, checkbox.w, checkbox.h, false)

  -- 如果被选中，绘制勾选标记
  if is_checked then
    -- 绘制勾选标记（√）
    local x1, y1 = checkbox.x + checkbox.w * 0.2, checkbox.y + checkbox.h * 0.5
    local x2, y2 = checkbox.x + checkbox.w * 0.4, checkbox.y + checkbox.h * 0.7
    local x3, y3 = checkbox.x + checkbox.w * 0.8, checkbox.y + checkbox.h * 0.3

    style_module.set_color(style_module.colors.button)
    gfx.line(x1, y1, x2, y2)
    gfx.line(x2, y2, x3, y3)
  end

  -- 绘制标签文本，调整垂直位置使其居中
  style_module.set_color(style_module.colors.text)

  -- 估算文字高度
  local _, _, font_size = gfx.getfont()
  -- 确保font_size有一个有效值，如果gfx.getfont()失败则使用默认值
  font_size = font_size or style_module.font_size or 18
  local text_h = font_size * 0.8

  -- 计算垂直居中位置
  local center_y = checkbox.y + (checkbox.h - text_h) / 2

  gfx.x, gfx.y = checkbox.x + checkbox.w + 5, center_y
  gfx.drawstr(label_text)
end

-- 绘制金属效果高亮（用于内容区域、章节列表和CV角色列表）
function style_module.draw_metallic_highlight(rect_x, rect_y, rect_w, rect_h, base_color, is_selected)
  -- 设置基础颜色（选中状态和悬停状态使用不同的颜色深浅）
  local color = {
    r = base_color.r,
    g = base_color.g,
    b = base_color.b,
    a = base_color.a or (is_selected and 0.9 or 0.8)  -- 使用基础颜色的透明度，如果没有则使用默认值
  }

  -- 直接绘制主体，不绘制边框
  style_module.set_color(color)
  gfx.rect(rect_x, rect_y, rect_w, rect_h)

  -- 对于选中状态，保留金属效果
  if is_selected then
    -- 缓存计算结果，减少重复计算
    local gradient_height = rect_h / 2.2
    local shadow_height = rect_h / 3
    local shadow_y = rect_y + rect_h - shadow_height

    -- 使用表预先计算所有alpha值，减少循环中的计算
    local highlight_alphas = {}
    local shadow_alphas = {}

    for i = 0, gradient_height do
      highlight_alphas[i] = 0.4 * (1 - i / gradient_height)
    end

    for i = 0, shadow_height do
      shadow_alphas[i] = 0.15 * (i / shadow_height)
    end

    -- 绘制金属效果的高光渐变（上半部分）
    for i = 0, gradient_height do
      style_module.set_color({r = 1, g = 1, b = 1, a = highlight_alphas[i]})
      gfx.rect(rect_x, rect_y + i, rect_w, 1)
    end

    -- 绘制底部的微弱阴影（模拟金属反光）
    for i = 0, shadow_height do
      style_module.set_color({r = 0, g = 0, b = 0, a = shadow_alphas[i]})
      gfx.rect(rect_x, shadow_y + i, rect_w, 1)
    end
  end

  -- 恢复文本颜色
  style_module.set_color({r = 1, g = 1, b = 1, a = 1})
end

-- 预定义的高亮颜色
style_module.highlight_colors = {
  -- 绿色系（用于内容区域和章节列表）
  green = {
    selected = {r = 0.2, g = 0.6, b = 0.3, a = 0.9},  -- 选中状态（深绿色）
    hover = {r = 0.4, g = 0.8, b = 0.5, a = 0.3}      -- 悬停状态（半透明浅绿色）
  },
  -- 蓝色系（用于CV角色列表）
  blue = {
    selected = {r = 0.2, g = 0.4, b = 0.8, a = 0.9},   -- 选中状态（深蓝色）
    hover = {r = 0.25, g = 0.25, b = 0.7, a = 0.9},     -- 悬停状态（浅蓝色）
    cv = {r = 0.25, g = 0.25, b = 0.7, a = 0.9}         -- CV分类（蓝色）
  }
}

-- 获取主容器大小
function style_module.get_main_container_size()
  -- 默认主窗口宽度为800，高度为600
  local window_w, window_h = gfx.w, gfx.h

  -- 如果窗口尚未初始化，使用默认值
  if window_w <= 0 or window_h <= 0 then
    window_w, window_h = 800, 600
  end

  -- 确保窗口最小尺寸
  window_w = math.max(800, window_w)
  window_h = math.max(600, window_h)

  -- 计算主容器大小（为窗口大小减去边距）
  local container_x = style_module.margin
  local container_y = style_module.margin
  -- 增加可用宽度，减少右侧边距，使文本有更多空间显示
  local container_w = window_w - style_module.margin * 1.5  -- 从2减少到1.5
  local container_h = window_h - style_module.margin * 2

  return {
    x = container_x,
    y = container_y,
    w = container_w,
    h = container_h
  }
end

-- 计算内容区域大小（文本显示区域）
function style_module.get_content_area_size(main_container)
  local button_height = style_module.button_height
  local button_margin = style_module.button_margin

  -- 总按钮区域高度包括按钮高度、编辑栏高度和边距
  local total_buttons_height = button_height * 3 + button_margin * 6

  -- 增加内容区域宽度，给文本留出更多空间
  -- 内容区域在主容器内，保留左右边距，但增加水平空间
  local content_area = {
    x = main_container.x + 5,                   -- 减少左侧边距
    y = main_container.y + total_buttons_height,
    w = main_container.w - 10,                  -- 减少右侧边距，增加宽度
    h = style_module.content_area_height        -- 使用动态高度
  }

  return content_area
end

-- =====================================================
-- 按钮辅助功能（从button_helpers.lua合并）
-- =====================================================

-- 将UTF-8字符串转换为本地编码（Windows下为GBK）
function style_module.utf8_to_local(str)
  local function char_to_hex(c)
    return string.format("%%%02X", string.byte(c))
  end
  -- 简化实现，直接使用gsub
  return str:gsub("([^%w%.%- ])", char_to_hex):gsub(" ", "+")
end

-- 打开CSV文件
function style_module.open_csv_file()
  -- 获取REAPER的Scripts目录
  local reaper_path = r.GetResourcePath()
  -- 构建CSV文件路径
  local csv_path = reaper_path .. "/Scripts/审听报告.csv"

  -- 检查文件是否存在（只读模式）
  local file = io.open(csv_path, "r")
  if file then
    -- 文件存在，关闭句柄
    file:close()

    -- 使用JS API打开文件（如果可用）
    if r.JS_ShellExecute then
      -- 使用JS_ShellExecute打开文件，SW_SHOWNORMAL(1)参数确保正常显示窗口
      r.JS_ShellExecute(csv_path:gsub("/", "\\"), "", "", "open", 1)
      return "正在打开审听报告.csv (使用JS_ShellExecute)"
    else
      -- 降级到标准方法，但使用0参数隐藏CMD窗口
      r.ExecProcess('cmd.exe /c start "" "' .. csv_path:gsub("/", "\\") .. '"', 0)
      return "正在打开审听报告.csv"
    end
  else
    -- 文件不存在，显示错误消息
    r.MB("无法找到审听报告.csv文件，请先使用写入功能创建报告。", "文件不存在", 0)
    return "错误：审听报告.csv文件不存在"
  end
end

-- 运行AU脚本
function style_module.run_au_script()
  -- 构建完整的脚本路径
  local au_script_path = script_dir .. "JHKAU.lua"

  -- 检查脚本是否存在
  local file = io.open(au_script_path, "r")
  if file then
    file:close()

    -- 如果有JS API可用，使用JS_ShellExecute运行脚本
    if r.JS_ShellExecute then
      -- 使用JS_ShellExecute以"open"方式打开Lua脚本，REAPER会自动用Lua解释器执行它
      -- 使用SW_SHOWNORMAL(1)参数确保正常显示窗口，但不显示CMD窗口
      r.JS_ShellExecute(au_script_path, "", "", "open", 1)
      return "已执行JHKAU.lua脚本 (使用JS_ShellExecute)"
    else
      -- 降级到直接执行脚本
      dofile(au_script_path)
      return "已执行JHKAU.lua脚本"
    end
  else
    r.MB("无法找到JHKAU.lua脚本文件。", "脚本不存在", 0)
    return "错误：JHKAU.lua脚本不存在"
  end
end

-- =====================================================
-- 内容区域拖拽调整大小功能
-- =====================================================

-- 检查鼠标是否在拖拽区域内
function style_module.is_mouse_in_resize_handle(mouse_x, mouse_y, resize_handle)
  return mouse_x >= resize_handle.x and mouse_x <= resize_handle.x + resize_handle.w and
         mouse_y >= resize_handle.y and mouse_y <= resize_handle.y + resize_handle.h
end

-- 开始拖拽调整大小
function style_module.start_content_resize(mouse_y)
  style_module.is_resizing_content = true
  style_module.resize_start_y = mouse_y
  style_module.resize_start_height = style_module.content_area_height
  -- 确保章节列表高度与内容区域高度同步
  style_module.chapter_list_height = style_module.content_area_height
end

-- 处理拖拽调整大小
function style_module.handle_content_resize(mouse_y)
  if not style_module.is_resizing_content then
    return false
  end

  local delta_y = mouse_y - style_module.resize_start_y
  local new_height = style_module.resize_start_height + delta_y

  -- 限制在最小和最大高度之间
  new_height = math.max(style_module.min_content_height,
                       math.min(style_module.max_content_height, new_height))

  -- 同时调整内容区域和章节列表的高度
  style_module.content_area_height = new_height
  style_module.chapter_list_height = new_height  -- 章节列表与内容区域保持相同高度
  return true
end

-- 结束拖拽调整大小
function style_module.end_content_resize()
  style_module.is_resizing_content = false
end

-- 绘制金属高亮效果
function style_module.draw_metallic_highlight(x, y, w, h, color, is_active)
  if is_active then
    -- 活跃状态：绘制渐变高亮效果
    local steps = 5
    for i = 0, steps - 1 do
      local alpha = (steps - i) / steps * color.a * 0.8
      gfx.set(color.r, color.g, color.b, alpha)
      gfx.rect(x + i, y + i, w - i * 2, h - i * 2)
    end

    -- 绘制边框
    gfx.set(color.r * 1.2, color.g * 1.2, color.b * 1.2, color.a)
    gfx.rect(x, y, w, h, false)
  else
    -- 普通状态：简单的背景色
    gfx.set(color.r * 0.7, color.g * 0.7, color.b * 0.7, color.a * 0.5)
    gfx.rect(x, y, w, h)
  end
end

-- 绘制复选框
function style_module.draw_checkbox(checkbox, label_text, is_checked)
  -- 绘制复选框背景
  gfx.set(0.2, 0.2, 0.2, 1)  -- 深灰色背景
  gfx.rect(checkbox.x, checkbox.y, checkbox.w, checkbox.h)

  -- 绘制复选框边框
  gfx.set(0.6, 0.6, 0.6, 1)  -- 灰色边框
  gfx.rect(checkbox.x, checkbox.y, checkbox.w, checkbox.h, false)

  -- 如果选中，绘制勾选标记
  if is_checked then
    gfx.set(0.2, 0.8, 0.2, 1)  -- 绿色勾选
    local margin = checkbox.w * 0.2
    gfx.line(checkbox.x + margin, checkbox.y + checkbox.h * 0.5,
             checkbox.x + checkbox.w * 0.4, checkbox.y + checkbox.h * 0.7)
    gfx.line(checkbox.x + checkbox.w * 0.4, checkbox.y + checkbox.h * 0.7,
             checkbox.x + checkbox.w - margin, checkbox.y + margin)
  end

  -- 绘制标签文本
  if label_text then
    gfx.set(1, 1, 1, 1)  -- 白色文本
    gfx.x, gfx.y = checkbox.x + checkbox.w + 5, checkbox.y
    gfx.drawstr(label_text)
  end
end

-- 绘制拖拽区域的视觉提示
function style_module.draw_resize_handle(resize_handle, is_hovering)
  -- 计算内容区域底部和按钮区域顶部的中间位置
  local content_bottom = resize_handle.y  -- 内容区域底部
  local buttons_top = content_bottom + resize_handle.h + 10  -- 按钮区域顶部
  local middle_y = content_bottom + (buttons_top - content_bottom) / 2  -- 中间位置

  -- 计算指示线的长度和居中位置
  local window_w = gfx.w or style_module.window_w
  local line_length = window_w * 0.6  -- 指示线长度为窗口宽度的60%
  local line_start_x = (window_w - line_length) / 2  -- 居中显示
  local line_end_x = line_start_x + line_length

  -- 始终绘制拖拽指示线，让用户清楚看到可拖拽区域
  gfx.set(0.6, 0.6, 0.8, 0.8)  -- 明显的蓝灰色线条
  local line_spacing = 3
  for i = -1, 1 do
    local line_y = middle_y + i * line_spacing
    gfx.line(line_start_x, line_y, line_end_x, line_y)
  end

  -- 悬停或拖拽时增强高亮效果
  if is_hovering or style_module.is_resizing_content then
    -- 绘制更亮的拖拽指示线
    gfx.set(0.8, 0.8, 1, 0.9)  -- 亮蓝色
    for i = -1, 1 do
      local line_y = middle_y + i * line_spacing
      gfx.line(line_start_x, line_y, line_end_x, line_y)
    end
  end
end



-- 返回样式模块
return style_module