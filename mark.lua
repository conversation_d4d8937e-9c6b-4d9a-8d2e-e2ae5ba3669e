-- 打标工具 - REAPER脚本
-- 用于音频后期制作工作流程



-- 导入必要的库
local r = reaper

-- 导入播放控制模块和文本处理模块
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"  -- 添加默认路径

-- 使用简单的加载函数导入模块加载器
local function load_module(file_path)
  local success, result = pcall(dofile, file_path)
  if not success then
    return {}
  end
  return result
end

-- 导入模块加载器
local module_loader = load_module(script_dir .. "module_loader.lua")

-- 使用模块加载器加载所有核心模块
modules = module_loader.load_all({
  -- 设置模块配置选项
  show_init_message = false  -- 禁用初始化信息显示
})

-- 从模块集合中获取各个模块
local utils_module = modules.utils_module
-- playback_control已合并到utils_module中
local text_utils = modules.text_utils  -- 直接使用text_utils
local excel_module = modules.excel_module
local button_module = modules.button_module
local chapter_module = modules.chapter_module
local style_module = modules.style_module
-- button_helpers已合并到style_module中，不再单独加载
-- cache_module已合并到utils_module中
local word_module = modules.word_module  -- 添加word模块

-- button_helpers已合并到style_module中，不再需要单独加载
-- 确保style_module已正确加载
if not style_module then
  -- 尝试加载style_module模块
  style_module = load_module(script_dir .. "style_module.lua")
  -- 将加载的模块存入modules表中，以便后续使用
  modules.style_module = style_module
end

-- 初始化缓存功能（现在在utils_module中）
if utils_module and utils_module.init_cache then
  utils_module.init_cache({
    max_size = 2000,            -- 最大缓存条目数
    cleanup_interval = 60,      -- 清理间隔（秒）
    cache_lifetime = 3600,      -- 缓存生命周期（1小时）
    enable_auto_cleanup = true  -- 是否启用自动清理
  })
end

-- 添加错误检查，确保text_utils已正确加载
if not text_utils then
  -- 创建一个基本的替代模块，避免nil引用错误
  text_utils = {
    format_time = function(time) return "00:00.000" end,
    wrap_text = function(...) return 0 end,
    calculate_text_height = function() return 20 end,
    get_clipboard = function() return false, "", "剪贴板功能不可用" end,
    read_text_file = function() return false, "", "文件读取功能不可用" end,
    parse_sentences = function() return {} end,
    extract_cv_role_pairs = function() return {} end
  }
end

-- 字体设置（从style_module中获取）
local font_size = style_module.font_size  -- 界面字体大小
local content_font_size = style_module.content_font_size  -- 文本内容区域字体大小
local font_name = style_module.font_name -- 使用微软雅黑作为中文字体

-- 全局变量
local clipboard_text = ""
local sentences = {}
local cv_role_pairs = {}
local selected_cv = ""
local selected_role = ""
local error_note = ""
local correct_note = ""  -- 添加正确表达变量
-- 移除未使用的变量 current_time，在需要的地方重新定义
local debug_message = "" -- 添加调试信息变量

local sentence_scroll_pos = 0  -- 句子列表滚动位置
local cv_role_scroll_pos = 0   -- CV角色列表滚动位置
local chapter_scroll_pos = 0   -- 章节列表滚动位置
local selection_scroll_pos = 0 -- 选择区域滚动位置
local content_scroll_y = 0     -- 内容区域垂直滚动位置，确保在窗口大小变化时不会出现nil比较
local last_click_time = 0      -- 上次点击时间，使用精确时间
local last_wheel_time = 0      -- 上次滚轮时间
local click_cooldown = 0.01     -- 点击冷却时间(秒)，降低以提高连续点击响应
local wheel_cooldown = 0.01    -- 滚轮冷却时间(秒)，降低以提高响应速度
local is_dragging_sentence_scrollbar = false  -- 是否正在拖动句子滚动条



-- 辅助函数：获取最后一个区间的位置
function GetLastRegionPos()
  local num_markers, num_regions = r.CountProjectMarkers(0)
  local last_region_end = 0
  
  for i = 0, num_markers + num_regions - 1 do
    local retval, isrgn, pos, rgnend, name, markrgnindexnumber = r.EnumProjectMarkers(i)
    if retval and isrgn then  -- 如果是区间
      if rgnend > last_region_end then
        last_region_end = rgnend
      end
    end
  end
  
  return last_region_end
end

-- 辅助函数：根据选中的音频块添加项目标记
function AddProjectMarkerBySelectedMediaItems(is_marker)
  local num_selected = r.CountSelectedMediaItems(0)
  if num_selected == 0 then
    return false
  end
  
  local start_time = math.huge
  local end_time = 0
  
  -- 获取所有选中音频块的时间范围
  for i = 0, num_selected - 1 do
    local item = r.GetSelectedMediaItem(0, i)
    local item_start = r.GetMediaItemInfo_Value(item, "D_POSITION")
    local item_length = r.GetMediaItemInfo_Value(item, "D_LENGTH")
    local item_end = item_start + item_length
    
    if item_start < start_time then
      start_time = item_start
    end
    if item_end > end_time then
      end_time = item_end
    end
  end
  
  if is_marker then
    -- 添加标记
    r.AddProjectMarker2(0, false, start_time, 0, "", -1, 0)
  else
    -- 添加区间，在开始和结束时间各添加2秒缓冲
    local buffered_start = math.max(0, start_time - 2)  -- 确保不小于0
    local buffered_end = end_time + 2
    r.AddProjectMarker2(0, true, buffered_start, buffered_end, "", -1, 0)
  end
  
  return true
end

-- 辅助函数：获取配置值（简化版本）
function GetConst(key)
  -- 这里可以根据需要添加配置逻辑
  -- 暂时返回默认值
  if key == "createRegionAutoAndNext" then
    return 0  -- 默认不自动下集
  end
  return 0
end

-- 辅助函数：验证下一个（简化版本）
function ValidateNext()
  -- 这里可以添加验证逻辑
  return true
end

-- 辅助函数：移动光标到下一个位置（简化版本）
function MoveCursorToNext()
  -- 这里可以添加移动光标的逻辑
end

-- 主要功能函数：创建区间并自动命名
function CreateRegionForAuto(region_name)
  r.Undo_BeginBlock()
  r.PreventUIRefresh(1)
  r.SelectAllMediaItems(0, false)
  r.Main_OnCommand(1016, 0)  -- 选择光标到项目结束的所有音频块
  
  local timeStart = GetLastRegionPos()
  local timeEnd = r.GetCursorPosition()
  
  -- 只在结束时间添加2秒缓冲时间，开始时间使用上一个区间的结束位置
  timeEnd = timeEnd + 2
  
  r.GetSet_LoopTimeRange(true, true, timeStart, timeEnd, false)
  r.Main_OnCommand(40717, 0)  -- 选择循环时间范围内的音频块
  
  local countSelected = r.CountSelectedMediaItems(0)
  if countSelected == 0 then
    r.Main_OnCommand(40020, 0)  -- 移除时间选择
    r.PreventUIRefresh(-1)
    r.UpdateArrange()
    r.Undo_EndBlock("创建区间失败", -1)
    return
  end
  
  AddProjectMarkerBySelectedMediaItems(false)  -- 添加区间
  
  -- 如果提供了区间名称，设置区间名称
  if region_name and region_name ~= "" then
    local num_markers, num_regions = r.CountProjectMarkers(0)
    -- 找到最后添加的区间并设置名称
    for i = num_markers + num_regions - 1, 0, -1 do
      local retval, isrgn, pos, rgnend, name, markrgnindexnumber = r.EnumProjectMarkers(i)
      if retval and isrgn and (name == "" or name == nil) then
        r.SetProjectMarker(markrgnindexnumber, isrgn, pos, rgnend, region_name)
        break
      end
    end
  end
  
  local txt = "自动添加区间"
  if region_name and region_name ~= "" then
    txt = "自动添加区间: " .. region_name
  end
  
  if GetConst("createRegionAutoAndNext") == 1 then
    if ValidateNext() then
      MoveCursorToNext()
    end
    txt = txt .. "并下集"
  end
  
  -- 移除时间选区并将光标向后移动1分钟
  r.Main_OnCommand(40020, 0)  -- 移除时间选择
  local current_pos = r.GetCursorPosition()
  r.SetEditCurPos(current_pos + 60, true, true)  -- 向后移动60秒（1分钟）
  
  r.PreventUIRefresh(-1)
  r.UpdateArrange()
  r.Undo_EndBlock(txt, -1)
end
local is_dragging_cv_role_scrollbar = false   -- 是否正在拖动CV角色滚动条
local is_dragging_chapter_scrollbar = false   -- 是否正在拖动章节滚动条
local is_dragging_selection_scrollbar = false  -- 是否正在拖动选择区域滚动条
local hover_sentence_idx = -1  -- 鼠标悬停的句子索引，-1表示没有悬停
local hover_chapter_idx = -1   -- 鼠标悬停的章节索引，-1表示没有悬停
local is_chapter_list_visible = false  -- 章节列表是否可见，默认不可见
local is_track_align_enabled = false   -- 对轨功能是否启用，默认不启用
-- 添加CV角色列表悬停状态
local hover_cv_role = {cv = "", role = "", is_cv = false}  -- 悬停的CV或角色
local selected_text = ""      -- 存储单击选择的文本内容
-- 添加多选相关变量
local selected_texts = {}     -- 存储多个选中的文本
local selected_indices = {}   -- 存储多个选中的句子索引
local is_ctrl_down = false    -- 全局跟踪Ctrl键状态
local is_mouse_down = false    -- 添加鼠标按下状态跟踪
local episode_number = ""     -- 添加集数变量，默认为空
local process_suggestion = ""  -- 添加处理建议变量
local current_playrate = 1.0   -- 当前播放速率，默认为1.0

-- 长按速率按钮相关变量
local rate_button_hold_state = {
  minus_pressed = false,
  plus_pressed = false,
  last_adjust_time = 0,
  adjust_interval = 0.1  -- 每0.1秒调整一次速率
}
local is_playing = false       -- 当前播放状态
local chapters = {}           -- 存储识别到的章节列表

-- 添加性能优化相关变量
local cached_sentence_heights = {}  -- 缓存句子高度
local last_content_font_size = content_font_size  -- 记录上次使用的内容字体大小
local use_smooth_scroll = true  -- 启用平滑滚动
local cached_total_content_height = nil  -- 缓存内容总高度
local scroll_speed = 0.3  -- 降低滚动速度系数，使滚动更平滑
local wheel_scroll_amount = 2  -- 减小每次滚轮滚动的基础量
local cached_text_wrapping = {}  -- 缓存已计算的文本换行结果

-- 搜索功能相关变量
local search_text = ""           -- 搜索关键词
local search_results = {}        -- 搜索结果列表，存储匹配的句子索引
local current_search_index = 0   -- 当前搜索结果索引
local search_input_active = false -- 搜索输入是否激活
local search_highlight_color = {r = 1.0, g = 1.0, b = 0.0, a = 0.5}  -- 搜索高亮颜色（黄色）

-- 搜索功能实现
function perform_search(keyword)
  if not keyword or keyword == "" then
    search_results = {}
    current_search_index = 0
    return
  end
  
  search_results = {}
  local keyword_lower = keyword:lower()
  
  -- 在所有句子中搜索
  for i, sentence in ipairs(sentences) do
    if sentence and sentence ~= "__SKIP_THIS_SENTENCE__" then
      -- 清除标签后搜索
      local clean_sentence = clean_text_tags(sentence):lower()
      if clean_sentence:find(keyword_lower, 1, true) then
        table.insert(search_results, i)
      end
    end
  end
  
  -- 重置当前搜索索引
  current_search_index = #search_results > 0 and 1 or 0
end

-- 跳转到下一个搜索结果
function goto_next_search_result()
  if #search_results == 0 then return end
  
  current_search_index = current_search_index + 1
  if current_search_index > #search_results then
    current_search_index = 1
  end
  
  -- 滚动到当前结果
  local target_sentence = search_results[current_search_index]
  scroll_to_sentence(target_sentence)
end

-- 跳转到上一个搜索结果
function goto_prev_search_result()
  if #search_results == 0 then return end
  
  current_search_index = current_search_index - 1
  if current_search_index < 1 then
    current_search_index = #search_results
  end
  
  -- 滚动到当前结果
  local target_sentence = search_results[current_search_index]
  scroll_to_sentence(target_sentence)
end

-- 滚动到指定句子
function scroll_to_sentence(sentence_index)
  if not sentence_index or sentence_index < 1 or sentence_index > #sentences then
    return
  end
  
  -- 计算目标滚动位置，让目标句子显示在可见区域的中间
  local visible_items_count = math.floor((content_area.h - 10) / (content_font_size + 5))
  local target_scroll = math.max(0, sentence_index - math.floor(visible_items_count / 2))
  
  -- 确保不超过最大滚动范围
  local max_scroll = math.max(0, #sentences - visible_items_count)
  sentence_scroll_pos = math.min(target_scroll, max_scroll)
end

-- 清除搜索
function clear_search()
  search_text = ""
  search_results = {}
  current_search_index = 0
end

-- 绘制搜索UI
function draw_search_ui()
  -- 获取内容区域
  if not ui then return end
  local content_area = ui.content_area
  if not content_area then return end
  
  -- 搜索框位置（在内容区域右上角）
  local search_box = {
    x = content_area.x + content_area.w - 250,
    y = content_area.y - 35,
    w = 200,
    h = 25
  }
  
  -- 绘制搜索框背景（根据激活状态改变颜色）
  if search_input_active then
    gfx.set(1.0, 1.0, 0.9, 1)  -- 激活时浅黄色背景
  else
    gfx.set(0.9, 0.9, 0.9, 1)  -- 普通时浅灰色背景
  end
  gfx.rect(search_box.x, search_box.y, search_box.w, search_box.h)
  
  -- 绘制搜索框边框（根据激活状态改变颜色）
  if search_input_active then
    gfx.set(0.0, 0.5, 1.0, 1)  -- 激活时蓝色边框
  else
    gfx.set(0.5, 0.5, 0.5, 1)  -- 普通时深灰色边框
  end
  style_module.draw_black_outline(search_box.x, search_box.y, search_box.w, search_box.h)
  
  -- 绘制搜索文本
  gfx.set(0, 0, 0, 1)  -- 黑色文本
  gfx.x, gfx.y = search_box.x + 5, search_box.y + 5
  local display_text = search_text
  if display_text == "" then
    if search_input_active then
      display_text = "输入搜索关键词..."
      gfx.set(0.4, 0.4, 0.4, 1)  -- 激活时深灰色占位符
    else
      display_text = "搜索... (Ctrl+F)"
      gfx.set(0.6, 0.6, 0.6, 1)  -- 普通时浅灰色占位符
    end
  end
  gfx.drawstr(display_text)
  
  -- 如果搜索输入激活，绘制光标
  if search_input_active then
    local text_width = gfx.measurestr(display_text)
    gfx.set(0, 0, 0, 1)
    gfx.line(search_box.x + 5 + text_width, search_box.y + 3, search_box.x + 5 + text_width, search_box.y + search_box.h - 3)
  end
  
  -- 绘制搜索结果信息
  if #search_results > 0 then
    gfx.set(0, 0, 0, 1)  -- 黑色文本
    gfx.x, gfx.y = search_box.x + search_box.w + 10, search_box.y + 5
    local result_text = string.format("%d/%d", current_search_index, #search_results)
    gfx.drawstr(result_text)
    
    -- 绘制导航按钮
    local btn_size = 20
    local btn_y = search_box.y + 2
    
    -- 上一个按钮
    local prev_btn = {
      x = search_box.x + search_box.w + 50,
      y = btn_y,
      w = btn_size,
      h = btn_size
    }
    
    -- 下一个按钮
    local next_btn = {
      x = prev_btn.x + btn_size + 5,
      y = btn_y,
      w = btn_size,
      h = btn_size
    }
    
    -- 绘制按钮背景
    gfx.set(0.8, 0.8, 0.8, 1)
    gfx.rect(prev_btn.x, prev_btn.y, prev_btn.w, prev_btn.h)
    gfx.rect(next_btn.x, next_btn.y, next_btn.w, next_btn.h)
    
    -- 绘制按钮边框
    gfx.set(0.5, 0.5, 0.5, 1)
    style_module.draw_black_outline(prev_btn.x, prev_btn.y, prev_btn.w, prev_btn.h)
    style_module.draw_black_outline(next_btn.x, next_btn.y, next_btn.w, next_btn.h)
    
    -- 绘制按钮文本
    gfx.set(0, 0, 0, 1)
    gfx.x, gfx.y = prev_btn.x + 6, prev_btn.y + 4
    gfx.drawstr("↑")
    gfx.x, gfx.y = next_btn.x + 6, next_btn.y + 4
    gfx.drawstr("↓")
    
    -- 存储按钮信息供鼠标事件使用
    ui.search_prev_btn = prev_btn
    ui.search_next_btn = next_btn
  else
    ui.search_prev_btn = nil
    ui.search_next_btn = nil
  end
  
  -- 存储搜索框信息供鼠标事件使用
  ui.search_box = search_box
end

-- 添加字体缩放处理全局变量
local font_scaling_in_progress = false  -- 是否正在进行字体缩放处理
local font_scale_start_time = 0         -- 字体缩放开始时间
local font_scale_process_interval = 0.05 -- 处理间隔
local last_font_scale_process_time = 0   -- 上次处理时间
local font_scale_batch_size = 5          -- 每批处理的句子数量
local last_calculated_index = 0          -- 上次处理到的索引

-- 添加缓动函数
local function ease_out_quad(t)
  return 1 - (1 - t) * (1 - t)
end

local function ease_in_out_quad(t)
  return t < 0.5 and 2 * t * t or 1 - math.pow(-2 * t + 2, 2) / 2
end

-- 添加更流畅的缓动函数
local function cubic_bezier(t, p1x, p1y, p2x, p2y)
  -- 使用三次贝塞尔曲线计算，提供更自然的动画效果
  local cx = 3 * p1x
  local bx = 3 * (p2x - p1x) - cx
  local ax = 1 - cx - bx

  local cy = 3 * p1y
  local by = 3 * (p2y - p1y) - cy
  local ay = 1 - cy - by

  local function sampleCurveX(t)
    return ((ax * t + bx) * t + cx) * t
  end

  local function sampleCurveY(t)
    return ((ay * t + by) * t + cy) * t
  end

  local function sampleCurveDerivativeX(t)
    return (3 * ax * t + 2 * bx) * t + cx
  end

  -- 使用牛顿-拉弗森迭代法求解t
  local x = t
  for i = 1, 5 do -- 通常5次迭代就足够了
    local currentX = sampleCurveX(x) - t
    if math.abs(currentX) < 0.001 then
      break
    end
    local dx = sampleCurveDerivativeX(x)
    if math.abs(dx) < 0.00001 then
      break
    end
    x = x - currentX / dx
  end

  return sampleCurveY(x)
end

-- 定义常用的缓动函数
local ease = {
  out_quad = ease_out_quad,
  in_out_quad = ease_in_out_quad,
  -- 使用贝塞尔曲线定义更加平滑的动画曲线
  swift_out = function(t) return cubic_bezier(t, 0.25, 0.1, 0.25, 1.0) end,
  bounce_out = function(t)
    local n1 = 7.5625
    local d1 = 2.75
    if t < 1 / d1 then
      return n1 * t * t
    elseif t < 2 / d1 then
      t = t - 1.5 / d1
      return n1 * t * t + 0.75
    elseif t < 2.5 / d1 then
      t = t - 2.25 / d1
      return n1 * t * t + 0.9375
    else
      t = t - 2.625 / d1
      return n1 * t * t + 0.984375
    end
  end
}

-- 用于控制重绘性能的变量
force_redraw = true  -- 强制重绘标志，避免无必要的界面刷新（改为全局变量，使其在handle_cv_role_checkbox_click中可用）
local last_redraw_time = 0  -- 上次完整重绘时间
local redraw_cooldown = 0.05  -- 重绘冷却时间(秒)
local partial_render = false  -- 部分渲染模式标志
local full_redraw_counter = 0  -- 完整重绘计数器
local target_sentence_scroll_pos = 0  -- 目标句子滚动位置，用于平滑滚动
local target_cv_role_scroll_pos = 0  -- 目标CV角色滚动位置，用于平滑滚动
local target_selection_scroll_pos = 0  -- 目标选择区域滚动位置，用于平滑滚动
local last_mouse_x, last_mouse_y = 0, 0  -- 上次鼠标位置，用于检测鼠标移动
local initial_window_w, initial_window_h = 800, 700  -- 初始窗口大小，用于窗口大小变化检测，设置默认值防止nil比较
local last_window_w, last_window_h = 800, 700  -- 上次窗口大小，用于窗口大小变化检测，设置默认值防止nil比较
local last_selected_suggestion = ""  -- 上次选择的处理建议，用于记忆

-- 添加章节列表动画相关变量
local chapter_list_animation = {
  in_progress = false,       -- 是否正在进行动画
  start_time = 0,            -- 动画开始时间
  duration = 0.15,           -- 动画持续时间(秒)，减少为150毫秒使动画更快速流畅
  start_x = 0,               -- 动画开始位置
  target_x = 0,              -- 动画目标位置
  current_x = 0,             -- 当前位置
  default_x = 20,            -- 章节列表默认x坐标
  hide_position = -200,      -- 章节列表隐藏时的x坐标
  content_start_x = 0,       -- 内容区域开始X位置
  content_target_x = 0,      -- 内容区域目标X位置
  content_start_w = 0,       -- 内容区域开始宽度
  content_target_w = 0       -- 内容区域目标宽度
}

-- 增加额外的状态跟踪变量
local show_suggestion_dropdown = false  -- 是否显示处理建议下拉菜单
local hover_suggestion_idx = -1  -- 鼠标悬停的处理建议索引，-1表示没有悬停
local suggestion_options = {"返音", "补音", "后期处理"}  -- 处理建议选项
local marked_relative_time = ""  -- 记录标记时的内部相对时间
local is_cv_role_reversed = false  -- 是否反转角色和CV的位置（默认左边是角色，右边是CV）

-- 使用style_module初始化UI元素，避免在多个文件中重复定义
local ui = style_module.init_ui_elements()

-- 获取UI元素
local window_w, window_h = style_module.window_w, style_module.window_h
local chapter_list = ui.chapter_list
local content_area = ui.content_area
local cv_role_list = ui.cv_role_list
local font_size_label = ui.font_size_label
local font_decrease_button = ui.font_decrease_button
local font_increase_button = ui.font_increase_button
local font_size_display = ui.font_size_display
local error_input = ui.error_input
local correct_input = ui.correct_input
local episode_input = ui.episode_input
local suggestion_input = ui.suggestion_input
local play_button = ui.play_button
local rate_minus_button = ui.rate_minus_button
local rate_display_area = ui.rate_display_area
local rate_plus_button = ui.rate_plus_button
local rate_reset_button = ui.rate_reset_button
local block_mark_button = ui.block_mark_button
local region_mark_button = ui.region_mark_button
local document_button = ui.document_button
local clipboard_button = ui.clipboard_button
local excel_button = ui.excel_button
local selection_area = ui.selection_area
-- 添加新按钮的引用
local open_csv_button = ui.open_csv_button  -- "开"按钮 - 打开CSV文件
local au_button = ui.au_button  -- "AU"按钮 - 调用JHKAU.lua脚本
-- 使用style_module中定义的按钮
local track_align_button = ui.track_align_button  -- 对轨按钮
local chapter_button = ui.chapter_button  -- 章节按钮

-- 安全执行函数，捕获错误
function safe_call(func, ...)
  return utils_module.safe_call(func, ...)
end

-- 初始化gfx
function init()
  local status, err = pcall(function()
    -- 获取REAPER主窗口的位置，计算窗口居中位置
    local center_x, center_y = 0, 0
    local main_hwnd = r.GetMainHwnd()

    if main_hwnd and r.JS_Window_GetRect then
      local retval, left, top, right, bottom = r.JS_Window_GetRect(main_hwnd)
      if retval then
        -- 计算窗口宽度和高度
        local w = right - left
        local h = bottom - top

        -- 计算窗口中心位置
        center_x = left + math.floor((w - window_w) / 2)
        center_y = top + math.floor((h - window_h) / 2)
      end
    end

    -- 初始化窗口，并设置位置
    gfx.init("审听助手 v1.0 by 流浪物语", window_w, window_h, 0, center_x, center_y)
    -- 使用gfx.dock函数设置窗口停靠
    -- 使用值1026表示停靠在附加泊坞窗右侧（1024 + 2）
    -- 1024是基础值，表示窗口可以停靠
    -- +2表示停靠在附加泊坞窗右侧
    gfx.dock(1026)
    gfx.setfont(1, font_name, font_size)  -- 使用界面字体大小初始化
    gfx.clear = 3355443  -- 深灰背景色

    -- 初始化gfx_w和gfx_h全局变量，避免后续使用时出现nil值
    gfx_w, gfx_h = window_w, window_h

    -- 恢复上次选择的处理建议（如果有）
    if last_selected_suggestion ~= "" then
      process_suggestion = last_selected_suggestion
    end

    -- 保存初始窗口大小
    initial_window_w, initial_window_h = window_w, window_h
    last_window_w, last_window_h = window_w, window_h

    -- 初始化章节列表位置
    if is_chapter_list_visible then
      chapter_list.x = chapter_list_animation.default_x
    else
      chapter_list.x = chapter_list_animation.hide_position
    end
    chapter_list_animation.current_x = chapter_list.x

    -- 尝试加载保存的画本内容
    if modules.data_persist_module then
      -- 检查是否有保存的画本内容
      if modules.data_persist_module.has_saved_storyboard() then
        local success, loaded_data = modules.data_persist_module.load_storyboard()
        if success and loaded_data then
          -- 加载句子列表
          if loaded_data.sentences and #loaded_data.sentences > 0 then
            sentences = loaded_data.sentences
            -- 清除缓存
            cached_sentence_heights = {}
            cached_total_content_height = nil
          end

          -- 加载CV角色对
          if loaded_data.cv_role_pairs and #loaded_data.cv_role_pairs > 0 then
            cv_role_pairs = loaded_data.cv_role_pairs
          end

          -- 恢复滚动位置
          if loaded_data.scroll_positions then
            -- 恢复句子列表滚动位置
            if loaded_data.scroll_positions.sentence_scroll_pos then
              sentence_scroll_pos = loaded_data.scroll_positions.sentence_scroll_pos
              target_sentence_scroll_pos = sentence_scroll_pos
            end

            -- 恢复内容区域垂直滚动位置
            if loaded_data.scroll_positions.content_scroll_y then
              content_scroll_y = loaded_data.scroll_positions.content_scroll_y
            end

            -- 恢复章节滚动位置
            if loaded_data.scroll_positions.chapter_scroll_pos then
              chapter_scroll_pos = loaded_data.scroll_positions.chapter_scroll_pos
            end

            -- 恢复CV角色列表滚动位置
            if loaded_data.scroll_positions.cv_role_scroll_pos then
              cv_role_scroll_pos = loaded_data.scroll_positions.cv_role_scroll_pos
              target_cv_role_scroll_pos = cv_role_scroll_pos
            end
          end

          -- 识别章节
          chapters = chapter_module.extract_chapters(sentences)

          -- 默认选中第一章
          if #chapters > 0 then
            -- 默认选中第一章
            current_chapter_idx = 1
            -- 更新当前章节
            update_current_chapter()

            -- 确保章节可见
            ensure_chapter_visible(current_chapter_idx)
          end

          force_redraw = true
        end
      end
    end

    -- debug_message = "初始化完成"
  end)

  if not status then
    -- debug_message = "初始化错误: " .. tostring(err)
    return false
  end

  -- 检查并确保启用"改变播放速率时保持音频对象的音高"选项
  if utils_module and utils_module.ensure_preserve_pitch then
    local changed = utils_module.ensure_preserve_pitch()
    if changed then
      -- 可以在这里添加提示信息，表示已自动启用音高保持
      -- debug_message = "已自动启用改变播放速率时保持音频对象的音高选项"
    end
  end

  return true
end

-- 获取剪贴板内容
function get_clipboard()
  local success, clipboard, message = text_utils.get_clipboard()
  if success then
    clipboard_text = clipboard
  else
    -- debug_message = message
  end
end

-- 读取文本文件内容
function read_text_file()
  local success, content, message = text_utils.read_text_file()
  if success then
    clipboard_text = content
    -- debug_message = message
  else
    -- debug_message = message
  end
end

-- 解析文本为句子
function parse_sentences()
  sentences = text_utils.parse_sentences(clipboard_text)
  -- 清除缓存
  cached_sentence_heights = {}
  cached_total_content_height = nil

  -- 识别章节
  chapters = chapter_module.extract_chapters(sentences)
  chapter_scroll_pos = 0  -- 重置章节滚动位置
end

-- 提取角色名和CV名
function extract_cv_role_pairs()
  -- 直接调用text_utils的函数，简化代码
  cv_role_pairs = text_utils.extract_cv_role_pairs(sentences, is_cv_role_reversed)

  -- 不再需要手动添加旁白角色，因为text_utils已经处理了这部分逻辑
end

-- 修改全局变量部分，添加性能控制参数
local frame_delay = 1/15  -- 降低帧率至15fps，大幅减少CPU使用
local skip_render_counter = 0  -- 跳过渲染计数器
local MAX_SKIP_FRAMES = 2  -- 最多跳过的帧数

-- 添加渲染控制变量
local force_redraw = true  -- 是否强制重绘界面
local last_redraw_time = 0  -- 上次完整重绘时间
local redraw_cooldown = 0.1  -- 完整重绘冷却时间
local full_redraw_counter = 0  -- 完整重绘计数器
local partial_render = false  -- 是否进行部分渲染

-- 添加智能缓存控制
local cache_manager = nil  -- 移除cache_manager定义

-- 优化渲染函数
function render()
    local current_time = os.clock()

    -- 检查是否需要完整重绘
    if force_redraw or (current_time - last_redraw_time) > redraw_cooldown then
        full_redraw_counter = full_redraw_counter + 1
        last_redraw_time = current_time
        force_redraw = false
        partial_render = false
    end

    -- 使用智能缓存进行渲染
    if not partial_render then
        -- 清除背景
        gfx.set(0.2, 0.2, 0.2, 1)
        gfx.rect(0, 0, (gfx_w or window_w), (gfx_h or window_h), 1)

        -- 记录之前的内容区域宽度，用于检测变化
        local prev_content_width = content_area.w

        -- 根据章节列表是否可见调整内容区域位置和宽度
        if is_chapter_list_visible then
          -- 章节列表可见时，内容区域靠右
          content_area.x = chapter_list.x + chapter_list.w + 10
          content_area.w = window_w - 14 - chapter_list.w - 10  -- 将右侧边距从20减少到14，增加显示宽度
        else
          -- 章节列表不可见时，内容区域占据更多空间
          content_area.x = 20  -- 左侧保留20像素边距
          content_area.w = window_w - 24 -- 右侧边距从30减少到24，进一步增加显示宽度
        end

        -- 如果内容区域宽度发生变化，清除句子高度缓存和总高度缓存
        if prev_content_width ~= content_area.w then
          cached_sentence_heights = {}
          cached_total_content_height = nil
        end

        -- 渲染对轨按钮 - 始终可见
        button_module.draw_track_align_button(track_align_button, is_track_align_enabled)
        
        -- 渲染章节按钮 - 始终可见
        button_module.draw_chapter_button(chapter_button, is_chapter_list_visible)

        -- 渲染章节列表 - 只在可见时渲染
        if is_chapter_list_visible then
          draw_chapter_list()
        end

        -- 渲染内容区域
        gfx.set(0.15, 0.15, 0.15, 1)  -- 深灰色背景
        gfx.rect(content_area.x, content_area.y, content_area.w, content_area.h)
        style_module.draw_black_outline(content_area.x, content_area.y, content_area.w, content_area.h)
        draw_sentences_list()
        
        -- 绘制内容区域拖拽调整大小区域
        if ui.content_resize_handle then
          local mouse_x, mouse_y = gfx.mouse_x, gfx.mouse_y
          local is_hovering = style_module.is_mouse_in_resize_handle(mouse_x, mouse_y, ui.content_resize_handle)
          style_module.draw_resize_handle(ui.content_resize_handle, is_hovering)
        end

        -- 渲染CV角色列表
        gfx.set(0.15, 0.15, 0.15, 1)  -- 深灰色背景
        gfx.rect(cv_role_list.x, cv_role_list.y, cv_role_list.w, cv_role_list.h)
        style_module.draw_black_outline(cv_role_list.x, cv_role_list.y, cv_role_list.w, cv_role_list.h)
        style_module.draw_checkbox(cv_role_reverse_checkbox, "交换位置", is_cv_role_reversed)

        -- 使用辅助函数获取CV角色分类数据，确保无论是否交换位置都保持一致的显示
        local cv_categories, cv_order = get_cv_role_categories(cv_role_pairs, is_cv_role_reversed)

        -- 计算所有分类项的总数（包括CV分类标题）
        local total_items = 0
        for _, roles in pairs(cv_categories) do
          -- 每个CV类别标题占一行，加上所有角色
          total_items = total_items + 1 + #roles
        end

        -- 使用基于字体大小的动态行高
        local cv_role_line_height = math.max(20, font_size + 2)
        local visible_items = math.floor((cv_role_list.h - 10) / cv_role_line_height)
        local max_scroll = math.max(0, total_items - visible_items)
        cv_role_scroll_pos = math.min(cv_role_scroll_pos, max_scroll)  -- 确保滚动位置不超过最大值

        -- 绘制滚动条
        if total_items > visible_items then
          -- 创建滚动条对象
          local scrollbar = {
            x = cv_role_list.x + cv_role_list.w - 10,
            y = cv_role_list.y,
            w = 10,
            h = cv_role_list.h
          }

          -- 调用style_module的draw_scrollbar函数
          style_module.draw_scrollbar(scrollbar, cv_role_scroll_pos, max_scroll, visible_items, total_items)
        end

        -- 计算开始绘制的位置（考虑滚动位置）
        local visible_start = cv_role_scroll_pos + 1
        local current_item = 1
        local y_offset = 5

        -- 按CV分类绘制角色列表
        for _, cv_name in ipairs(cv_order) do
          local roles = cv_categories[cv_name]

          -- 绘制CV分类标题（只有在可见范围内才绘制）
          if current_item >= visible_start and (current_item - visible_start) < visible_items then
            -- 检查是否是鼠标悬停的CV分类
            if hover_cv_role.cv == cv_name and hover_cv_role.is_cv then
              -- 绘制金属效果的悬停高亮效果（蓝色系）
              local rect_h = cv_role_line_height
              local rect_y = cv_role_list.y + y_offset - 2
              local rect_x = cv_role_list.x
              local rect_w = cv_role_list.w - 10

              -- 使用style_module的金属高亮函数
              style_module.draw_metallic_highlight(rect_x, rect_y, rect_w, rect_h, style_module.highlight_colors.blue.cv, true)
            else
              -- 正常绘制CV分类标题背景
              -- 使用style_module的金属高亮函数
              style_module.draw_metallic_highlight(cv_role_list.x, cv_role_list.y + y_offset - 2, cv_role_list.w - 10, cv_role_line_height, style_module.highlight_colors.blue.cv, false)
            end

            -- 绘制CV分类标题文字
            gfx.set(1, 1, 1, 1)  -- 白色文字
            gfx.x, gfx.y = cv_role_list.x + 5, cv_role_list.y + y_offset
            gfx.drawstr("CV: " .. cv_name)

            -- 更新Y偏移
            y_offset = y_offset + cv_role_line_height
          end
          current_item = current_item + 1

          -- 绘制该CV下的所有角色
          for _, role_info in ipairs(roles) do
            if current_item >= visible_start and (current_item - visible_start) < visible_items then
              -- 检查是否是当前选中的CV和角色或鼠标悬停的角色
              -- 需要考虑是否交换位置
              local display_selected_cv, display_selected_role

              if is_cv_role_reversed then
                -- 如果已交换，则需要反向处理以保持显示一致
                display_selected_cv = selected_role  -- 实际上是CV
                display_selected_role = selected_cv  -- 实际上是角色
              else
                -- 未交换时正常处理
                display_selected_cv = selected_cv
                display_selected_role = selected_role
              end

              if cv_name == display_selected_cv and role_info.role == display_selected_role then
                -- 绘制金属效果的选中高亮效果（蓝色系）
                local rect_h = cv_role_line_height
                local rect_y = cv_role_list.y + y_offset - 2
                local rect_x = cv_role_list.x
                local rect_w = cv_role_list.w - 10

                -- 使用style_module的金属高亮函数
                style_module.draw_metallic_highlight(rect_x, rect_y, rect_w, rect_h, style_module.highlight_colors.blue.selected, true)
              elseif hover_cv_role.cv == cv_name and hover_cv_role.role == role_info.role and not hover_cv_role.is_cv then
                -- 绘制金属效果的鼠标悬停高亮效果（绿色系）
                local rect_h = cv_role_line_height
                local rect_y = cv_role_list.y + y_offset - 2
                local rect_x = cv_role_list.x
                local rect_w = cv_role_list.w - 10

                -- 使用style_module的金属高亮函数
                style_module.draw_metallic_highlight(rect_x, rect_y, rect_w, rect_h, style_module.highlight_colors.green.hover, false)

                -- 恢复文本颜色
                gfx.set(1, 1, 1, 1)
              end

              -- 绘制角色名称
              gfx.x, gfx.y = cv_role_list.x + 15, cv_role_list.y + y_offset  -- 稍微缩进
              gfx.drawstr(role_info.role)

              -- 更新Y偏移
              y_offset = y_offset + cv_role_line_height
            end
            current_item = current_item + 1
          end
        end

        -- 渲染输入区域
        gfx.set(1, 1, 1, 1)  -- 设置白色文本颜色，确保标签颜色统一
        gfx.x, gfx.y = error_input.x, error_input.y - 20
        gfx.drawstr("错误描述:")

        -- 使用模块化的方式绘制输入区域，避免重复绘制
        button_module.draw_input_area(error_input, error_note, "", "错误描述:")
        button_module.draw_input_area(correct_input, correct_note, "", "正确表达:")
        button_module.draw_input_area(episode_input, episode_number, "", "集数:")
        button_module.draw_suggestion_input(suggestion_input, process_suggestion, "", "处理建议:")

        -- 绘制当前选择的CV和角色以及错误描述
        gfx.set(0.15, 0.15, 0.15, 1)  -- 深灰色背景
        gfx.rect(selection_area.x, selection_area.y, selection_area.w, selection_area.h)
        style_module.draw_black_outline(selection_area.x, selection_area.y, selection_area.w, selection_area.h)

        -- 绘制选择内容
        draw_selection_content()

        -- 渲染控制按钮
        -- 获取当前选中的音频块
        local has_selected_item = r.GetSelectedMediaItem(0, 0) ~= nil
        local role_cv_selected = selected_cv ~= "" and selected_role ~= ""
        local has_process_suggestion = process_suggestion ~= ""

        -- 块标按钮只在同时满足三个条件时才启用：1)选中了音频块；2)选择了角色和CV；3)填写了处理建议
        local block_mark_button_enabled = has_selected_item and role_cv_selected and has_process_suggestion
        -- 区标按钮只需要满足两个条件：1)选择了角色和CV；2)填写了处理建议
        local region_mark_button_enabled = role_cv_selected and has_process_suggestion
        button_module.draw_block_mark_button(block_mark_button, block_mark_button_enabled)
        button_module.draw_region_mark_button(region_mark_button, region_mark_button_enabled)

        -- 绘制写入报告按钮
        local has_selected_text = selected_text ~= ""
        button_module.draw_excel_button(excel_button, has_selected_text)

        -- 绘制开按钮、AU按钮、区名按钮、文名按钮和轨色按钮
        button_module.draw_open_au_buttons(open_csv_button, au_button, ui.region_name_button, ui.file_name_button, ui.track_color_button, ui.track_split_button)

        -- 绘制播放/暂停按钮
        button_module.draw_play_button(play_button, is_playing)

        -- 绘制速率相关按钮
        button_module.draw_rate_buttons(rate_minus_button, rate_display_area, rate_plus_button, rate_reset_button, current_playrate)

        -- 绘制字体大小按钮
        button_module.draw_font_buttons(font_size_label, font_decrease_button, font_increase_button, font_size_display, content_font_size)

        -- 最后绘制下拉菜单，确保它显示在最上层
        if show_suggestion_dropdown then
          button_module.draw_suggestion_dropdown(suggestion_input, suggestion_options, hover_suggestion_idx)
        end
    end

    -- 更新界面
    gfx.update()
end

-- 章节按钮渲染已合并到render函数中

-- 优化句子高度计算
function calculate_sentence_height(sentence)
  if not sentence then return content_font_size + 2 end

  -- 调用text_utils中的函数
  return text_utils.calculate_sentence_height(sentence, content_font_size, font_name, content_area.w, cache_module, gfx)
end

-- 添加性能优化相关函数

-- 优化句子高度计算，避免频繁重新计算
function ensure_sentence_heights_cached()
  -- 如果字体大小改变，清除相关缓存
  if last_content_font_size ~= content_font_size then
    -- 记录字体大小变化
    last_content_font_size = content_font_size
    cached_total_content_height = nil

    -- 标记需要进行异步处理
    font_scaling_in_progress = true
    font_scale_start_time = r.time_precise()
    last_font_scale_process_time = font_scale_start_time

    -- 使用动态批处理大小
    font_scale_batch_size = 10

    -- 立即计算可见区域内的前几个句子
    local visible_start = math.max(1, sentence_scroll_pos)
    local urgent_count = math.min(5, #sentences - visible_start + 1)

    for i = visible_start, visible_start + urgent_count - 1 do
      if i <= #sentences then
        local sentence = sentences[i]
        calculate_sentence_height(sentence)
      end
    end

    return
  end

  -- 如果总高度已经计算过，无需重新计算
  if cached_total_content_height then
    return cached_total_content_height
  end

  -- 计算总高度
  local total_height = 0
  for i = 1, #sentences do
    total_height = total_height + calculate_sentence_height(sentences[i])
  end

  -- 缓存总高度
  cached_total_content_height = total_height
  return total_height
end

-- 添加单独的句子绘制函数，简化逻辑
function draw_sentence(sentence_idx, is_hover, is_selected, y_offset, max_width, safety_margin)
  -- 调用text_utils中的函数，确保参数顺序与text_utils.lua中的定义匹配
  return text_utils.draw_sentence(sentence_idx, sentences, content_area, y_offset, is_selected, is_hover, content_font_size, font_name, style_module, gfx)
end

-- 绘制句子列表函数
function draw_sentences_list()
  if #sentences == 0 then return end

  -- 添加一个ui.sentence_scrollbar定义以便后续使用
  ui.sentence_scrollbar = {
    x = content_area.x + content_area.w - 10,
    y = content_area.y,
    w = 10,
    h = content_area.h
  }

  -- 直接实现章节更新逻辑（只有在非章节点击跳转的情况下更新）
  if chapter_module and chapter_module.get_chapter_by_sentence_idx and not chapter_click_jumping then
    -- 获取当前可见区域的句子范围
    local visible_items_count = math.floor((content_area.h - 10) / (content_font_size + 5))
    local first_visible_sentence = sentence_scroll_pos + 1
    local last_visible_sentence = math.min(#sentences, sentence_scroll_pos + visible_items_count)

    -- 获取可见区域内最后一个句子所属的章节
    local chapter, chapter_idx = chapter_module.get_chapter_by_sentence_idx(last_visible_sentence)

    -- 如果找不到章节，尝试使用第一个可见句子
    if not chapter_idx then
      chapter, chapter_idx = chapter_module.get_chapter_by_sentence_idx(first_visible_sentence)
    end

    -- 更新当前章节
    if chapter_idx and chapter_idx ~= current_chapter_idx then
      current_chapter_idx = chapter_idx

      -- 确保当前章节在章节列表中可见
      local line_height = font_size + 5
      local visible_items = math.floor((chapter_list.h - 10) / line_height)

      if chapter_idx <= chapter_scroll_pos then
        chapter_scroll_pos = math.max(0, chapter_idx - 1)
      elseif chapter_idx > chapter_scroll_pos + visible_items then
        chapter_scroll_pos = math.min(#chapters - visible_items, chapter_idx - visible_items)
      end
    end
  end

  -- 调用text_utils中的函数绘制句子列表
  text_utils.draw_sentences_list(sentences, content_area, sentence_scroll_pos, hover_sentence_idx, selected_indices, content_font_size, font_name, cached_sentence_heights, cached_total_content_height, style_module, gfx, search_text, search_highlight_color)

  -- 不再显示句子总数
end

-- 全局变量
current_chapter_idx = -1  -- 当前章节索引

-- 确保章节在可视区域内
function ensure_chapter_visible(chapter_idx)
  if not chapter_idx then return end

  local line_height = font_size + 5
  local visible_items = math.floor((chapter_list.h - 10) / line_height)

  -- 如果当前章节不在可视区域内，调整滚动位置
  if chapter_idx <= chapter_scroll_pos then
    chapter_scroll_pos = math.max(0, chapter_idx - 1)
  elseif chapter_idx > chapter_scroll_pos + visible_items then
    chapter_scroll_pos = math.min(#chapters - visible_items, chapter_idx - visible_items)
  end
end

-- 更新当前章节函数
function update_current_chapter()
  if not chapter_module or not chapter_module.get_chapter_by_sentence_idx then
    return  -- 如果章节模块或函数不存在，直接返回
  end

  -- 获取当前可见区域的句子范围
  local visible_items_count = math.floor((content_area.h - 10) / (content_font_size + 5))
  local first_visible_sentence = sentence_scroll_pos + 1
  local last_visible_sentence = math.min(#sentences, sentence_scroll_pos + visible_items_count)

  -- 获取可见区域内最后一个句子所属的章节
  local chapter, chapter_idx = chapter_module.get_chapter_by_sentence_idx(last_visible_sentence)

  -- 如果找不到章节，尝试使用第一个可见句子
  if not chapter_idx then
    chapter, chapter_idx = chapter_module.get_chapter_by_sentence_idx(first_visible_sentence)
  end

  -- 更新当前章节
  if chapter_idx and chapter_idx ~= current_chapter_idx then
    current_chapter_idx = chapter_idx
    -- 确保当前章节可见
    ensure_chapter_visible(chapter_idx)
  end
end

-- 绘制章节列表函数
function draw_chapter_list()
  -- 检查章节列表是否应该显示
  if not is_chapter_list_visible then
    return  -- 如果章节列表不可见，直接返回不绘制
  end

  -- 确保使用正确的字体大小
  gfx.setfont(1, font_name, font_size)

  -- 绘制章节列表标题
  gfx.set(1, 1, 1, 1)  -- 白色
  gfx.x, gfx.y = chapter_list.x, chapter_list.y - 20
  gfx.drawstr("章节列表:")

  -- 绘制章节列表背景
  gfx.set(0.15, 0.15, 0.15, 1)  -- 深灰色背景
  gfx.rect(chapter_list.x, chapter_list.y, chapter_list.w, chapter_list.h)

  -- 绘制章节列表黑色边框
  style_module.draw_black_outline(chapter_list.x, chapter_list.y, chapter_list.w, chapter_list.h)

  -- 只有在有章节数据时才绘制章节内容和滚动条
  if #chapters > 0 then
    -- 计算可见区域
    local line_height = font_size + 5  -- 章节标题行高
    local visible_area_height = chapter_list.h - 10
    local visible_items = math.floor(visible_area_height / line_height)

    -- 计算最大滚动位置
    local max_scroll = math.max(0, #chapters - visible_items)
    chapter_scroll_pos = math.min(chapter_scroll_pos, max_scroll)

    -- 绘制滚动条
    if #chapters > visible_items then
      -- 创建滚动条对象
      local scrollbar = {
        x = chapter_list.x + chapter_list.w - 10,
        y = chapter_list.y,
        w = 10,
        h = chapter_list.h
      }

      -- 调用style_module的draw_scrollbar函数
      style_module.draw_scrollbar(scrollbar, chapter_scroll_pos, max_scroll, visible_items, #chapters)
    end

    -- 显示章节
    local y_offset = 5
    local max_visible_idx = math.min(#chapters, chapter_scroll_pos + visible_items)

    for i = chapter_scroll_pos + 1, max_visible_idx do
      if chapters[i] then
        -- 检查是否是当前章节或鼠标悬停在此章节上
        local is_current = (i == current_chapter_idx)
        local is_hover = (i == hover_chapter_idx)

        -- 绘制高亮背景
        if is_current then
          -- 绘制金属效果的当前章节高亮（绿色系）
          local rect_h = line_height
          local rect_y = chapter_list.y + y_offset - 2
          local rect_x = chapter_list.x
          local rect_w = chapter_list.w - 10

          -- 使用style_module的金属高亮函数
          style_module.draw_metallic_highlight(rect_x, rect_y, rect_w, rect_h, style_module.highlight_colors.green.selected, true)
        elseif is_hover then
          -- 绘制金属效果的悬停高亮（浅绿色系）
          local rect_h = line_height
          local rect_y = chapter_list.y + y_offset - 2
          local rect_x = chapter_list.x
          local rect_w = chapter_list.w - 10

          -- 使用style_module的金属高亮函数
          style_module.draw_metallic_highlight(rect_x, rect_y, rect_w, rect_h, style_module.highlight_colors.green.hover, false)
        end

        -- 绘制章节标题
        if is_current then
          gfx.set(1, 1, 1, 1)  -- 当前章节使用白色
        else
          gfx.set(0.9, 0.9, 0.9, 1)  -- 其他章节使用浅灰色
        end
        gfx.x, gfx.y = chapter_list.x + 5, chapter_list.y + y_offset

        -- 截断过长的标题并添加省略号
        local title = chapters[i].title
        local max_title_width = chapter_list.w - 20
        local title_width = gfx.measurestr(title)

        if title_width > max_title_width then
          -- 截断标题，确保在UTF-8字符边界处截断
          local short_title = ""
          local char_count = 0
          local byte_pos = 1

          -- 逐字符截取，确保不会在UTF-8字符中间截断
          while byte_pos <= #title and char_count < 10 do
            local char_len = 1
            local b = title:byte(byte_pos)

            -- 检测UTF-8字符的字节长度
            if b >= 240 then -- 4字节字符 (11110xxx)
              char_len = 4
            elseif b >= 224 then -- 3字节字符 (1110xxxx)
              char_len = 3
            elseif b >= 192 then -- 2字节字符 (110xxxxx)
              char_len = 2
            end

            -- 添加当前字符到短标题
            short_title = short_title .. title:sub(byte_pos, byte_pos + char_len - 1)
            byte_pos = byte_pos + char_len
            char_count = char_count + 1
          end

          short_title = short_title .. "..."
          gfx.drawstr(short_title)
        else
          gfx.drawstr(title)
        end

        -- 更新下一个章节的起始位置
        y_offset = y_offset + line_height
      end
    end
  else
    -- 当没有章节数据时显示提示信息
    gfx.set(0.7, 0.7, 0.7, 1)  -- 亮灰色
    gfx.x = chapter_list.x + 5
    gfx.y = chapter_list.y + (chapter_list.h - gfx.texth) / 2  -- 垂直居中
    gfx.drawstr("无章节数据")
  end
end

-- 在内容滚动时更新当前章节
local function on_content_scroll()
  update_current_chapter()
end

-- 绘制界面优化
function draw_ui()
  local status, err = pcall(function()
    -- 当前时间
    local current_time = r.time_precise()

    -- 鼠标位置
    local mouse_x, mouse_y = gfx.mouse_x, gfx.mouse_y

    -- 检查是否需要完整重绘
    local need_full_redraw = force_redraw or
                             (current_time - last_redraw_time > redraw_cooldown) or
                             (full_redraw_counter % 3 == 0) or  -- 每3帧强制完整重绘一次
                             chapter_list_animation.in_progress  -- 动画过程中强制重绘

    if not need_full_redraw and
       last_mouse_x == mouse_x and
       last_mouse_y == mouse_y then
      -- 鼠标未移动，且不需要完整重绘，跳过绘制
      partial_render = true
      return
    end

    -- 更新鼠标位置记录
    last_mouse_x, last_mouse_y = mouse_x, mouse_y

    -- 如果进行完整重绘，更新时间戳
    if need_full_redraw then
      last_redraw_time = current_time
      force_redraw = false
    end

    -- 增加计数器
    full_redraw_counter = full_redraw_counter + 1

    -- 清屏
    gfx.clear = 3355443

    -- 定义统一的高亮颜色
    local highlight_color = {r = 0.2, g = 0.7, b = 0.2, a = 0.5}

    -- 设置界面字体（只设置一次）
    gfx.setfont(1, font_name, font_size)  -- 使用界面字体大小

    -- 检查是否需要清除缓存
    if last_content_font_size ~= content_font_size then
      cached_sentence_heights = {}  -- 字体大小变更时清除缓存
      cached_total_content_height = nil
      last_content_font_size = content_font_size
    end

    -- 处理章节列表动画
    if chapter_list_animation.in_progress then
      local elapsed = current_time - chapter_list_animation.start_time
      local progress = math.min(1, elapsed / chapter_list_animation.duration)

      -- 使用更流畅的贝塞尔曲线缓动函数
      local eased_progress = ease.swift_out(progress)

      -- 计算章节列表当前位置
      chapter_list_animation.current_x = chapter_list_animation.start_x +
        (chapter_list_animation.target_x - chapter_list_animation.start_x) * eased_progress

      -- 更新章节列表位置
      chapter_list.x = math.floor(chapter_list_animation.current_x)

      -- 同时动画更新内容区域位置和宽度，实现协调过渡
      content_area.x = math.floor(chapter_list_animation.content_start_x +
        (chapter_list_animation.content_target_x - chapter_list_animation.content_start_x) * eased_progress)

      content_area.w = math.floor(chapter_list_animation.content_start_w +
        (chapter_list_animation.content_target_w - chapter_list_animation.content_start_w) * eased_progress)

      -- 检查动画是否完成
      if progress >= 1 then
        chapter_list_animation.in_progress = false
        chapter_list.x = chapter_list_animation.target_x
        content_area.x = chapter_list_animation.content_target_x
        content_area.w = chapter_list_animation.content_target_w

        -- 如果内容区域宽度发生了变化，清除高度缓存以便重新计算
        cached_sentence_heights = {}
        cached_total_content_height = nil
      end
    else
      -- 非动画状态下根据章节列表是否可见调整内容区域位置和宽度
      if is_chapter_list_visible then
        -- 章节列表可见时，内容区域靠右
        content_area.x = chapter_list.x + chapter_list.w + 10
        content_area.w = window_w - 14 - chapter_list.w - 10  -- 将右侧边距从20减少到14，增加显示宽度
      else
        -- 章节列表不可见时，内容区域占据更多空间
        content_area.x = 20  -- 左侧保留20像素边距
        content_area.w = window_w - 24 -- 右侧边距从30减少到24，进一步增加显示宽度
      end
    end

    -- 标题已移除

    -- 内容区域标题
    gfx.set(1, 1, 1, 1)  -- 设置白色文本颜色，确保标签颜色统一
    gfx.x, gfx.y = content_area.x, content_area.y - 20
    gfx.drawstr("画本内容:")

    -- 绘制字体大小标签和按钮
    button_module.draw_font_buttons(font_size_label, font_decrease_button, font_increase_button, font_size_display, content_font_size)

    -- 绘制文档和剪贴板按钮
    button_module.draw_document_buttons(document_button, clipboard_button)

    -- 绘制对轨按钮，无论对轨功能是否启用都需要绘制
    button_module.draw_track_align_button(track_align_button, is_track_align_enabled)
    
    -- 绘制章节按钮，无论章节列表是否可见都需要绘制
    button_module.draw_chapter_button(chapter_button, is_chapter_list_visible)

    -- 只有当章节列表在屏幕内时才绘制章节列表
    if chapter_list.x > chapter_list_animation.hide_position then
      draw_chapter_list()
    end

    -- 绘制内容区域背景
    style_module.set_color(style_module.colors.content_bg)  -- 羊皮纸黄色背景
    gfx.rect(content_area.x, content_area.y, content_area.w, content_area.h)

    -- 绘制内容区域黑色边框
    style_module.draw_black_outline(content_area.x, content_area.y, content_area.w, content_area.h)

    -- 绘制内容区域拖拽调整大小区域
    if ui.content_resize_handle then
      local mouse_x, mouse_y = gfx.mouse_x, gfx.mouse_y
      local is_hovering = style_module.is_mouse_in_resize_handle(mouse_x, mouse_y, ui.content_resize_handle)
      style_module.draw_resize_handle(ui.content_resize_handle, is_hovering)
    end

    -- 使用优化的句子列表绘制函数
    draw_sentences_list()

    -- 绘制CV和角色列表标题
    gfx.setfont(1, font_name, font_size)  -- 切换回界面字体，确保CV角色列表使用固定的界面字体大小
    gfx.set(1, 1, 1, 1)  -- 设置白色文本颜色，确保标签颜色统一
    gfx.x, gfx.y = cv_role_list.x, cv_role_list.y - 20
    gfx.drawstr(style_module.cv_role_label_text)

    -- 绘制角色和CV位置切换复选框（使用style_module中定义的位置）
    local cv_role_reverse_checkbox = ui.cv_role_reverse_checkbox
    local cv_role_reverse_label = ui.cv_role_reverse_label
    style_module.draw_checkbox(cv_role_reverse_checkbox, "交换位置", is_cv_role_reversed)

    -- 绘制CV和角色列表背景
    gfx.set(0.15, 0.15, 0.15, 1)  -- 深灰色背景
    gfx.rect(cv_role_list.x, cv_role_list.y, cv_role_list.w, cv_role_list.h)

    -- 绘制CV角色列表黑色边框
    style_module.draw_black_outline(cv_role_list.x, cv_role_list.y, cv_role_list.w, cv_role_list.h)

    -- 绘制CV和角色列表（按CV分类）
    gfx.set(1, 1, 1, 1)  -- 白色
    y_offset = 5

    -- 使用基于字体大小的动态行高而不是固定值
    local cv_role_line_height = math.max(20, font_size + 2)

    -- 按CV分类整理角色列表
    -- 使用辅助函数获取CV角色分类数据，确保无论是否交换位置都保持一致的显示
    local cv_categories, cv_order = get_cv_role_categories(cv_role_pairs, is_cv_role_reversed)

    -- 计算所有分类项的总数（包括CV分类标题）
    local total_items = 0
    for _, roles in pairs(cv_categories) do
        -- 每个CV类别标题占一行，加上所有角色
        total_items = total_items + 1 + #roles
    end

    -- 计算可见项目数和最大滚动位置
    local visible_items = math.floor((cv_role_list.h - 10) / cv_role_line_height)
    local max_scroll = math.max(0, total_items - visible_items)
    cv_role_scroll_pos = math.min(cv_role_scroll_pos, max_scroll)  -- 确保滚动位置不超过最大值

    -- 绘制滚动条
    if total_items > visible_items then
      -- 创建滚动条对象
      local scrollbar = {
        x = cv_role_list.x + cv_role_list.w - 10,
        y = cv_role_list.y,
        w = 10,
        h = cv_role_list.h
      }

      -- 调用style_module的draw_scrollbar函数
      style_module.draw_scrollbar(scrollbar, cv_role_scroll_pos, max_scroll, visible_items, total_items)
    end

    -- 计算开始绘制的位置（考虑滚动位置）
    local visible_start = cv_role_scroll_pos + 1
    local current_item = 1

    -- 按CV分类绘制角色列表
    for _, cv_name in ipairs(cv_order) do
        local roles = cv_categories[cv_name]

        -- 绘制CV分类标题（只有在可见范围内才绘制）
        if current_item >= visible_start and (current_item - visible_start) < visible_items then
            -- 检查是否是鼠标悬停的CV分类
            if hover_cv_role.cv == cv_name and hover_cv_role.is_cv then
                -- 绘制金属效果的悬停高亮效果（蓝色系）
                local rect_h = cv_role_line_height
                local rect_y = cv_role_list.y + y_offset - 2
                local rect_x = cv_role_list.x
                local rect_w = cv_role_list.w - 10

                -- 使用style_module的金属高亮函数
                style_module.draw_metallic_highlight(rect_x, rect_y, rect_w, rect_h, style_module.highlight_colors.blue.cv, true)
            else
                -- 正常绘制CV分类标题背景
                -- 使用style_module的金属高亮函数
                style_module.draw_metallic_highlight(cv_role_list.x, cv_role_list.y + y_offset - 2, cv_role_list.w - 10, cv_role_line_height, style_module.highlight_colors.blue.cv, false)
            end

            -- 绘制CV分类标题文字
            gfx.set(1, 1, 1, 1)  -- 白色文字
            gfx.x, gfx.y = cv_role_list.x + 5, cv_role_list.y + y_offset
            gfx.drawstr("CV: " .. cv_name)

            -- 更新Y偏移
            y_offset = y_offset + cv_role_line_height
        end
        current_item = current_item + 1

        -- 绘制该CV下的所有角色
        for _, role_info in ipairs(roles) do
            if current_item >= visible_start and (current_item - visible_start) < visible_items then
                -- 检查是否是当前选中的CV和角色或鼠标悬停的角色
                -- 需要考虑是否交换位置
                local display_selected_cv, display_selected_role

                if is_cv_role_reversed then
                    -- 如果已交换，则需要反向处理以保持显示一致
                    display_selected_cv = selected_role  -- 实际上是CV
                    display_selected_role = selected_cv  -- 实际上是角色
                else
                    -- 未交换时正常处理
                    display_selected_cv = selected_cv
                    display_selected_role = selected_role
                end

                if cv_name == display_selected_cv and role_info.role == display_selected_role then
                    -- 绘制金属效果的选中高亮效果（蓝色系）
                    local rect_h = cv_role_line_height
                    local rect_y = cv_role_list.y + y_offset - 2
                    local rect_x = cv_role_list.x
                    local rect_w = cv_role_list.w - 10

                    -- 使用style_module的金属高亮函数
                    style_module.draw_metallic_highlight(rect_x, rect_y, rect_w, rect_h, style_module.highlight_colors.blue.selected, true)
                elseif hover_cv_role.cv == cv_name and hover_cv_role.role == role_info.role and not hover_cv_role.is_cv then
                    -- 绘制金属效果的鼠标悬停高亮效果（绿色系）
                    local rect_h = cv_role_line_height
                    local rect_y = cv_role_list.y + y_offset - 2
                    local rect_x = cv_role_list.x
                    local rect_w = cv_role_list.w - 10

                    -- 使用style_module的金属高亮函数
                    style_module.draw_metallic_highlight(rect_x, rect_y, rect_w, rect_h, style_module.highlight_colors.green.hover, false)

                    -- 恢复文本颜色
                    gfx.set(1, 1, 1, 1)
                end

                -- 绘制角色名称
                gfx.x, gfx.y = cv_role_list.x + 15, cv_role_list.y + y_offset  -- 稍微缩进
                gfx.drawstr(role_info.role)

                -- 更新Y偏移
                y_offset = y_offset + cv_role_line_height
            end
            current_item = current_item + 1
        end
    end

    -- 错误输入区域标题
    gfx.set(1, 1, 1, 1)  -- 设置白色文本颜色，确保标签颜色统一
    gfx.x, gfx.y = error_input.x, error_input.y - 20
    gfx.drawstr("错误描述:")

    -- 使用模块化的方式绘制输入区域，避免重复绘制

    -- 绘制输入区域
    button_module.draw_input_area(error_input, error_note, "", "错误描述:")
    button_module.draw_input_area(correct_input, correct_note, "", "正确表达:")
    button_module.draw_input_area(episode_input, episode_number, "", "集数:")
    button_module.draw_suggestion_input(suggestion_input, process_suggestion, "", "处理建议:")

    -- 绘制当前选择的CV和角色以及错误描述（移动到输入区域下方并对齐）
    -- 使用全局的selection_area变量代替局部定义

    -- 绘制选择区域背景
    gfx.set(0.15, 0.15, 0.15, 1)  -- 深灰色背景
    gfx.rect(selection_area.x, selection_area.y, selection_area.w, selection_area.h)

    -- 添加选择区域的黑色描边
    style_module.draw_black_outline(selection_area.x, selection_area.y, selection_area.w, selection_area.h)

    -- 绘制选择区域标题
    gfx.set(1, 1, 1, 1)  -- 白色
    gfx.x, gfx.y = selection_area.x, selection_area.y - 20
    gfx.drawstr("当前选择:")

    -- 绘制选择内容（自动换行）
    local y_offset = 5
    local max_width = selection_area.w - 20  -- 减少宽度，为滚动条留出空间
    local total_height = 0  -- 计算内容总高度
    local content_to_draw = {}  -- 存储要绘制的内容

    -- 确保绘制前设置默认的白色文本
    gfx.set(1, 1, 1, 1)  -- 白色

    -- 收集所有要显示的内容并计算总高度
    if episode_number ~= "" then
      table.insert(content_to_draw, {type = "text", text = "集数: " .. episode_number, height = 20})
      total_height = total_height + 20
    end

    if selected_role ~= "" then
      local display_role = clean_text_tags(selected_role)
      table.insert(content_to_draw, {type = "text", text = "角色: " .. display_role, height = 20})
      total_height = total_height + 20
    end

    if selected_cv ~= "" then
      local display_cv = clean_text_tags(selected_cv)
      table.insert(content_to_draw, {type = "text", text = "CV: " .. display_cv, height = 20})
      total_height = total_height + 20
    end

    if error_note ~= "" then
      local error_text = "错误描述: " .. error_note
      -- 使用text_utils模块计算错误描述的高度
      gfx.setfont(1, font_name, font_size)  -- 使用固定的界面字体大小用于计算
      local text_height = text_utils.calculate_text_height(error_text, max_width, gfx)
      table.insert(content_to_draw, {type = "wrap", text = error_text, height = text_height})
      total_height = total_height + text_height
    end

    if correct_note ~= "" then
      local correct_text = "正确表达: " .. correct_note
      -- 使用text_utils模块计算正确表达的高度
      gfx.setfont(1, font_name, font_size)  -- 使用固定的界面字体大小
      local text_height = text_utils.calculate_text_height(correct_text, max_width, gfx)
      table.insert(content_to_draw, {type = "wrap", text = correct_text, height = text_height})
      total_height = total_height + text_height
    end

    if process_suggestion ~= "" then
      local suggestion_text = "处理建议: " .. process_suggestion
      -- 使用text_utils模块计算处理建议的高度
      gfx.setfont(1, font_name, font_size)  -- 使用固定的界面字体大小
      local text_height = text_utils.calculate_text_height(suggestion_text, max_width, gfx)
      table.insert(content_to_draw, {type = "wrap", text = suggestion_text, height = text_height})
      total_height = total_height + text_height
    end

    if marked_relative_time ~= "" then
      table.insert(content_to_draw, {type = "text", text = "错误时间: " .. marked_relative_time, height = 20})
      total_height = total_height + 20
    end

    if selected_text ~= "" then
      -- 分离前缀和内容，单独处理
      local prefix = "文本: "
      -- 计算前缀宽度
      gfx.setfont(1, font_name, font_size)
      local prefix_width = gfx.measurestr(prefix)

      -- 直接从选择区域宽度计算可用宽度，确保考虑滚动条空间
      local content_max_width = selection_area.w - prefix_width - 30  -- 修改：增加边距，确保文本不会超出背景框

      -- 根据是否需要滚动条进一步调整宽度
      if total_height > selection_area.h then
        content_max_width = content_max_width - 10  -- 额外减去滚动条宽度
      end

      -- 如果是多选模式，分别处理每个选中的句子
      if #selected_indices > 1 then
        local combined_height = 0
        local sentence_elements = {}

        -- 计算每个句子的高度并添加分隔符
        for i, idx in ipairs(selected_indices) do
          -- 清除句子中的标签
          local sentence_text = clean_text_tags(sentences[idx])
          local sentence_height = text_utils.calculate_text_height(sentence_text, content_max_width, gfx)

          -- 添加句子内容
          table.insert(sentence_elements, {
            type = "complex",
            prefix = "",
            content = sentence_text,
            prefix_width = 0,
            content_max_width = content_max_width,
            height = sentence_height,
            indent = 10  -- 缩进显示
          })
          combined_height = combined_height + sentence_height

          -- 除最后一个句子外，添加分隔线
          if i < #selected_indices then
            table.insert(sentence_elements, {
              type = "separator",
              height = 10
            })
            combined_height = combined_height + 10
          end
        end

        -- 添加多选文本的复合元素
        table.insert(content_to_draw, {
          type = "text",
          text = prefix,
          height = 20
        })
        total_height = total_height + 20

        -- 添加所有句子元素
        for _, element in ipairs(sentence_elements) do
          table.insert(content_to_draw, element)
          total_height = total_height + element.height
        end

      else
        -- 单选模式使用原来的处理方式
        -- 清除选中文本中的标签
        local clean_selected_text = clean_text_tags(selected_text)
        -- 计算文本内容的高度
        local content_height = text_utils.calculate_text_height(clean_selected_text, content_max_width, gfx)

        -- 添加前缀和内容作为一个复合元素
        table.insert(content_to_draw, {
          type = "complex",
          prefix = prefix,
          content = clean_selected_text,
          prefix_width = prefix_width,
          content_max_width = content_max_width,
          height = content_height + 10 -- 增加间距但不要过大
        })

        total_height = total_height + content_height + 10
      end
    end

    -- 绘制滚动条（如果内容需要滚动）
    if total_height > selection_area.h then
      -- 计算滚动条参数
      local scrollbar_width = 10
      local scrollbar_x = selection_area.x + selection_area.w - scrollbar_width
      local scrollbar_y = selection_area.y
      local scrollbar_height = selection_area.h

      -- 计算滚动条滑块参数
      local visible_ratio = selection_area.h / total_height
      local thumb_height = math.max(20, scrollbar_height * visible_ratio)
      local scroll_ratio = selection_scroll_pos / (total_height - selection_area.h)
      local thumb_y = scrollbar_y + scroll_ratio * (scrollbar_height - thumb_height)

      -- 绘制滚动条背景
      gfx.set(0.2, 0.2, 0.2, 1)
      gfx.rect(scrollbar_x, scrollbar_y, scrollbar_width, scrollbar_height)

      -- 绘制滚动条滑块
      gfx.set(0.5, 0.5, 0.5, 1)
      gfx.rect(scrollbar_x, thumb_y, scrollbar_width, thumb_height)

      -- 存储滚动条信息供鼠标事件使用
      ui.selection_scrollbar = {
        x = scrollbar_x,
        y = scrollbar_y,
        w = scrollbar_width,
        h = scrollbar_height,
        thumb_y = thumb_y,
        thumb_height = thumb_height,
        visible_ratio = visible_ratio,
        total_height = total_height
      }
    else
      -- 如果不需要滚动，重置滚动位置
      selection_scroll_pos = 0
      ui.selection_scrollbar = nil
    end

    -- 确保滚动位置不超过最大值
    local max_scroll = math.max(0, total_height - selection_area.h)
    selection_scroll_pos = math.min(selection_scroll_pos, max_scroll)

    -- 绘制内容
    y_offset = 5 - selection_scroll_pos  -- 应用滚动偏移
    for _, content in ipairs(content_to_draw) do
      if y_offset + content.height > 0 and y_offset < selection_area.h then  -- 只绘制可见区域内的内容
        if content.type == "text" then
          -- 使用白色文本（适配黑色背景）
          gfx.set(1, 1, 1, 1)
          -- 检查文本是否完全在可见区域内
          if y_offset >= 0 and y_offset + 20 <= selection_area.h then
            -- 应用缩进(如果有)
            local x_offset = content.is_header and 15 or 5
            gfx.x, gfx.y = selection_area.x + x_offset, selection_area.y + y_offset
            gfx.drawstr(content.text)
          elseif y_offset < 0 and y_offset + 20 > 0 then
            -- 文本部分可见（顶部被截断）
            local x_offset = content.is_header and 15 or 5
            gfx.x, gfx.y = selection_area.x + x_offset, selection_area.y
            gfx.drawstr(content.text)
          elseif y_offset < selection_area.h and y_offset + 20 > selection_area.h then
            -- 文本部分可见（底部被截断）
            local x_offset = content.is_header and 15 or 5
            gfx.x, gfx.y = selection_area.x + x_offset, selection_area.y + y_offset
            gfx.drawstr(content.text)
          end
        elseif content.type == "separator" then
          -- 绘制分隔线
          if y_offset >= 0 and y_offset + content.height <= selection_area.h then
            gfx.set(0.5, 0.5, 0.5, 0.5)  -- 灰色半透明
            local line_y = selection_area.y + y_offset + content.height/2
            gfx.line(selection_area.x + 20, line_y, selection_area.x + selection_area.w - 20, line_y)
            gfx.set(1, 1, 1, 1)  -- 使用白色文本
          end
        elseif content.type == "wrap" then
          -- 对于换行文本，使用text_utils的wrap_text函数但限制在可见区域内
          gfx.setfont(1, font_name, font_size)  -- 使用固定的界面字体大小，而不是内容字体大小
          -- 设置白色文本
          gfx.set(1, 1, 1, 1)
          local visible_y = math.max(0, y_offset)
          local y_pos = selection_area.y + visible_y
          -- 确保不会绘制超出底部边界的内容
          if y_pos < selection_area.y + selection_area.h then
            text_utils.wrap_text(
              content.text,
              max_width,
              selection_area.x + 5,
              y_pos,
              true,
              selection_area.y,
              selection_area.y + selection_area.h,
              gfx
            )
          end
        elseif content.type == "complex" then
          -- 对于复合元素，使用text_utils模块的wrap_text函数但限制在可见区域内
          gfx.setfont(1, font_name, font_size)  -- 使用固定的界面字体大小，而不是内容字体大小
          local visible_y = math.max(0, y_offset)
          local y_pos = selection_area.y + visible_y

          -- 确保不会绘制超出底部边界的内容
          if y_pos < selection_area.y + selection_area.h then
            -- 先绘制前缀
            gfx.set(1, 1, 1, 1)  -- 使用白色文本

            -- 计算X坐标，考虑缩进
            local indent = content.indent or 0
            local x_pos = selection_area.x + 5 + indent
            gfx.x = x_pos
            gfx.y = y_pos

            -- 如果有前缀，绘制前缀
            if content.prefix and content.prefix ~= "" then
              gfx.drawstr(content.prefix)
            end

            -- 对长文本特殊处理，分段绘制避免单次处理过多内容
            local text_to_draw = content.content
            if #text_to_draw > 200 then
              -- 将长文本分成多行预处理，提高渲染效率
              local lines = {}
              local current_line = ""
              local current_width = 0
              
              -- 确保内容最大宽度不超过实际可用宽度
              local safe_content_max_width = math.min(content.content_max_width, selection_area.w - 30)

              -- 逐字符测量并分行
              local i = 1
              while i <= #text_to_draw do
                local byte = text_to_draw:byte(i)
                local char_len = 1
                if byte >= 0xC0 and byte <= 0xDF then char_len = 2
                elseif byte >= 0xE0 and byte <= 0xEF then char_len = 3
                elseif byte >= 0xF0 and byte <= 0xF7 then char_len = 4 end

                local c = text_to_draw:sub(i, i + char_len - 1)
                local char_width = gfx.measurestr(c)

                if current_width + char_width > safe_content_max_width then
                  -- 换行
                  table.insert(lines, current_line)
                  current_line = c
                  current_width = char_width
                else
                  -- 继续当前行
                  current_line = current_line .. c
                  current_width = current_width + char_width
                end

                i = i + char_len
              end

              -- 添加最后一行
              if current_line ~= "" then
                table.insert(lines, current_line)
              end

              -- 绘制处理好的各行文本
              local line_height = math.max(20, font_size + 2)
              local line_y = y_pos

              -- 第一行需要和前缀在同一行
              if #lines > 0 and line_y >= selection_area.y and line_y < selection_area.y + selection_area.h then
                local x_offset = (content.prefix and content.prefix ~= "") and content.prefix_width or 0
                local indent = content.indent or 0
                gfx.x = selection_area.x + 5 + x_offset + indent
                gfx.y = line_y
                gfx.set(1, 1, 1, 1)  -- 使用白色文本
                gfx.drawstr(lines[1])
                line_y = line_y + line_height
              end

              -- 绘制剩余的行
              for i = 2, #lines do
                local line = lines[i]
                -- 检查是否在可见区域内
                if line_y >= selection_area.y and line_y < selection_area.y + selection_area.h then
                  local indent = content.indent or 0
                  gfx.x = selection_area.x + 10 + indent -- 应用额外缩进
                  gfx.y = line_y
                  gfx.set(1, 1, 1, 1)  -- 使用白色文本
                  gfx.drawstr(line)
                end
                line_y = line_y + line_height

                -- 如果已超出可见区域，退出循环
                if line_y > selection_area.y + selection_area.h then
                  break
                end
              end
            else
              -- 对短文本使用原始的wrap_text函数
              local indent = content.indent or 0
              local x_pos = selection_area.x + 5 + content.prefix_width + indent

              -- 确保内容最大宽度不超过实际可用宽度
              local safe_content_max_width = math.min(content.content_max_width, selection_area.w - 30)

              text_utils.wrap_text(
                text_to_draw,
                safe_content_max_width,
                x_pos,
                y_pos,
                true,
                selection_area.y,
                selection_area.y + selection_area.h,
                gfx
              )
            end
          end
        end
      end
      y_offset = y_offset + content.height
    end

    -- 绘制打标按钮
    -- 获取当前选中的音频块
    local has_selected_item = r.GetSelectedMediaItem(0, 0) ~= nil
    local role_cv_selected = selected_cv ~= "" and selected_role ~= ""
    local has_process_suggestion = process_suggestion ~= ""

    -- 块标按钮只在同时满足三个条件时才启用：1)选中了音频块；2)选择了角色和CV；3)填写了处理建议
    local block_mark_button_enabled = has_selected_item and role_cv_selected and has_process_suggestion
    -- 区标按钮只需要满足两个条件：1)选择了角色和CV；2)填写了处理建议
    local region_mark_button_enabled = role_cv_selected and has_process_suggestion
    button_module.draw_block_mark_button(block_mark_button, block_mark_button_enabled)
    button_module.draw_region_mark_button(region_mark_button, region_mark_button_enabled)

    -- 获取并显示当前编辑光标处音频块的内部真实时间
    local cursor_pos = r.GetCursorPosition() -- 获取编辑光标位置
    if cursor_pos ~= current_time then
      current_time = cursor_pos
    end

    -- 获取当前选中的音频块
    local selected_item = r.GetSelectedMediaItem(0, 0)
    local item_time_str = "无选中音频块"

    if selected_item then
      -- 获取音频块起始位置
      local item_start = r.GetMediaItemInfo_Value(selected_item, "D_POSITION")
      -- 计算相对位置（内部真实时间）
      local relative_pos = cursor_pos - item_start

      -- 确保相对位置在音频块范围内
      local item_length = r.GetMediaItemInfo_Value(selected_item, "D_LENGTH")
      if relative_pos < 0 then relative_pos = 0 end
      if relative_pos > item_length then relative_pos = item_length end

      -- 格式化内部时间
      item_time_str = text_utils.format_time(relative_pos)
    end

    -- 在处理建议输入框下方显示音频块内部时间
    gfx.x, gfx.y = suggestion_input.x, suggestion_input.y + suggestion_input.h + 20
    gfx.set(1, 1, 0, 1)  -- 黄色，使提示更加醒目
    gfx.drawstr("光标时间: " .. item_time_str)
    

    
    gfx.set(1, 1, 1, 1)  -- 恢复白色

    -- 绘制写入报告按钮
    local has_selected_text = selected_text ~= ""
    button_module.draw_excel_button(excel_button, has_selected_text)

    -- 绘制开按钮、AU按钮、区名按钮、文名按钮和轨色按钮
    button_module.draw_open_au_buttons(open_csv_button, au_button, ui.region_name_button, ui.file_name_button, ui.track_color_button, ui.track_split_button)

    -- 绘制播放/暂停按钮
    button_module.draw_play_button(play_button, is_playing)

    -- 绘制速率相关按钮
    button_module.draw_rate_buttons(rate_minus_button, rate_display_area, rate_plus_button, rate_reset_button, current_playrate)

    -- 绘制字体大小按钮
    button_module.draw_font_buttons(font_size_label, font_decrease_button, font_increase_button, font_size_display, content_font_size)

    -- 绘制搜索UI
    draw_search_ui()

    -- 最后绘制下拉菜单，确保它显示在最上层
    if show_suggestion_dropdown then
      button_module.draw_suggestion_dropdown(suggestion_input, suggestion_options, hover_suggestion_idx)
    end

    -- 不再显示调试信息
  end)

  if not status then
    debug_message = "绘制界面错误: " .. tostring(err)
    r.ShowConsoleMsg("Draw UI Error: " .. tostring(err) .. "\n")
  end

  -- 重置部分渲染标志
  partial_render = false
end

-- 包装style_module的handle_wheel_scroll函数，以匹配现有的调用签名
function handle_wheel_scroll(area, scroll_pos, items_count, visible_items, mouse_x, mouse_y, item_height)
  return style_module.handle_wheel_scroll(area, scroll_pos, items_count, visible_items, mouse_x, mouse_y, item_height)
end

-- 判断点是否在矩形内
function is_point_in_rect(x, y, rect)
  return utils_module.is_point_in_rect(x, y, rect)
end

-- 修改handle_mouse函数中的滚轮处理部分
function handle_mouse()
  local status, err = pcall(function()
    local mouse_x, mouse_y = gfx.mouse_x, gfx.mouse_y
    local mouse_cap = gfx.mouse_cap
    local wheel = gfx.mouse_wheel

    -- 定义行高和可见项目数，确保在整个函数中可用
    local line_height = font_size + 5  -- 章节标题行高
    local visible_items = math.floor((chapter_list.h - 10) / line_height)
    local y_offset = 5  -- 初始垂直偏移

    -- 定义滚动条区域
    local sentence_scrollbar = {
      x = content_area.x + content_area.w - 10,
      y = content_area.y,
      w = 10,
      h = content_area.h
    }

    local cv_role_scrollbar = {
      x = cv_role_list.x + cv_role_list.w - 10,
      y = cv_role_list.y,
      w = 10,
      h = cv_role_list.h
    }

    local chapter_scrollbar = {
      x = chapter_list.x + chapter_list.w - 10,
      y = chapter_list.y,
      w = 10,
      h = chapter_list.h
    }

    -- 优先检查下拉菜单悬停，这需要特殊处理
    if show_suggestion_dropdown then
      -- 使用按钮模块检查鼠标悬停
      hover_suggestion_idx = button_module.check_dropdown_hover(suggestion_input, suggestion_options, mouse_x, mouse_y)

      -- 如果鼠标不在下拉菜单区域内且鼠标按下，关闭下拉菜单
      if hover_suggestion_idx == -1 and mouse_cap == 1 and not is_mouse_down then
        show_suggestion_dropdown = false
      end
    else
      hover_suggestion_idx = -1
    end

    -- 处理鼠标滚轮（只在这里处理一次滚轮事件）
    if wheel ~= 0 and r.time_precise() - last_wheel_time > wheel_cooldown then
      -- 处理章节列表滚动
      if is_point_in_rect(mouse_x, mouse_y, chapter_list) then
        -- 行高和可见项目数已在函数开头定义
        local max_scroll = math.max(0, #chapters - visible_items)
        -- 减小滚动步长，使滚动更平滑
        local scroll_amount = gfx.mouse_wheel < 0 and 1 or -1
        chapter_scroll_pos = math.max(0, math.min(max_scroll, chapter_scroll_pos + scroll_amount))

      -- 处理句子列表滚动
      elseif is_point_in_rect(mouse_x, mouse_y, content_area) then
        local max_scroll = math.max(0, #sentences - 1)
        -- 减小滚动步长，使滚动更平滑
        local scroll_amount = gfx.mouse_wheel < 0 and 2 or -2
        sentence_scroll_pos = math.max(0, math.min(max_scroll, sentence_scroll_pos + scroll_amount))
        target_sentence_scroll_pos = sentence_scroll_pos
        cached_total_content_height = nil  -- 滚动时重新计算内容高度

        -- 重置章节点击跳转状态，以便正确更新当前章节
        chapter_click_jumping = false

        -- 直接更新当前章节
        if chapter_module and chapter_module.get_chapter_by_sentence_idx then
          local visible_items_count = math.floor((content_area.h - 10) / (content_font_size + 5))
          local first_visible_sentence = sentence_scroll_pos + 1
          local last_visible_sentence = math.min(#sentences, sentence_scroll_pos + visible_items_count)

          -- 获取可见区域内最后一个句子所属的章节
          local chapter, chapter_idx = chapter_module.get_chapter_by_sentence_idx(last_visible_sentence)

          -- 如果找不到章节，尝试使用第一个可见句子
          if not chapter_idx then
            chapter, chapter_idx = chapter_module.get_chapter_by_sentence_idx(first_visible_sentence)
          end

          -- 更新当前章节
          if chapter_idx and chapter_idx ~= current_chapter_idx then
            current_chapter_idx = chapter_idx

            -- 确保当前章节在章节列表中可见
            local line_height = font_size + 5
            local visible_items = math.floor((chapter_list.h - 10) / line_height)

            if chapter_idx <= chapter_scroll_pos then
              chapter_scroll_pos = math.max(0, chapter_idx - 1)
            elseif chapter_idx > chapter_scroll_pos + visible_items then
              chapter_scroll_pos = math.min(#chapters - visible_items, chapter_idx - visible_items)
            end
          end
        end

      -- 处理CV角色列表滚动
      elseif is_point_in_rect(mouse_x, mouse_y, cv_role_list) then
        -- 使用基于字体大小的动态行高
        local cv_role_line_height = math.max(20, font_size + 2)

        -- 按CV分类整理角色列表，以计算实际的总项目数
        local cv_categories = {}  -- 用于存储按CV分类的角色
        local cv_order = {}       -- 用于记录CV的顺序

        -- 遍历所有CV角色对，按CV进行分类
        for i, pair in ipairs(cv_role_pairs) do
            local cv_name = pair.cv
            if not cv_categories[cv_name] then
                cv_categories[cv_name] = {}
                table.insert(cv_order, cv_name)
            end
            table.insert(cv_categories[cv_name], {role = pair.role, index = i})
        end

        -- 计算所有分类项的总数（包括CV分类标题）
        local total_items = 0
        for _, roles in pairs(cv_categories) do
            -- 每个CV类别标题占一行，加上所有角色
            total_items = total_items + 1 + #roles
        end

        -- 计算可见项目数和最大滚动位置
        local visible_items = math.floor((cv_role_list.h - 10) / cv_role_line_height)
        local max_scroll = math.max(0, total_items - visible_items)

        -- 减小滚动步长，使滚动更平滑
        local scroll_amount = gfx.mouse_wheel < 0 and 1 or -1
        cv_role_scroll_pos = math.max(0, math.min(max_scroll, cv_role_scroll_pos + scroll_amount))
        target_cv_role_scroll_pos = cv_role_scroll_pos

      -- 处理当前选择列表滚动
      elseif is_point_in_rect(mouse_x, mouse_y, selection_area) then
        -- 只有当有滚动条且内容溢出时才处理滚动
        if ui.selection_scrollbar then
          -- 使用ui中存储的实际内容高度计算最大滚动位置
          local max_scroll = math.max(0, ui.selection_scrollbar.total_height - selection_area.h)
          -- 增加滚动步长，使滚动更快
          local scroll_amount = gfx.mouse_wheel < 0 and 15 or -15
          selection_scroll_pos = math.max(0, math.min(max_scroll, selection_scroll_pos + scroll_amount))
          target_selection_scroll_pos = selection_scroll_pos
        end
      end

      -- 重置滚轮冷却时间，防止滚动太快
      last_wheel_time = r.time_precise()
    end

    -- 计算章节列表悬停状态
    if #chapters > 0 then
      local y_offset = 5
      local max_visible_idx = math.min(#chapters, chapter_scroll_pos + visible_items)

      -- 记录上一次悬停索引，用于检测变化
      local prev_hover_idx = hover_chapter_idx

      if is_point_in_rect(mouse_x, mouse_y, chapter_list) and
         not is_point_in_rect(mouse_x, mouse_y, {x = chapter_list.x + chapter_list.w - 10, y = chapter_list.y, w = 10, h = chapter_list.h}) then
        -- 重置悬停索引
        hover_chapter_idx = -1

        -- 只遍历可见的章节，提高性能
        for i = chapter_scroll_pos + 1, max_visible_idx do
          if mouse_y >= chapter_list.y + y_offset - 2 and
             mouse_y < chapter_list.y + y_offset + line_height - 2 then
            hover_chapter_idx = i
            break
          end
          y_offset = y_offset + line_height
        end

        -- 只在悬停状态变化时才强制重绘，减少不必要的重绘
        if hover_chapter_idx ~= prev_hover_idx then
          force_redraw = true
        end
      else
        -- 只在之前有悬停时才重置并重绘
        if hover_chapter_idx > 0 then
          hover_chapter_idx = -1
          force_redraw = true
        end
      end
    else
      hover_chapter_idx = -1  -- 无章节数据，清除悬停状态
    end

    -- 检查CV角色列表悬停状态
    if #cv_role_pairs > 0 and is_point_in_rect(mouse_x, mouse_y, cv_role_list) and
       not is_point_in_rect(mouse_x, mouse_y, cv_role_scrollbar) then

      -- 记录上一次悬停状态，用于检测变化
      local prev_hover_cv_role = {cv = hover_cv_role.cv, role = hover_cv_role.role, is_cv = hover_cv_role.is_cv}
      hover_cv_role = {cv = "", role = "", is_cv = false}

      -- 使用基于字体大小的动态行高
      local cv_role_line_height = math.max(20, font_size + 2)
      local y_offset = 5

      -- 按CV分类整理角色列表
      -- 使用辅助函数获取CV角色分类数据，确保无论是否交换位置都保持一致的显示
      local cv_categories, cv_order = get_cv_role_categories(cv_role_pairs, is_cv_role_reversed)

      -- 计算所有可见项并找出悬停的项
      local current_item = 1
      local visible_start = cv_role_scroll_pos + 1
      local visible_items = math.floor((cv_role_list.h - 10) / cv_role_line_height)

      -- 遍历所有CV分类
      for _, cv_name in ipairs(cv_order) do
          local roles = cv_categories[cv_name]

          -- 检查是否悬停在CV分类标题上
          if current_item >= visible_start and (current_item - visible_start) < visible_items then
              if mouse_y >= cv_role_list.y + y_offset - 2 and
                 mouse_y < cv_role_list.y + y_offset + cv_role_line_height - 2 then
                  -- 悬停在CV分类标题上
                  hover_cv_role = {cv = cv_name, role = "", is_cv = true}
                  break
              end
              y_offset = y_offset + cv_role_line_height
          end
          current_item = current_item + 1

          -- 检查是否悬停在该CV下的某个角色上
          for _, role_info in ipairs(roles) do
              if current_item >= visible_start and (current_item - visible_start) < visible_items then
                  if mouse_y >= cv_role_list.y + y_offset - 2 and
                     mouse_y < cv_role_list.y + y_offset + cv_role_line_height - 2 then
                      -- 悬停在角色上
                      hover_cv_role = {cv = cv_name, role = role_info.role, is_cv = false}
                      break
                  end
                  y_offset = y_offset + cv_role_line_height
              end
              current_item = current_item + 1
          end

          -- 如果找到了悬停项，结束遍历
          if hover_cv_role.cv ~= "" then break end
      end

      -- 只在悬停状态变化时才强制重绘，减少不必要的重绘
      if hover_cv_role.cv ~= prev_hover_cv_role.cv or
         hover_cv_role.role ~= prev_hover_cv_role.role or
         hover_cv_role.is_cv ~= prev_hover_cv_role.is_cv then
        force_redraw = true
      end
    else
      -- 只在之前有悬停时才重置并重绘
      if hover_cv_role.cv ~= "" then
        hover_cv_role = {cv = "", role = "", is_cv = false}
        force_redraw = true
      end
    end

    -- 检查鼠标是否在内容区域内，并更新悬停句子索引
    if is_point_in_rect(mouse_x, mouse_y, content_area) and
       not is_point_in_rect(mouse_x, mouse_y, sentence_scrollbar) then

      -- 记录上一次悬停索引，用于检测变化
      local prev_hover_idx = hover_sentence_idx
      hover_sentence_idx = -1 -- 重置悬停索引

      -- 使用与绘制句子相同的计算方法来确定鼠标悬停位置
      local current_y = 5 - (sentence_scroll_pos % 1) * (content_font_size + math.max(3, math.floor(content_font_size * 0.1)))
      local start_idx = math.floor(sentence_scroll_pos) + 1
      local safety_margin = math.max(3, math.floor(content_font_size * 0.1))
      local max_width = content_area.w - 4
      
      -- 只遍历当前可见的句子，提高性能
      for i = start_idx, #sentences do
        -- 跳过标记为不显示的句子
        if not sentences[i] or sentences[i] == "__SKIP_THIS_SENTENCE__" then
          goto continue
        end
        
        -- 获取句子高度（使用缓存或计算）
        if not cached_sentence_heights[i] then
          gfx.setfont(1, font_name, content_font_size)
          cached_sentence_heights[i] = text_utils.calculate_text_height(
            sentences[i], max_width, gfx)
        end
        local sentence_height = cached_sentence_heights[i]
        
        -- 计算句子的Y坐标范围
        local sentence_y = content_area.y + current_y
        local sentence_bottom = sentence_y + sentence_height
        
        -- 检查鼠标是否在当前句子区域内
        if mouse_y >= sentence_y and mouse_y <= sentence_bottom then
          hover_sentence_idx = i
          break
        end
        
        -- 更新下一个句子的起始位置
        current_y = current_y + sentence_height + safety_margin
        
        -- 如果已经超出可见区域，停止检查
        if content_area.y + current_y > content_area.y + content_area.h then
          break
        end
        
        ::continue::
      end

      -- 只在悬停状态变化时才强制重绘，减少不必要的重绘
      if hover_sentence_idx ~= prev_hover_idx then
        force_redraw = true
      end
    else
      -- 只在之前有悬停时才重置并重绘
      if hover_sentence_idx > 0 then
        hover_sentence_idx = -1
        force_redraw = true
      end
    end

    -- 处理鼠标按下事件
    if (mouse_cap & 3) > 0 then  -- 检测左键(1)或右键(2)是否按下
      -- 如果鼠标刚刚按下
      if not is_mouse_down then
        is_mouse_down = true

        -- 优先处理下拉菜单相关的点击
        if show_suggestion_dropdown then
          -- 使用按钮模块检查鼠标点击位置
          local option_idx = button_module.check_dropdown_hover(suggestion_input, suggestion_options, mouse_x, mouse_y)

          if option_idx > 0 then
            -- 选择该选项
            process_suggestion = suggestion_options[option_idx]
            last_selected_suggestion = process_suggestion
            show_suggestion_dropdown = false
            last_click_time = r.time_precise()  -- 更新点击时间
            return -- 直接返回，不处理其他点击
          else
            -- 点击下拉菜单外部，关闭下拉菜单
            show_suggestion_dropdown = false
            -- 继续执行，处理其他点击
          end
        elseif is_point_in_rect(mouse_x, mouse_y, suggestion_input) then
          -- 处理建议输入区域点击，显示下拉菜单
          show_suggestion_dropdown = true
          last_click_time = r.time_precise()  -- 更新点击时间
          return -- 直接返回，不处理其他点击
        end

        -- 检查章节列表点击（不包括滚动条区域）
        if is_point_in_rect(mouse_x, mouse_y, chapter_list) and
           not is_point_in_rect(mouse_x, mouse_y, {x = chapter_list.x + chapter_list.w - 10, y = chapter_list.y, w = 10, h = chapter_list.h}) and
           #chapters > 0 then
          -- 在点击时重新计算点击的章节索引
          local y_offset = 5
          local clicked_chapter_idx = -1
          local max_visible_idx = math.min(#chapters, chapter_scroll_pos + visible_items)

          for i = chapter_scroll_pos + 1, max_visible_idx do
            if mouse_y >= chapter_list.y + y_offset - 2 and
               mouse_y < chapter_list.y + y_offset + line_height - 2 then
              clicked_chapter_idx = i
              break
            end
            y_offset = y_offset + line_height
          end

          -- 使用计算得到的章节索引替代hover_chapter_idx
          if clicked_chapter_idx > 0 and clicked_chapter_idx <= #chapters then
            -- 设置跳转状态标记
            chapter_click_jumping = true

            -- 跳转到对应章节
            local success, message = chapter_module.jump_to_chapter(clicked_chapter_idx, function(target_idx)
              -- 设置滚动位置，使目标句子显示在可见区域
              sentence_scroll_pos = math.max(0, target_idx - 1)
              target_sentence_scroll_pos = sentence_scroll_pos

              -- 直接更新当前章节索引，确保章节高亮正确显示
              current_chapter_idx = clicked_chapter_idx
              -- 确保当前章节在章节列表中可见
              ensure_chapter_visible(clicked_chapter_idx)

              force_redraw = true

              -- 重置跳转状态标记
              chapter_click_jumping = false
            end)

            last_click_time = r.time_precise()  -- 更新点击时间
            return -- 直接返回，不处理其他点击
          end
        end

        -- 检查滚动条操作，这些需要快速响应
        -- 检查句子滚动条点击
        if is_point_in_rect(mouse_x, mouse_y, sentence_scrollbar) and #sentences > 0 then
          is_dragging_sentence_scrollbar = true
          -- 直接根据点击位置设置滚动位置
          local scroll_ratio = (mouse_y - sentence_scrollbar.y) / sentence_scrollbar.h
          local max_scroll = math.max(0, #sentences - 1)
          sentence_scroll_pos = math.floor(scroll_ratio * max_scroll)
          sentence_scroll_pos = math.max(0, math.min(max_scroll, sentence_scroll_pos))
          target_sentence_scroll_pos = sentence_scroll_pos
          cached_total_content_height = nil  -- 滚动时重新计算内容高度
          return -- 直接返回，提高滚动条响应速度
        end

        -- 检查CV角色滚动条点击
        if is_point_in_rect(mouse_x, mouse_y, cv_role_scrollbar) and #cv_role_pairs > 0 then
          is_dragging_cv_role_scrollbar = true
          -- 直接根据点击位置设置滚动位置
          local scroll_ratio = (mouse_y - cv_role_scrollbar.y) / cv_role_scrollbar.h
          local visible_items = math.floor((cv_role_list.h - 10) / 20)
          local max_scroll = math.max(0, #cv_role_pairs - visible_items)
          cv_role_scroll_pos = math.floor(scroll_ratio * max_scroll)
          cv_role_scroll_pos = math.max(0, math.min(max_scroll, cv_role_scroll_pos))
          target_cv_role_scroll_pos = cv_role_scroll_pos
          return -- 直接返回，提高滚动条响应速度
        end

        -- 检查选择区域滚动条点击
        if ui.selection_scrollbar and is_point_in_rect(mouse_x, mouse_y, ui.selection_scrollbar) then
          is_dragging_selection_scrollbar = true
          -- 使用ui中存储的实际内容高度
          local scroll_ratio = (mouse_y - ui.selection_scrollbar.y) / ui.selection_scrollbar.h
          local max_scroll = math.max(0, ui.selection_scrollbar.total_height - selection_area.h)
          selection_scroll_pos = math.floor(scroll_ratio * max_scroll)
          selection_scroll_pos = math.max(0, selection_scroll_pos)
          target_selection_scroll_pos = selection_scroll_pos
          return -- 直接返回，提高滚动条响应速度
        end

        -- 检查内容区域拖拽调整大小区域点击
        if ui.content_resize_handle and style_module.is_mouse_in_resize_handle(mouse_x, mouse_y, ui.content_resize_handle) then
          style_module.start_content_resize(mouse_y)
          return -- 直接返回，开始拖拽调整大小
        end

        -- 检查章节滚动条点击
        if is_point_in_rect(mouse_x, mouse_y, chapter_scrollbar) and #chapters > 0 then
          is_dragging_chapter_scrollbar = true
          -- 直接根据点击位置设置滚动位置
          local scroll_ratio = (mouse_y - chapter_scrollbar.y) / chapter_scrollbar.h
          local line_height = font_size + 5  -- 章节标题行高
          local visible_items = math.floor((chapter_list.h - 10) / line_height)
          local max_scroll = math.max(0, #chapters - visible_items)
          chapter_scroll_pos = math.floor(scroll_ratio * max_scroll)
          chapter_scroll_pos = math.max(0, math.min(max_scroll, chapter_scroll_pos))
          return -- 直接返回，提高滚动条响应速度
        end

        -- 获取当前点击时间
        local current_click_time = r.time_precise()
        -- 检查是否超过冷却时间
        if current_click_time - last_click_time < click_cooldown then
          return -- 点击太频繁，直接返回不处理
        end

        -- 更新上次点击时间
        last_click_time = current_click_time

        -- 处理搜索相关点击事件
        if ui.search_box and is_point_in_rect(mouse_x, mouse_y, ui.search_box) then
          -- 点击搜索框，激活搜索输入
          search_input_active = true
          return
        end
        
        if ui.search_prev_btn and is_point_in_rect(mouse_x, mouse_y, ui.search_prev_btn) then
          -- 点击上一个搜索结果按钮
          goto_prev_search_result()
          return
        end
        
        if ui.search_next_btn and is_point_in_rect(mouse_x, mouse_y, ui.search_next_btn) then
          -- 点击下一个搜索结果按钮
          goto_next_search_result()
          return
        end

        -- 处理其他按钮点击
        -- 检查字体缩小按钮点击
        if is_point_in_rect(mouse_x, mouse_y, font_decrease_button) then
          -- 获取新的字体大小
          local new_size, message = button_module.handle_font_decrease_click(content_font_size)
          local old_size = content_font_size

          -- 更新内容字体大小
          content_font_size = new_size

          -- 确保显示的消息中的字体大小与实际应用的字体大小一致
          message = string.format("字体大小已调整为: %d", content_font_size)

          -- 只有字体大小真正变化时才执行缓存清理和异步处理
          if old_size ~= content_font_size then
            -- 不清除所有缓存，只清除总高度缓存
            cached_total_content_height = nil

            -- 标记开始字体缩放异步处理
            font_scaling_in_progress = true
            font_scale_start_time = r.time_precise()
            last_font_scale_process_time = font_scale_start_time
            font_scale_batch_size = 10 -- 起始批处理大小，后续会自适应调整

            -- 优先计算可见区域内容 - 但只计算第一个，其余的留给异步处理
            local start_idx = math.max(1, sentence_scroll_pos)
            if sentences[start_idx] then
              cached_sentence_heights[start_idx] = calculate_sentence_height(sentences[start_idx])
            end
          end
          force_redraw = true  -- 强制重绘界面
        end

        -- 检查字体增大按钮点击
        if is_point_in_rect(mouse_x, mouse_y, font_increase_button) then
          -- 获取新的字体大小
          local new_size, message = button_module.handle_font_increase_click(content_font_size)
          local old_size = content_font_size

          -- 更新内容字体大小
          content_font_size = new_size

          -- 确保显示的消息中的字体大小与实际应用的字体大小一致
          message = string.format("字体大小已调整为: %d", content_font_size)

          -- 只有字体大小真正变化时才执行缓存清理和异步处理
          if old_size ~= content_font_size then
            -- 不清除所有缓存，只清除总高度缓存
            cached_total_content_height = nil

            -- 标记开始字体缩放异步处理
            font_scaling_in_progress = true
            font_scale_start_time = r.time_precise()
            last_font_scale_process_time = font_scale_start_time
            font_scale_batch_size = 10 -- 起始批处理大小，后续会自适应调整

            -- 优先计算可见区域内容 - 但只计算第一个，其余的留给异步处理
            local start_idx = math.max(1, sentence_scroll_pos)
            if sentences[start_idx] then
              cached_sentence_heights[start_idx] = calculate_sentence_height(sentences[start_idx])
            end
          end
          force_redraw = true  -- 强制重绘界面
        end

        -- 检查读取文档按钮点击
        if is_point_in_rect(mouse_x, mouse_y, document_button) then
          button_module.handle_document_button({
            read_text_file = read_text_file,
            parse_sentences = parse_sentences,
            extract_cv_role_pairs = extract_cv_role_pairs,
            set_clipboard_text = set_clipboard_text,  -- 添加设置剪贴板文本的回调
            handle_text_content = handle_text_content  -- 添加处理文本内容的回调
          })
          update_current_chapter()  -- 确保更新当前章节显示
        end

        -- 检查剪贴板按钮点击
        if is_point_in_rect(mouse_x, mouse_y, clipboard_button) then
          button_module.handle_clipboard_button({
            get_clipboard = get_clipboard,
            parse_sentences = parse_sentences,
            extract_cv_role_pairs = extract_cv_role_pairs
          })
          update_current_chapter()  -- 添加这一行
        end

        -- 检查CV角色位置切换复选框点击
        if is_point_in_rect(mouse_x, mouse_y, ui.cv_role_reverse_checkbox) or
           is_point_in_rect(mouse_x, mouse_y, ui.cv_role_reverse_label) then
          handle_cv_role_checkbox_click()
        end

        -- 检查内容区域点击（不包括滚动条区域）
        if is_point_in_rect(mouse_x, mouse_y, content_area) and
           not is_point_in_rect(mouse_x, mouse_y, sentence_scrollbar) then
          -- 如果有悬停的句子，则选择该句子
          if hover_sentence_idx > 0 and hover_sentence_idx <= #sentences then
            -- 检测当前鼠标按键状态 (左键=1, 右键=2)
            local mouse_cap = gfx.mouse_cap

            -- 确定是左键还是右键点击
            local is_left_click = (mouse_cap & 1) == 1  -- 检查最低位(1)是否置位
            local is_right_click = (mouse_cap & 2) == 2 -- 检查第二位(2)是否置位，代表右键

            -- 初始化或确保选择数组存在
            selected_indices = selected_indices or {}
            selected_texts = selected_texts or {}

            -- 处理鼠标点击
            if is_right_click then
              -- 检查对轨开关是否开启
              if is_track_align_enabled then
                -- 对轨开关开启时，先选中句子，然后执行empty_media脚本
                -- 设置选中文本
                selected_text = sentences[hover_sentence_idx]
                
                -- 清空多选列表，设置为单选
                selected_indices = {hover_sentence_idx}
                selected_texts = {selected_text}
                is_ctrl_down = false
                
                -- 提取CV和角色信息（右键选择时重新提取，不保留之前的值）
                if text_utils and text_utils.handle_cv_role_selection then
                  selected_role, selected_cv = text_utils.handle_cv_role_selection(
                    selected_text,
                    cv_role_pairs,
                    "",  -- 传入空字符串，强制重新提取
                    "",  -- 传入空字符串，强制重新提取
                    is_cv_role_reversed
                  )
                  -- 识别到CV和角色信息
                  scroll_to_cv_role(selected_role, selected_cv)
                  
                  -- 调用track_align_script模块，传递right_click标识
                  if selected_cv and selected_cv ~= "" then
                    -- 获取当前脚本路径并构建track_align_script路径
                    local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
                    local script_dir = script_path:match("(.+[\\/])") or "./"
                    local track_align_script_path = script_dir .. "track_align_script.lua"
                    
                    -- 检查脚本文件是否存在
                    local track_file = io.open(track_align_script_path, "r")
                    if track_file then
                      track_file:close()
                      -- 调用对轨脚本，传递CV信息和右键标识
                      local status, result = pcall(function()
                        -- 设置全局变量供脚本使用
                        _G.selected_cv_for_track_align = selected_cv
                        _G.selected_role_for_track_align = selected_role
                        _G.is_right_click_for_track_align = true  -- 标识这是右键操作
                        
                        -- 执行脚本
                        dofile(track_align_script_path)
                      end)
                      
                      if status then
                        -- 右键操作完成
                      else
                        -- 右键操作失败
                      end
                    else
                      -- 轨道对齐脚本文件不存在
                    end
                  else
                    -- 未识别到有效CV，跳过右键操作
                  end
                end
              else
                -- 对轨开关关闭时，执行原有的多选逻辑
                -- 首次右键点击时或从左键切换到右键时，清空所有之前的选择
                if #selected_indices == 0 or (selected_indices and #selected_indices == 1 and not is_ctrl_down) then
                  -- 清空所有选择
                  selected_indices = {}
                  selected_texts = {}
                  selected_text = ""
                  -- 添加当前句子
                  table.insert(selected_indices, hover_sentence_idx)
                  table.insert(selected_texts, sentences[hover_sentence_idx])
                else
                  -- 已经在多选模式下，进行切换选择
                  local found = false
                  local index_to_remove = nil

                  -- 检查当前句子是否已在选择列表中
                  for i, idx in ipairs(selected_indices) do
                    if idx == hover_sentence_idx then
                      found = true
                      index_to_remove = i
                      break
                    end
                  end

                  -- 如果已选中则移除，否则添加
                  if found and index_to_remove then
                    table.remove(selected_indices, index_to_remove)
                    table.remove(selected_texts, index_to_remove)
                  else
                    table.insert(selected_indices, hover_sentence_idx)
                    table.insert(selected_texts, sentences[hover_sentence_idx])
                  end
                end

                -- 始终将状态设置为多选模式
                 is_ctrl_down = true

                 -- 更新选中文本显示
                 if #selected_texts > 0 then
                   selected_text = table.concat(selected_texts, "\n")

                   -- 更新CV和角色信息（从第一个选中的句子获取）
                   if text_utils and text_utils.handle_cv_role_selection then
                     -- 使用新的统一处理函数
                     selected_role, selected_cv = text_utils.handle_cv_role_selection(
                       sentences[selected_indices[1]],
                       cv_role_pairs,
                       selected_role,
                       selected_cv,
                       is_cv_role_reversed
                     )
                     -- 自动跳转到对应的角色CV位置
                     scroll_to_cv_role(selected_role, selected_cv)
                   end
                 else
                   selected_text = ""
                 end
               end
            elseif is_left_click then
              -- 左键点击，进入单选模式
              is_ctrl_down = false

              -- 设置选中文本
              selected_text = sentences[hover_sentence_idx]

              -- 左键单选模式下，清空多选列表
              selected_indices = {hover_sentence_idx}
              selected_texts = {selected_text}

              -- 提取CV和角色信息
              if text_utils and text_utils.handle_cv_role_selection then
                -- 使用新的统一处理函数
                selected_role, selected_cv = text_utils.handle_cv_role_selection(
                  selected_text,
                  cv_role_pairs,
                  selected_role,
                  selected_cv,
                  is_cv_role_reversed
                )
                -- 自动跳转到对应的角色CV位置
                scroll_to_cv_role(selected_role, selected_cv)
              end

              -- 检查是否点击了章节句子，如果是且对轨开关打开，则创建区间
              if is_track_align_enabled then
                -- 利用现有的CV识别逻辑：当CV被识别为章节内容时，执行CreateRegionForAuto
                -- 系统已经通过text_utils.extract_cv_role将章节识别为CV
                if selected_cv and selected_cv ~= "" then
                  -- 检查CV是否为章节内容（通过检查是否包含章节标记或章节格式）
                  local is_chapter_cv = false
                  
                  -- 检查是否包含章节标记
                  if selected_cv:match("%[CHAPTER%](.-)%[/CHAPTER%]") then
                    is_chapter_cv = true
                  else
                    -- 检查是否为章节标题格式
                    if selected_cv:match("^第[%d一二三四五六七八九十百千]+[章节集话回]") or
                       selected_cv:match("^章节[%d一二三四五六七八九十百千]+") then
                      is_chapter_cv = true
                    end
                  end
                  
                  -- 如果CV是章节内容，直接调用本地函数创建区间
                   if is_chapter_cv then
                     -- 直接调用本地的CreateRegionForAuto函数，传递章节名
                     local success, err = pcall(CreateRegionForAuto, selected_cv)
                     
                     if success then
                       debug_message = "已识别章节CV: " .. selected_cv .. "，创建区间成功"
                     else
                       debug_message = "创建区间失败: " .. tostring(err)
                     end
                     
                     -- 不继续执行对轨脚本，直接返回
                     return
                  else
                    debug_message = "CV不是章节: " .. selected_cv
                  end
                else
                  debug_message = "未选中CV或CV为空"
                end
              end
              
              -- 对轨功能：当对轨开关打开时，识别CV和轨道并调用脚本
              if is_track_align_enabled and selected_cv and selected_cv ~= "" then
                -- 获取当前脚本路径并构建track_align_script路径
                local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
                local script_dir = script_path:match("(.+[\\/])") or "./"
                local track_align_script_path = script_dir .. "track_align_script.lua"
                
                -- 检查脚本文件是否存在
                local file = io.open(track_align_script_path, "r")
                if file then
                  file:close()
                  -- 调用对轨脚本，传递CV信息
                  local status, result = pcall(function()
                    -- 设置全局变量供脚本使用
                    _G.selected_cv_for_track_align = selected_cv
                    _G.selected_role_for_track_align = selected_role
                    _G.is_right_click_for_track_align = false  -- 标识这是左键操作
                    
                    -- 执行脚本
                    dofile(track_align_script_path)
                  end)
                  
                  if status then
                    -- 对轨脚本执行成功
                  else
                    -- 对轨脚本执行失败
                  end
                else
                  -- 对轨脚本文件不存在
                end
              end
            end

            force_redraw = true -- 强制重绘界面以显示选择效果
          end
        end

        -- 检查CV角色列表点击（不包括滚动条区域）
        if is_point_in_rect(mouse_x, mouse_y, cv_role_list) and
           not is_point_in_rect(mouse_x, mouse_y, cv_role_scrollbar) then

          -- 使用基于字体大小的动态行高
          local cv_role_line_height = math.max(20, font_size + 2)
          local y_offset = 5

          -- 恢复按CV分类的数据结构（与绘制函数保持一致）
          local cv_categories = {}  -- 用于存储按CV分类的角色
          local cv_order = {}       -- 用于记录CV的顺序

          -- 遍历所有CV角色对，按CV进行分类
          -- 无论是否选择了交换位置复选框，都以相同的方式显示
          for i, pair in ipairs(cv_role_pairs) do
              -- 始终保持角色在右边，CV在左边的显示方式
              local cv_name = pair.cv
              local role_name = pair.role

              if not cv_categories[cv_name] then
                  cv_categories[cv_name] = {}
                  table.insert(cv_order, cv_name)
              end
              table.insert(cv_categories[cv_name], {role = role_name, index = i})
          end

          -- 计算所有可见项并找出点击的项
          local current_item = 1
          local visible_start = cv_role_scroll_pos + 1
          local found_item = false
          local selected_cv_name = ""
          local selected_role_name = ""
          local selected_pair_index = -1

          -- 遍历所有CV分类
          for _, cv_name in ipairs(cv_order) do
              local roles = cv_categories[cv_name]

              -- 检查是否点击在CV分类标题上
              if current_item >= visible_start then
                  if mouse_y >= cv_role_list.y + y_offset - 2 and
                     mouse_y < cv_role_list.y + y_offset + cv_role_line_height - 2 then
                      -- 点击了CV分类标题，选择该CV（所有角色使用该CV）
                      selected_cv_name = cv_name
                      -- 保持当前角色不变或使用该CV下的第一个角色
                      if #roles > 0 then
                          selected_role_name = roles[1].role
                          selected_pair_index = roles[1].index
                      end
                      found_item = true
                      break
                  end
                  y_offset = y_offset + cv_role_line_height
              end
              current_item = current_item + 1

              -- 检查是否点击在该CV下的某个角色上
              for _, role_info in ipairs(roles) do
                  if current_item >= visible_start then
                      if mouse_y >= cv_role_list.y + y_offset - 2 and
                         mouse_y < cv_role_list.y + y_offset + cv_role_line_height - 2 then
                          -- 找到了点击的角色
                          selected_cv_name = cv_name
                          selected_role_name = role_info.role
                          selected_pair_index = role_info.index
                          found_item = true
                          break
                      end
                      y_offset = y_offset + cv_role_line_height
                  end
                  current_item = current_item + 1
              end

              if found_item then break end
          end

          -- 如果找到了点击的项，更新选择状态
          if found_item and selected_pair_index > 0 then
              -- 使用统一的handle_cv_role_selection函数处理角色和CV选择
              if text_utils and text_utils.handle_cv_role_selection then
                  -- 构造点击项的文本用于处理
                  local clicked_text = selected_cv_name .. ":" .. selected_role_name
                  selected_role, selected_cv = text_utils.handle_cv_role_selection(
                      clicked_text,
                      cv_role_pairs,
                      selected_role,
                      selected_cv,
                      is_cv_role_reversed
                  )
              else
                  -- 后备方案：直接使用点击的CV角色对
                  if is_cv_role_reversed then
                      selected_role = selected_cv_name
                      selected_cv = selected_role_name
                  else
                      selected_cv = selected_cv_name
                      selected_role = selected_role_name
                  end
              end

              -- 更新所有相关状态
              force_redraw = true
          end
        end

        -- 检查错误输入区域点击
        if is_point_in_rect(mouse_x, mouse_y, error_input) then
          error_note = button_module.handle_error_input_click(error_note)
        end

        -- 检查正确表达输入区域点击
        if is_point_in_rect(mouse_x, mouse_y, correct_input) then
          correct_note = button_module.handle_correct_input_click(correct_note)
        end

        -- 检查集数输入区域点击
        if is_point_in_rect(mouse_x, mouse_y, episode_input) then
          -- 首先尝试从当前选择的音频块获取集数
          local auto_episode = utils_module.get_episode_from_selected_item()
          episode_number = button_module.handle_episode_input_click(episode_number, auto_episode)
        end

        -- 检查处理建议输入区域点击
        if is_point_in_rect(mouse_x, mouse_y, suggestion_input) then
          -- 切换下拉菜单显示状态
          show_suggestion_dropdown = not show_suggestion_dropdown
          -- 如果下拉菜单即将显示，重置悬停索引
          if show_suggestion_dropdown then
            hover_suggestion_idx = -1
          end
        end

        -- 检查写入报告按钮点击
        if is_point_in_rect(mouse_x, mouse_y, excel_button) and mouse_cap == 1 and style_module.last_mouse_state == 0 then
          -- 只有在选中了句子时才执行写入报告操作
          if selected_text ~= "" then
            -- 检查按钮是否已在动画中，避免重复触发
            if not style_module.is_button_animating(excel_button) then
              -- 开始按钮动画，动画完成后执行业务逻辑
              style_module.start_button_animation(excel_button, function()
                -- 调用Excel模块处理按钮点击
                local params = {
                  episode_number = episode_number,
                  marked_relative_time = marked_relative_time,
                  selected_text = selected_text,
                  error_note = error_note,
                  correct_note = correct_note,
                  process_suggestion = process_suggestion,
                  selected_role = selected_role,
                  selected_cv = selected_cv
                }

                button_module.handle_excel_button_click(params)
              end)
            end
          end
        end

        -- 处理"开"按钮点击 - 打开CSV文件
        if is_point_in_rect(mouse_x, mouse_y, open_csv_button) then
          style_module.open_csv_file()
          force_redraw = true
        end

        -- 处理"AU"按钮点击 - 调用JHKAU.lua脚本
        if is_point_in_rect(mouse_x, mouse_y, au_button) then
          style_module.run_au_script()
          force_redraw = true
        end

        -- 处理"区名"按钮点击 - 将区间名修改为章节名
        if is_point_in_rect(mouse_x, mouse_y, ui.region_name_button) then
          button_module.handle_region_name_button_click(chapters)
          force_redraw = true
        end

        -- 处理"文名"按钮点击 - 将音频块文件名修改为章节名
        if is_point_in_rect(mouse_x, mouse_y, ui.file_name_button) then
          button_module.handle_file_name_button_click(chapters)
          force_redraw = true
        end

        -- 处理"轨色"按钮点击 - 根据CV段落颜色设置轨道颜色
        if is_point_in_rect(mouse_x, mouse_y, ui.track_color_button) then
          button_module.handle_track_color_button_click(sentences, cv_role_pairs, is_cv_role_reversed)
          force_redraw = true
        end

        -- 处理"分轨"按钮点击 - 将选中的音频块按名称分配到对应轨道
        if is_point_in_rect(mouse_x, mouse_y, ui.track_split_button) then
          button_module.handle_track_split_button_click()
          force_redraw = true
        end

        -- 检查播放/暂停按钮点击
        if is_point_in_rect(mouse_x, mouse_y, play_button) then
          -- 切换播放状态
          local message, playing = button_module.handle_play_button_click()
          is_playing = playing
        end

        -- 检查减速按钮点击
        if is_point_in_rect(mouse_x, mouse_y, rate_minus_button) then
          -- 减小播放速率
          local message, new_rate = button_module.handle_rate_minus_button_click()
          current_playrate = new_rate
          -- 标记减速按钮被按下
          rate_button_hold_state.minus_pressed = true
          rate_button_hold_state.last_adjust_time = r.time_precise()
        end

        -- 检查加速按钮点击
        if is_point_in_rect(mouse_x, mouse_y, rate_plus_button) then
          -- 增加播放速率
          local message, new_rate = button_module.handle_rate_plus_button_click()
          current_playrate = new_rate
          -- 标记加速按钮被按下
          rate_button_hold_state.plus_pressed = true
          rate_button_hold_state.last_adjust_time = r.time_precise()
        end

        -- 检查速率显示区域点击（双击重置为1.0）
        if is_point_in_rect(mouse_x, mouse_y, rate_reset_button) then
          -- 点击重置速率按钮，将播放速率重置为1.0
          local message, new_rate = button_module.handle_rate_reset_button_click()
          current_playrate = new_rate
        end

        -- 检查打标按钮点击
        -- 检查是否选中了音频块和角色CV
        local has_selected_item = r.GetSelectedMediaItem(0, 0) ~= nil
        local role_cv_selected = selected_cv ~= "" and selected_role ~= ""
        local has_process_suggestion = process_suggestion ~= ""

        -- 检查块标按钮点击
        if is_point_in_rect(mouse_x, mouse_y, block_mark_button) and mouse_cap == 1 and style_module.last_mouse_state == 0 then
          -- 只有同时满足三个条件才执行打标操作，否则显示相应提示信息
          if has_selected_item and role_cv_selected and has_process_suggestion then
            -- 检查按钮是否已在动画中，避免重复触发
            if not style_module.is_button_animating(block_mark_button) then
              -- 开始按钮动画，动画完成后执行业务逻辑
              style_module.start_button_animation(block_mark_button, function()
                mark_error()
              end)
            end
          elseif not has_selected_item then
          elseif not role_cv_selected then
          elseif not has_process_suggestion then
          end
        end

        -- 检查区标按钮点击
        if is_point_in_rect(mouse_x, mouse_y, region_mark_button) and mouse_cap == 1 and style_module.last_mouse_state == 0 then
          -- 构建正确的state对象
          local state = {
            selected_role = selected_role,
            selected_cv = selected_cv,
            process_suggestion = process_suggestion,
            selected_texts = selected_texts  -- 添加选中的文本内容
          }
          
          -- 检查按钮是否启用
          local role_cv_selected = state.selected_role and state.selected_role ~= "" and state.selected_cv and state.selected_cv ~= ""
          local has_process_suggestion = state.process_suggestion and state.process_suggestion ~= ""
          local region_mark_button_enabled = role_cv_selected and has_process_suggestion
          
          if region_mark_button_enabled then
            -- 检查按钮是否已在动画中，避免重复触发
            if not style_module.is_button_animating(region_mark_button) then
              -- 开始按钮动画，动画完成后执行业务逻辑
              style_module.start_button_animation(region_mark_button, function()
              local success_msg, formatted_time, relative_pos = button_module.handle_region_mark_button_click(state)
              debug_message = success_msg
              
              -- 判断区标是否真正成功
              local is_region_mark_successful = success_msg and success_msg:find("错误") == nil and formatted_time and formatted_time ~= ""
              
              -- 如果成功添加区标，将相对时间写入错误时间字段和选择列表
              if is_region_mark_successful then
                error_time = formatted_time
                marked_relative_time = formatted_time  -- 设置到选择列表显示
              
              -- 从region名称中提取集数并设置到集数输入框
              if relative_pos then
                -- 获取当前光标位置
                local cursor_pos = r.GetCursorPosition()
                local region_count = r.CountProjectMarkers(0)
                
                -- 查找光标所在的region
                for i = 0, region_count - 1 do
                  local retval, isrgn, pos, rgnend, name, markrgnindexnumber, color = r.EnumProjectMarkers3(0, i)
                  if retval and isrgn and cursor_pos >= pos and cursor_pos <= rgnend then
                    -- 从region名称中提取数字
                    local extracted_number = name:match("%d+")
                    if extracted_number then
                      episode_number = extracted_number
                    end
                    break
                  end
                end
              end
                

                
                -- 显示成功提示弹窗
                local popup_message = "区标添加成功！\n\n"
                popup_message = popup_message .. "角色: " .. state.selected_role .. "\n"
                popup_message = popup_message .. "CV: " .. state.selected_cv .. "\n"
                popup_message = popup_message .. "错误时间: " .. formatted_time .. "\n"
                popup_message = popup_message .. "处理建议: " .. state.process_suggestion
                if episode_number and episode_number ~= "" then
                  popup_message = popup_message .. "\n集数: " .. episode_number
                end
                
                r.MB(popup_message, "区标成功", 0)
              end
              end)
            end
          else
            debug_message = "区标按钮未启用：请确保已选择角色、CV并填写处理建议"
          end
        end

        -- 在handle_mouse函数中的按钮点击处理部分添加对轨按钮和章节按钮的处理逻辑
        -- 检查对轨按钮点击
        if is_point_in_rect(mouse_x, mouse_y, track_align_button) then
          -- 切换对轨功能的启用状态
          is_track_align_enabled = not is_track_align_enabled
        end
        
        -- 检查章节按钮点击
        if is_point_in_rect(mouse_x, mouse_y, chapter_button) then
          debug_message = "点击了章节按钮"

          -- 切换章节列表的显示状态
          is_chapter_list_visible = not is_chapter_list_visible

          -- 设置动画初始参数
          chapter_list_animation.in_progress = true
          chapter_list_animation.start_time = r.time_precise()

          -- 保存内容区域的当前状态
          chapter_list_animation.content_start_x = content_area.x
          chapter_list_animation.content_start_w = content_area.w

          if is_chapter_list_visible then
            -- 显示动画：从左侧滑入
            chapter_list_animation.start_x = chapter_list_animation.hide_position
            chapter_list_animation.target_x = chapter_list_animation.default_x
            -- 立即更新位置以开始动画
            chapter_list.x = chapter_list_animation.hide_position

            -- 计算内容区域的目标位置和宽度
            chapter_list_animation.content_target_x = chapter_list_animation.default_x + chapter_list.w + 10
            chapter_list_animation.content_target_w = window_w - 14 - chapter_list.w - 10  -- 修改为与新的宽度计算一致
          else
            -- 隐藏动画：向左侧滑出
            chapter_list_animation.start_x = chapter_list.x
            chapter_list_animation.target_x = chapter_list_animation.hide_position

            -- 计算内容区域的目标位置和宽度
            chapter_list_animation.content_target_x = 20
            chapter_list_animation.content_target_w = window_w - 24  -- 修改为与新的宽度计算一致
          end

          -- 提高动画期间的帧率，确保更流畅的体验
          frame_delay = 1/60  -- 临时将帧率提高到60fps

          force_redraw = true  -- 强制重绘界面

          -- 更新上次点击时间
          last_click_time = current_click_time
          return
        end

        -- 检查其他按钮点击
      end
    else
      -- 鼠标释放时，重置所有拖动状态
      is_dragging_sentence_scrollbar = false
      is_dragging_cv_role_scrollbar = false
      is_dragging_chapter_scrollbar = false
      is_dragging_selection_scrollbar = false
      is_mouse_down = false
      
      -- 结束内容区域拖拽调整大小
      style_module.end_content_resize()
      
      -- 重置长按速率按钮状态
      rate_button_hold_state.minus_pressed = false
      rate_button_hold_state.plus_pressed = false
    end
    
    -- 检查长按状态：如果鼠标仍在按钮上且按下，保持长按状态
    if is_mouse_down then
      local mouse_x, mouse_y = gfx.mouse_x, gfx.mouse_y
      
      -- 检查是否仍在减速按钮上
      if rate_button_hold_state.minus_pressed and not is_point_in_rect(mouse_x, mouse_y, rate_minus_button) then
        rate_button_hold_state.minus_pressed = false
      end
      
      -- 检查是否仍在加速按钮上
      if rate_button_hold_state.plus_pressed and not is_point_in_rect(mouse_x, mouse_y, rate_plus_button) then
        rate_button_hold_state.plus_pressed = false
      end
    end

    -- 处理滚动条拖动
    if is_dragging_sentence_scrollbar then
      -- 使用style_module处理滚动条拖动
      local visible_items = math.floor(content_area.h / (content_font_size + 2))
      sentence_scroll_pos = style_module.handle_scrollbar_drag(sentence_scrollbar, mouse_y, #sentences, visible_items)
      target_sentence_scroll_pos = sentence_scroll_pos
      cached_total_content_height = nil  -- 滚动时重新计算内容高度
    end

    if is_dragging_cv_role_scrollbar then
      -- 使用辅助函数获取CV角色分类数据，确保无论是否交换位置都保持一致的显示
      local cv_categories, cv_order = get_cv_role_categories(cv_role_pairs, is_cv_role_reversed)

      -- 计算所有分类项的总数（包括CV分类标题）
      local total_items = 0
      for _, roles in pairs(cv_categories) do
          -- 每个CV类别标题占一行，加上所有角色
          total_items = total_items + 1 + #roles
      end

      -- 使用基于字体大小的动态行高
      local cv_role_line_height = math.max(20, font_size + 2)
      local visible_items = math.floor((cv_role_list.h - 10) / cv_role_line_height)

      -- 计算滚动位置
      cv_role_scroll_pos = style_module.handle_scrollbar_drag(cv_role_scrollbar, mouse_y, total_items, visible_items)
      target_cv_role_scroll_pos = cv_role_scroll_pos
    end

    if is_dragging_chapter_scrollbar then
      local line_height = font_size + 5  -- 章节标题行高
      local visible_items = math.floor((chapter_list.h - 10) / line_height)
      chapter_scroll_pos = style_module.handle_scrollbar_drag(chapter_scrollbar, mouse_y, #chapters, visible_items)
    end

    if is_dragging_selection_scrollbar then
      -- 处理选择框内容滚动
      if ui.selection_scrollbar then
        -- 计算鼠标在滚动条上的相对位置
        local scroll_ratio = (mouse_y - ui.selection_scrollbar.y) / ui.selection_scrollbar.h
        -- 使用实际内容高度计算最大滚动位置
        local max_scroll = math.max(0, ui.selection_scrollbar.total_height - selection_area.h)
        -- 应用滚动位置
        selection_scroll_pos = math.floor(scroll_ratio * max_scroll)
        selection_scroll_pos = math.max(0, math.min(max_scroll, selection_scroll_pos))
        target_selection_scroll_pos = selection_scroll_pos
      end
    end

    -- 处理内容区域拖拽调整大小
    if style_module.handle_content_resize(mouse_y) then
      -- 内容区域高度发生变化，需要重新初始化UI元素
      ui = style_module.init_ui_elements(gfx.w, gfx.h)
      -- 更新相关区域的引用
      content_area = ui.content_area
      cv_role_list = ui.cv_role_list
      selection_area = ui.selection_area
      chapter_list = ui.chapter_list
      error_input = ui.error_input
      correct_input = ui.correct_input
      episode_input = ui.episode_input
      suggestion_input = ui.suggestion_input
      play_button = ui.play_button
      block_mark_button = ui.block_mark_button
      region_mark_button = ui.region_mark_button
      excel_button = ui.excel_button
      open_csv_button = ui.open_csv_button
      au_button = ui.au_button
      rate_minus_button = ui.rate_minus_button
      rate_display_area = ui.rate_display_area
      rate_plus_button = ui.rate_plus_button
      rate_reset_button = ui.rate_reset_button
      document_button = ui.document_button
      clipboard_button = ui.clipboard_button
      font_decrease_button = ui.font_decrease_button
      font_increase_button = ui.font_increase_button
      chapter_button = ui.chapter_button
      
      -- 清除缓存的句子高度，因为内容区域大小变化了
      cached_sentence_heights = {}
      cached_total_content_height = nil
      
      force_redraw = true
    end
  end)

  if not status then
    -- debug_message = "鼠标处理错误: " .. tostring(err)
  end
end

-- 标记错误
-- 添加全局变量用于存储内部时间信息
marked_relative_time = ""
local marked_relative_pos = 0

function mark_error()
  local status, err = pcall(function()
    -- 二次检查是否选中了音频块和角色CV及处理建议，确保不会出现无法打标的情况
    local has_selected_item = r.GetSelectedMediaItem(0, 0) ~= nil
    local role_cv_selected = selected_cv ~= "" and selected_role ~= ""
    local has_process_suggestion = process_suggestion ~= ""

    -- 只有同时满足三个条件才能执行打标操作
    if not has_selected_item then
      -- debug_message = "请先选择一个音频块!"
      return false
    end

    if not role_cv_selected then
      -- debug_message = "请先选择角色和CV!"
      return false
    end

    if not has_process_suggestion then
      -- debug_message = "请先填写处理建议!"
      return false
    end

    -- 调用按钮模块的打标函数
    -- 界面上已经显示了交换后的结果，这里不需要再次交换
    local params = {
      process_suggestion = process_suggestion,
      selected_role = selected_role,
      selected_cv = selected_cv,
      selected_texts = selected_texts  -- 添加选中的文本内容
    }

    local message, formatted_time, relative_pos = button_module.handle_block_mark_button_click(params)

    -- 判断标记是否真正成功
    -- 如果message包含错误信息或formatted_time为空，则表示标记失败
    local is_mark_successful = message:find("错误") == nil and formatted_time ~= ""

    -- 保存内部时间信息到全局变量（只有在成功时）
    if is_mark_successful then
      marked_relative_time = formatted_time
      marked_relative_pos = relative_pos
    else
      -- 标记失败，仅显示错误信息
      -- debug_message = message
      return false
    end

    -- debug_message = message

    -- 标记成功时显示弹窗，如果有警告信息则同时显示在控制台
    if message and message ~= "" then
      -- 消息处理
    end

    -- 只有在真正成功标记时才显示成功提示
    if is_mark_successful then

      
      local popup_message = "标记添加成功！\n\n"
      popup_message = popup_message .. "角色: " .. selected_role .. "\n"
      popup_message = popup_message .. "CV: " .. selected_cv .. "\n"
      popup_message = popup_message .. "错误时间: " .. formatted_time .. "\n"
      popup_message = popup_message .. "处理建议: " .. process_suggestion

      r.MB(popup_message, "标记成功", 0)
    end

    return is_mark_successful
  end)

  if not status then
    -- debug_message = "标记错误失败: " .. tostring(err)
    return false
  end
end

-- 刷新界面
-- 更新滚动位置
function update_scroll_positions()
  -- 如果禁用平滑滚动，直接设置为目标位置
  if use_smooth_scroll == false then
    sentence_scroll_pos = target_sentence_scroll_pos
    cv_role_scroll_pos = target_cv_role_scroll_pos
    selection_scroll_pos = target_selection_scroll_pos
    return
  end

  -- 使用缓动函数进行平滑滚动
  -- 更新句子列表滚动位置
  if sentence_scroll_pos ~= target_sentence_scroll_pos then
    local diff = target_sentence_scroll_pos - sentence_scroll_pos
    local progress = math.min(1, math.abs(diff) * scroll_speed)
    local eased_progress = ease.out_quad(progress)  -- 使用本地定义的函数
    sentence_scroll_pos = sentence_scroll_pos + diff * eased_progress

    -- 如果差距很小，直接设置为目标位置
    if math.abs(diff) < 0.1 then
      sentence_scroll_pos = target_sentence_scroll_pos
    end
  end

  -- 更新CV角色列表滚动位置
  if cv_role_scroll_pos ~= target_cv_role_scroll_pos then
    local diff = target_cv_role_scroll_pos - cv_role_scroll_pos
    local progress = math.min(1, math.abs(diff) * scroll_speed)
    local eased_progress = ease.out_quad(progress)  -- 使用本地定义的函数
    cv_role_scroll_pos = cv_role_scroll_pos + diff * eased_progress

    -- 如果差距很小，直接设置为目标位置
    if math.abs(diff) < 0.1 then
      cv_role_scroll_pos = target_cv_role_scroll_pos
    end
  end

  -- 更新选择区域滚动位置
  if selection_scroll_pos ~= target_selection_scroll_pos then
    local diff = target_selection_scroll_pos - selection_scroll_pos
    local progress = math.min(1, math.abs(diff) * scroll_speed)
    local eased_progress = ease.out_quad(progress)  -- 使用本地定义的函数
    selection_scroll_pos = selection_scroll_pos + diff * eased_progress

    -- 如果差距很小，直接设置为目标位置
    if math.abs(diff) < 0.1 then
      selection_scroll_pos = target_selection_scroll_pos
    end
  end
end

-- 添加REAPER项目状态监听变量
local last_selected_item = nil
local last_selected_item_name = ""
local item_selection_change_cooldown = 0.1  -- 选择变更冷却时间，防止频繁更新
local last_selection_check_time = 0

-- 检查项目选择变更的函数
function check_selection_changed()
  local current_time = r.time_precise()

  -- 防止频繁检查，设置冷却时间
  if current_time - last_selection_check_time < item_selection_change_cooldown then
    return false
  end
  last_selection_check_time = current_time

  -- 获取当前选中的项目
  local selected_item = r.GetSelectedMediaItem(0, 0)

  -- 判断选择是否变更
  if selected_item ~= last_selected_item then
    last_selected_item = selected_item

    -- 如果有新选中的项目，检查是否需要更新集数
    if selected_item then
      local take = r.GetActiveTake(selected_item)
      if take then
        local item_name = r.GetTakeName(take)
        -- 检查项目名称是否变更
        if item_name ~= last_selected_item_name then
          last_selected_item_name = item_name
          return true
        end
      end
    else
      -- 清除上次记录的项目名称
      last_selected_item_name = ""
    end
  end

  return false
end

-- 将此函数移到文件前面，放在其他函数定义之后，main函数之前
-- 获取当前选择音频块集数的函数
function get_episode_from_selected_item()
  local number, message = utils_module.get_episode_from_selected_item()
  if number then
    -- debug_message = message
  else
    -- debug_message = message or "未能获取集数"
  end
  return number, message
end

-- 在global variables部分添加窗口拖动状态检测相关变量
local is_resizing = false           -- 是否正在调整窗口大小
-- 移除未使用的变量
-- local resize_debounce_time = 0.1    -- 拖动防抖时间(秒)
-- local last_resize_time = 0          -- 上次窗口大小调整时间
-- local resize_end_timer = 0          -- 窗口调整结束计时器
-- local minimal_ui_during_resize = true -- 调整窗口时使用简化界面

-- 检测窗口大小变化并更新UI元素
function check_window_resize()
  -- 由于窗口大小已经锁定，此函数不再响应窗口大小变化
  return false
end



function refresh_ui()
  local status, err = pcall(function()
    -- 检测窗口大小变化并调整UI元素
    check_window_resize()

    -- 获取关键状态变化
    local play_state = r.GetPlayState()
    -- 移除未使用的变量
    -- local play_state_changed = (is_playing ~= (play_state == 1))
    is_playing = (play_state == 1)

    -- 只在播放状态下更新播放速率
    -- 移除未使用的变量
    if is_playing then
      local new_rate = utils_module.get_playrate()
      -- local rate_changed = (new_rate ~= current_playrate)
      current_playrate = new_rate
    end

    -- 更新滚动位置
    update_scroll_positions()
    
    -- 处理长按速率按钮的持续调整
    local current_time = r.time_precise()
    if (rate_button_hold_state.minus_pressed or rate_button_hold_state.plus_pressed) and 
       (current_time - rate_button_hold_state.last_adjust_time) >= rate_button_hold_state.adjust_interval then
      
      if rate_button_hold_state.minus_pressed then
        -- 持续减小播放速率
        local message, new_rate = button_module.handle_rate_minus_button_click()
        current_playrate = new_rate
      elseif rate_button_hold_state.plus_pressed then
        -- 持续增加播放速率
        local message, new_rate = button_module.handle_rate_plus_button_click()
        current_playrate = new_rate
      end
      
      -- 更新最后调整时间
      rate_button_hold_state.last_adjust_time = current_time
      force_redraw = true  -- 强制重绘以更新速率显示
    end

    -- 检查选择是否变更，如果变更则自动获取集数
    local selection_changed = check_selection_changed()
    if selection_changed then
      -- 移除未使用的变量
      local auto_episode = utils_module.get_episode_from_selected_item()
      if auto_episode and (episode_number == "" or auto_episode ~= episode_number) then
        episode_number = auto_episode
        -- debug_message = "自动更新集数: " .. episode_number
        force_redraw = true  -- 强制重绘界面以立即显示新集数
      end
    end

    -- 检查动画状态并恢复正常帧率
    if not chapter_list_animation.in_progress and frame_delay ~= 1/15 then
      -- 如果动画已结束且帧率不是标准值，则恢复到正常帧率
      frame_delay = 1/15  -- 恢复到标准帧率
    end

    -- 更新按钮状态，跟踪鼠标悬停和按下状态
    local buttons = {
      play_button,
      block_mark_button,
      region_mark_button,
      excel_button,
      open_csv_button,  -- 添加"开"按钮
      au_button,        -- 添加"AU"按钮
      ui.region_name_button, -- 添加"区名"按钮
      ui.file_name_button,   -- 添加"文名"按钮
      ui.track_color_button, -- 添加"轨色"按钮
      ui.track_split_button, -- 添加"分轨"按钮
      track_align_button,    -- 添加"对轨"按钮
      chapter_button,        -- 添加"章"按钮
      rate_minus_button,
      rate_plus_button,
      rate_reset_button,
      document_button,
      clipboard_button,
      font_decrease_button,
      font_increase_button
    }

    -- 获取当前鼠标位置和按键状态
    local mouse_x, mouse_y = gfx.mouse_x, gfx.mouse_y
    local mouse_cap = gfx.mouse_cap

    -- 先调用style_module更新按钮状态，实现凹陷效果
    local button_state_changed = style_module.update_button_states(buttons, mouse_x, mouse_y, mouse_cap)
    
    -- 更新按钮动画状态
    style_module.update_button_animations()

    -- 如果按钮状态有变化，强制立即重绘界面，提高按钮响应灵敏度
    if button_state_changed then
      force_redraw = true
    end

    -- 然后处理鼠标事件（在按钮状态更新之后处理，确保凹陷效果先显示）
    handle_mouse()



    -- 保存当前鼠标状态供下一帧使用
    style_module.last_mouse_state = mouse_cap

    -- 重绘界面
    draw_ui()

    -- 不再在这里检查ESC键，已经在主循环中处理

    return true
  end)

  if not status then
    -- debug_message = "刷新界面错误: " .. tostring(err)
    return false
  end

  -- 确保返回的是pcall内部函数的结果
  if type(err) == "boolean" then
    return err -- 如果是布尔值，直接返回
  else
    return true -- 如果不是布尔值，默认返回true继续循环
  end
end

-- 主函数优化
function main()
  -- 初始化界面
  if not init() then
    return
  end

  -- 主循环
  local continue = true
  local last_frame_time = 0

  local function loop()
    local char = gfx.getchar()

    -- 处理搜索相关键盘输入
    if search_input_active then
      if char == 13 then  -- 回车键
        -- 执行搜索
        perform_search(search_text)
        search_input_active = false
      elseif char == 27 then  -- ESC键
        -- 取消搜索输入
        search_input_active = false
      elseif char == 8 then  -- 退格键
        if #search_text > 0 then
          search_text = search_text:sub(1, -2)
          -- 实时搜索
          perform_search(search_text)
        end
      elseif char > 31 and char < 127 then  -- 可打印ASCII字符
        search_text = search_text .. string.char(char)
        -- 实时搜索
        perform_search(search_text)
      end
      return  -- 搜索输入模式下不处理其他键盘事件
    end
    
    -- 处理搜索快捷键
    if char == 6 then  -- Ctrl+F
      search_input_active = true
      return
    elseif char == 7 then  -- Ctrl+G (下一个搜索结果)
      goto_next_search_result()
      return
    elseif char == 16 then  -- Ctrl+P (上一个搜索结果)
      goto_prev_search_result()
      return
    end

    -- 检查是否需要关闭（ESC键或窗口关闭）
    if not continue or char == 27 or char == -1 then  -- 27是ESC键，-1是窗口关闭
      -- 保存当前画本内容
      if modules.data_persist_module and #sentences > 0 then
        -- 收集当前滚动位置
        local scroll_positions = {
          sentence_scroll_pos = sentence_scroll_pos,
          content_scroll_y = content_scroll_y,
          chapter_scroll_pos = chapter_scroll_pos,
          cv_role_scroll_pos = cv_role_scroll_pos
        }

        modules.data_persist_module.save_storyboard(sentences, cv_role_pairs, scroll_positions)
      end

      -- 退出前恢复播放速率为1.0
      if utils_module and utils_module.reset_playrate then
        utils_module.reset_playrate()
      end

      -- 关闭图形界面
      gfx.quit()
      -- 直接退出，不再调用r.defer()
      return
    end

    local current_time = r.time_precise()

    -- 检查鼠标状态和滚轮值，判断是否需要立即更新UI
    local force_immediate_update = false

    -- 检测鼠标按下或释放事件
    local mouse_cap = gfx.mouse_cap
    local mouse_state_changed = (mouse_cap ~= style_module.last_mouse_state)

    -- 更新鼠标右键状态（现在只用于记录状态，不再直接修改is_ctrl_down）
    -- is_ctrl_down = (mouse_cap & 2) == 2  -- 使用值2，代表右键
    -- 检测右键是否按下，但不修改is_ctrl_down
    -- local right_button_down = (mouse_cap & 2) == 2

    -- 检测鼠标滚轮事件
    local mouse_wheel_event = (gfx.mouse_wheel ~= 0)

    -- 当检测到鼠标状态变化或滚轮事件时，强制立即更新UI
    if mouse_state_changed or mouse_wheel_event then
      force_immediate_update = true
      -- 如果内容区域发生变化，更新当前章节
      if is_point_in_rect(gfx.mouse_x, gfx.mouse_y, content_area) then
        update_current_chapter()
      end
    end

    -- 检查窗口大小是否变化
    local window_resized = check_window_resize()
    if window_resized then
      -- 重置滚动位置
      if content_scroll_y > 0 then
        content_scroll_y = 0
      end
      force_immediate_update = true
    end

    -- 处理字体缩放异步计算
    if font_scaling_in_progress then
      if current_time - last_font_scale_process_time >= font_scale_process_interval then
        last_font_scale_process_time = current_time

        -- 优化：使用更高效的批处理策略，动态调整批量大小
        local start_process_time = current_time
        local max_process_time = 0.015 -- 提高到15毫秒，但仍保持界面响应性
        local adaptive_batch_size = font_scale_batch_size
        local processed = 0
        local visible_processed = false

        -- 确定处理优先级：可见区域 > 可见区域附近 > 其他区域
        local start_idx = math.max(1, sentence_scroll_pos)
        local visible_items_count = math.floor((content_area.h - 10) / (content_font_size + 5))
        local end_idx = math.min(#sentences, sentence_scroll_pos + visible_items_count)

        -- 第一阶段：优先处理可见区域内的句子
        if not visible_processed then
          for i = start_idx, end_idx do
            if not cached_sentence_heights[i] then
              gfx.setfont(1, font_name, content_font_size)
              cached_sentence_heights[i] = text_utils.calculate_text_height(
                sentences[i], content_area.w - 20, gfx)

              processed = processed + 1

              -- 性能优化：检查是否有足够时间继续处理
              if processed >= adaptive_batch_size or (r.time_precise() - start_process_time > max_process_time) then
                break
              end
            end
          end

          -- 检查可见区域是否处理完毕
          visible_processed = true
          for i = start_idx, end_idx do
            if not cached_sentence_heights[i] then
              visible_processed = false
              break
            end
          end

          -- 如果已处理了一些项目但未完成可见区域，则直接返回，下一帧继续
          if processed > 0 and not visible_processed then
            -- 强制重绘显示进度
            force_redraw = true
            -- 调整后续帧的批量大小
            if (r.time_precise() - start_process_time) < max_process_time * 0.5 then
              -- 处理很快，增加批量大小
              font_scale_batch_size = math.min(50, font_scale_batch_size + 5)
            elseif (r.time_precise() - start_process_time) > max_process_time * 0.9 then
              -- 处理较慢，减少批量大小
              font_scale_batch_size = math.max(5, font_scale_batch_size - 2)
            end
            return -- 提前返回，避免过多处理导致卡顿
          end
        end

        -- 第二阶段：处理可见区域外的句子，从上次处理的位置继续
        if visible_processed and processed < adaptive_batch_size and (r.time_precise() - start_process_time) < max_process_time then
          -- 找出未处理的句子
          local unprocessed_indices = {}
          for i = 1, #sentences do
            if not cached_sentence_heights[i] and (i < start_idx or i > end_idx) then
              table.insert(unprocessed_indices, i)
            end
          end

          -- 如果没有未处理的句子，标记完成
          if #unprocessed_indices == 0 then
            font_scaling_in_progress = false
            cached_total_content_height = nil -- 强制重新计算总高度
            force_redraw = true
          else
            -- 优先处理最接近可见区域的句子
            table.sort(unprocessed_indices, function(a, b)
              local dist_a = math.min(math.abs(a - start_idx), math.abs(a - end_idx))
              local dist_b = math.min(math.abs(b - start_idx), math.abs(b - end_idx))
              return dist_a < dist_b
            end)

            -- 处理尽可能多的句子，但不超过批处理限制和时间限制
            for i = 1, math.min(adaptive_batch_size - processed, #unprocessed_indices) do
              local idx = unprocessed_indices[i]
              gfx.setfont(1, font_name, content_font_size)
              cached_sentence_heights[idx] = text_utils.calculate_text_height(
                sentences[idx], content_area.w - 20, gfx)

              processed = processed + 1

              -- 检查是否超过了最大处理时间
              if (r.time_precise() - start_process_time) > max_process_time then
                break
              end
            end

            -- 检查是否处理完所有句子
            local all_processed = true
            for i = 1, #sentences do
              if not cached_sentence_heights[i] then
                all_processed = false
                break
              end
            end

            if all_processed then
              font_scaling_in_progress = false
              cached_total_content_height = nil -- 强制重新计算总高度
              force_redraw = true
            end
          end
        end

        -- 如果这一帧什么都没处理，标记完成
        if processed == 0 then
          font_scaling_in_progress = false
          cached_total_content_height = nil -- 强制重新计算总高度
          force_redraw = true
        else
          -- 强制重绘以更新进度
          force_redraw = true
        end
      end
    end

    -- 限制帧率，但鼠标状态变化时允许立即响应
    if force_immediate_update or (current_time - last_frame_time >= frame_delay) then
      last_frame_time = current_time

      -- 获取当前鼠标位置
      local mouse_x, mouse_y = gfx.mouse_x, gfx.mouse_y
      local mouse_moved = (mouse_x ~= last_mouse_x or mouse_y ~= last_mouse_y)

      -- 检查是否要跳过此帧渲染
      if not mouse_moved and not force_immediate_update and skip_render_counter < MAX_SKIP_FRAMES and #sentences > 0 and not font_scaling_in_progress and not is_resizing then
        -- 仍然处理鼠标事件
        handle_mouse()
        skip_render_counter = skip_render_counter + 1
      else
        -- 需要渲染，重置计数器
        skip_render_counter = 0
        last_mouse_x, last_mouse_y = mouse_x, mouse_y

        -- 处理回车点击事件
        if is_enter_clicked and text_timer <= 0 then
          is_enter_clicked = false
          process_enter_click()
        end

        -- 窗口拖动期间使用简化绘制，提高性能
        if is_resizing then
          -- 从style_module.colors.bg中获取RGB值并转换为整数
          local bg_color = style_module.colors.bg
          local r, g, b = bg_color.r, bg_color.g, bg_color.b
          -- 将RGB值转换为整数 (0-255范围)
          local color_int = math.floor(r * 255) * 65536 + math.floor(g * 255) * 256 + math.floor(b * 255)
          gfx.clear = color_int
          -- 简化界面绘制
        else
          -- 正常绘制完整界面
          continue = refresh_ui()
        end

        -- 立即更新界面，确保按钮状态变化立即可见
        gfx.update()
      end

      -- 如果是强制更新的帧，重新存储鼠标状态
      if force_immediate_update then
        style_module.last_mouse_state = mouse_cap
        gfx.mouse_wheel = 0  -- 重置滚轮值
      end
    end

    r.defer(loop)
  end

  -- 启动循环
  r.defer(loop)
end



-- 运行主函数
safe_call(main)

-- 在当前章节范围内查找指定CV和角色的位置
function find_cv_role_position_in_current_chapter(role_name, cv_name)
  if cv_name == "" or role_name == "" or #cv_role_pairs == 0 or not current_chapter_idx then
    return -1
  end
  
  -- 获取当前章节的句子范围
  local current_chapter_start = 1
  local current_chapter_end = #sentences
  
  if chapter_module and chapter_module.chapters and #chapter_module.chapters > 0 then
    local chapters = chapter_module.chapters
    
    -- 获取当前章节的开始位置
    if current_chapter_idx <= #chapters then
      current_chapter_start = chapters[current_chapter_idx].sentence_idx
    end
    
    -- 获取当前章节的结束位置
    if current_chapter_idx < #chapters then
      current_chapter_end = chapters[current_chapter_idx + 1].sentence_idx - 1
    end
  end
  
  -- 重新提取当前章节范围内的CV角色对
  local chapter_sentences = {}
  for i = current_chapter_start, current_chapter_end do
    if sentences[i] then
      table.insert(chapter_sentences, sentences[i])
    end
  end
  
  -- 从当前章节的句子中提取CV角色对
  local chapter_cv_role_pairs = {}
  if text_utils and text_utils.extract_cv_role_pairs then
    chapter_cv_role_pairs = text_utils.extract_cv_role_pairs(chapter_sentences, is_cv_role_reversed)
  end
  
  -- 如果当前章节没有CV角色对，返回-1
  if #chapter_cv_role_pairs == 0 then
    return -1
  end
  
  -- 在当前章节的CV角色对中查找匹配项
  if text_utils and text_utils.find_cv_role_index then
    local index = text_utils.find_cv_role_index(chapter_cv_role_pairs, role_name, cv_name)
    if index > 0 then
      -- 找到确切匹配的项，计算在CV分类列表中的位置
      
      -- 构建当前章节的CV分类数据结构
      local cv_categories = {}
      local cv_order = {}
      
      for i, pair in ipairs(chapter_cv_role_pairs) do
        local current_cv = pair.cv
        if not cv_categories[current_cv] then
          cv_categories[current_cv] = {}
          table.insert(cv_order, current_cv)
        end
        table.insert(cv_categories[current_cv], {role = pair.role, index = i})
      end
      
      -- 计算位置
      local position = 0
      for _, current_cv in ipairs(cv_order) do
        position = position + 1  -- CV标题行
        
        if current_cv == cv_name then
          for _, role_info in ipairs(cv_categories[current_cv]) do
            if role_info.index == index then
              return position
            end
            position = position + 1
          end
          
          -- 如果找不到确切的角色，至少指向CV标题
          return position - #cv_categories[current_cv]
        else
          position = position + #cv_categories[current_cv]  -- 跳过该CV下的所有角色
        end
      end
    end
  end
  
  return -1
end

-- 查找指定CV和角色在列表中的位置（修改参数顺序为role_name, cv_name）
function find_cv_role_position(role_name, cv_name)
  if cv_name == "" or role_name == "" or #cv_role_pairs == 0 then
    return -1
  end

  -- 尝试使用text_utils的功能查找
  if text_utils and text_utils.find_cv_role_index then
    local index = text_utils.find_cv_role_index(cv_role_pairs, role_name, cv_name)
    if index > 0 then
      -- 找到确切匹配的项
      -- 现在需要转换为列表位置（考虑分类的显示方式）

      -- 构建CV分类数据结构
      local cv_categories = {}  -- 用于存储按CV分类的角色
      local cv_order = {}       -- 用于记录CV的顺序

      -- 遍历所有CV角色对，按CV进行分类
      for i, pair in ipairs(cv_role_pairs) do
        local current_cv = pair.cv
        if not cv_categories[current_cv] then
          cv_categories[current_cv] = {}
          table.insert(cv_order, current_cv)
        end
        table.insert(cv_categories[current_cv], {role = pair.role, index = i})
      end

      -- 计算位置
      local position = 0
      for _, current_cv in ipairs(cv_order) do
        position = position + 1  -- CV标题行

        if current_cv == cv_name then
          for _, role_info in ipairs(cv_categories[current_cv]) do
            if role_info.index == index then
              return position
            end
            position = position + 1
          end

          -- 如果找不到确切的角色，至少指向CV标题
          return position - #cv_categories[current_cv]
        else
          position = position + #cv_categories[current_cv]  -- 跳过该CV下的所有角色
        end
      end
    end
  end

  -- 回退到原始实现
  -- 构建CV分类数据结构
  local cv_categories = {}  -- 用于存储按CV分类的角色
  local cv_order = {}       -- 用于记录CV的顺序

  -- 遍历所有CV角色对，按CV进行分类
  for i, pair in ipairs(cv_role_pairs) do
    local current_cv = pair.cv
    if not cv_categories[current_cv] then
      cv_categories[current_cv] = {}
      table.insert(cv_order, current_cv)
    end
    table.insert(cv_categories[current_cv], {role = pair.role, index = i})
  end

  -- 计算目标位置
  local position = 0

  for _, current_cv in ipairs(cv_order) do
    -- 每个CV类别占一行（标题）
    position = position + 1

    -- 如果是目标CV，检查角色
    if current_cv == cv_name then
      local roles = cv_categories[current_cv]

      -- 遍历该CV下的所有角色
      for _, role_info in ipairs(roles) do
        if role_info.role == role_name then
          -- 找到了目标角色，返回其位置
          return position
        end
        position = position + 1
      end

      -- 如果没有找到指定角色但找到了CV，返回CV的位置
      return position - #roles
    else
      -- 如果不是目标CV，跳过其所有角色
      position = position + #cv_categories[current_cv]
    end
  end

  return -1  -- 未找到
end

-- 跳转到指定角色CV位置（修改参数顺序为role_name, cv_name）
function scroll_to_cv_role(role_name, cv_name)
  -- 检查是否为章节内容，如果是则不执行滚动
  if cv_name and cv_name ~= "" then
    -- 检查是否包含章节标记或为章节标题格式
    if cv_name:match("%[CHAPTER%](.-)%[/CHAPTER%]") or
       cv_name:match("^第[%d一二三四五六七八九十百千]+[章节集话回]") or
       cv_name:match("^章节[%d一二三四五六七八九十百千]+") then
      return  -- 直接返回，不执行滚动
    end
  end
  
  -- 只在当前章节范围内查找角色CV，避免跳转到其他章节
  local position = find_cv_role_position_in_current_chapter(role_name, cv_name)
  
  -- 如果当前章节找不到，直接返回，不跳转到其他章节
  if position < 0 then return end

  -- 计算可见项目数
  local cv_role_line_height = math.max(20, font_size + 2)
  local visible_items = math.floor((cv_role_list.h - 10) / cv_role_line_height)

  -- 调整滚动位置，使目标项在视图中间
  local target_pos = math.max(0, position - math.floor(visible_items / 2))

  -- 构建CV分类数据结构来计算总项目数
  local cv_categories = {}  -- 用于存储按CV分类的角色
  local cv_order = {}       -- 用于记录CV的顺序

  -- 遍历所有CV角色对，按CV进行分类
  for i, pair in ipairs(cv_role_pairs) do
    local current_cv = pair.cv
    if not cv_categories[current_cv] then
      cv_categories[current_cv] = {}
      table.insert(cv_order, current_cv)
    end
    table.insert(cv_categories[current_cv], {role = pair.role, index = i})
  end

  -- 计算总项目数（CV类别标题 + 每个CV下的所有角色）
  local total_items = 0
  for _, current_cv in ipairs(cv_order) do
    -- 每个CV类别占一行（标题）
    total_items = total_items + 1
    -- 加上该CV下的所有角色数量
    total_items = total_items + #cv_categories[current_cv]
  end

  -- 确保不超过最大滚动位置
  local max_scroll = math.max(0, total_items - visible_items)
  target_pos = math.min(target_pos, max_scroll)

  -- 设置目标滚动位置
  target_cv_role_scroll_pos = target_pos
  cv_role_scroll_pos = target_pos  -- 立即跳转，也可以不设置这个让它平滑滚动

  -- 强制重绘界面
  force_redraw = true
end

-- 处理CV角色位置切换复选框点击
function handle_cv_role_checkbox_click()
  -- 切换复选框状态
  is_cv_role_reversed = not is_cv_role_reversed

  -- 重新提取角色和CV列表
  if sentences and #sentences > 0 then
    -- 清除缓存，确保重新提取角色和CV列表
    if utils_module and utils_module.cache_clear_pattern then
      utils_module.cache_clear_pattern("cv_role_pairs_")
    end

    -- 重新提取角色和CV列表
    cv_role_pairs = text_utils.extract_cv_role_pairs(sentences, is_cv_role_reversed)

    -- 如果当前已选择了文本，重新处理角色和CV（不保留之前的值）
    if selected_text and selected_text ~= "" then
      selected_role, selected_cv = text_utils.handle_cv_role_selection(
        selected_text,
        cv_role_pairs,
        "",  -- 传入空字符串，强制重新提取
        "",  -- 传入空字符串，强制重新提取
        is_cv_role_reversed
      )
    else
      -- 没有选中文本时，只交换当前的角色和CV
      if selected_role ~= "" and selected_cv ~= "" then
        local temp_role = selected_role
        selected_role = selected_cv
        selected_cv = temp_role
      end
    end

    -- 不再打印调试信息到控制台

    force_redraw = true  -- 强制重绘界面
  end
end

-- 添加一个设置clipboard_text的辅助函数
function set_clipboard_text(text)
  clipboard_text = text
end

-- 添加一个直接处理文本内容的函数
function handle_text_content(text)
  -- 设置剪贴板文本
  clipboard_text = text
  -- 解析句子
  parse_sentences()
  -- 提取CV和角色
  extract_cv_role_pairs()
  -- 更新章节信息
  update_current_chapter()
end

-- 渲染函数已合并到render函数中

-- 添加一个辅助函数用于绘制选择内容
function draw_selection_content()
  local y_offset = 5
  local max_width = selection_area.w - 20  -- 减少宽度，为滚动条留出空间
  local total_height = 0  -- 计算内容总高度
  local content_to_draw = {}  -- 存储要绘制的内容

  -- 确保绘制前设置默认的白色文本
  gfx.set(1, 1, 1, 1)  -- 白色

  -- 收集所有要显示的内容并计算总高度
  if episode_number ~= "" then
    table.insert(content_to_draw, {type = "text", text = "集数: " .. episode_number, height = 20})
    total_height = total_height + 20
  end

  if selected_role ~= "" then
    local display_role = clean_text_tags(selected_role)
    table.insert(content_to_draw, {type = "text", text = "角色: " .. display_role, height = 20})
    total_height = total_height + 20
  end

  if selected_cv ~= "" then
    local display_cv = clean_text_tags(selected_cv)
    table.insert(content_to_draw, {type = "text", text = "CV: " .. display_cv, height = 20})
    total_height = total_height + 20
  end

  if error_note ~= "" then
    local error_text = "错误描述: " .. error_note
    -- 使用text_utils模块计算错误描述的高度
    gfx.setfont(1, font_name, font_size)  -- 使用固定的界面字体大小用于计算
    local text_height = text_utils.calculate_text_height(error_text, max_width, gfx)
    table.insert(content_to_draw, {type = "wrap", text = error_text, height = text_height})
    total_height = total_height + text_height
  end

  if correct_note ~= "" then
    local correct_text = "正确表达: " .. correct_note
    -- 使用text_utils模块计算正确表达的高度
    gfx.setfont(1, font_name, font_size)  -- 使用固定的界面字体大小
    local text_height = text_utils.calculate_text_height(correct_text, max_width, gfx)
    table.insert(content_to_draw, {type = "wrap", text = correct_text, height = text_height})
    total_height = total_height + text_height
  end

  if process_suggestion ~= "" then
    local suggestion_text = "处理建议: " .. process_suggestion
    -- 使用text_utils模块计算处理建议的高度
    gfx.setfont(1, font_name, font_size)  -- 使用固定的界面字体大小
    local text_height = text_utils.calculate_text_height(suggestion_text, max_width, gfx)
    table.insert(content_to_draw, {type = "wrap", text = suggestion_text, height = text_height})
    total_height = total_height + text_height
  end

  if marked_relative_time ~= "" then
    table.insert(content_to_draw, {type = "text", text = "错误时间: " .. marked_relative_time, height = 20})
    total_height = total_height + 20
  end

  if selected_text ~= "" then
    -- 分离前缀和内容，单独处理
    local prefix = "文本: "
    -- 计算前缀宽度
    gfx.setfont(1, font_name, font_size)
    local prefix_width = gfx.measurestr(prefix)

    -- 直接从选择区域宽度计算可用宽度，确保考虑滚动条空间
    local content_max_width = selection_area.w - prefix_width - 30  -- 修改：增加边距，确保文本不会超出背景框

    -- 根据是否需要滚动条进一步调整宽度
    if total_height > selection_area.h then
      content_max_width = content_max_width - 10  -- 额外减去滚动条宽度
    end

    -- 如果是多选模式，分别处理每个选中的句子
    if #selected_indices > 1 then
      local combined_height = 0
      local sentence_elements = {}

      -- 计算每个句子的高度并添加分隔符
      for i, idx in ipairs(selected_indices) do
        -- 清除句子中的标签
        local sentence_text = clean_text_tags(sentences[idx])
        local sentence_height = text_utils.calculate_text_height(sentence_text, content_max_width, gfx)

        -- 添加句子内容
        table.insert(sentence_elements, {
          type = "complex",
          prefix = "",
          content = sentence_text,
          prefix_width = 0,
          content_max_width = content_max_width,
          height = sentence_height,
          indent = 10  -- 缩进显示
        })
        combined_height = combined_height + sentence_height

        -- 除最后一个句子外，添加分隔线
        if i < #selected_indices then
          table.insert(sentence_elements, {
            type = "separator",
            height = 10
          })
          combined_height = combined_height + 10
        end
      end

      -- 添加多选文本的复合元素
      table.insert(content_to_draw, {
        type = "text",
        text = prefix,
        height = 20
      })
      total_height = total_height + 20

      -- 添加所有句子元素
      for _, element in ipairs(sentence_elements) do
        table.insert(content_to_draw, element)
        total_height = total_height + element.height
      end

    else
      -- 单选模式使用原来的处理方式
      -- 清除选中文本中的标签
      local clean_selected_text = clean_text_tags(selected_text)
      -- 计算文本内容的高度
      local content_height = text_utils.calculate_text_height(clean_selected_text, content_max_width, gfx)

      -- 添加前缀和内容作为一个复合元素
      table.insert(content_to_draw, {
        type = "complex",
        prefix = prefix,
        content = clean_selected_text,
        prefix_width = prefix_width,
        content_max_width = content_max_width,
        height = content_height + 10 -- 增加间距但不要过大
      })

      total_height = total_height + content_height + 10
    end
  end

  -- 绘制滚动条
  if total_height > selection_area.h then
    -- 创建滚动条对象
    local scrollbar = {
      x = selection_area.x + selection_area.w - 10,
      y = selection_area.y,
      w = 10,
      h = selection_area.h
    }

    -- 保存滚动条信息供鼠标处理使用
    ui.selection_scrollbar = {
      x = scrollbar.x,
      y = scrollbar.y,
      w = scrollbar.w,
      h = scrollbar.h,
      total_height = total_height
    }

    -- 确保滚动位置不超过有效范围
    local max_scroll = math.max(0, total_height - selection_area.h)
    selection_scroll_pos = math.min(selection_scroll_pos, max_scroll)

    -- 调用style_module中的绘制滚动条函数
    style_module.draw_scrollbar(scrollbar, selection_scroll_pos, max_scroll, selection_area.h, total_height)
  else
    ui.selection_scrollbar = nil  -- 无需滚动条时清空引用
    selection_scroll_pos = 0  -- 重置滚动位置
  end

  -- 绘制内容
  y_offset = 5 - selection_scroll_pos  -- 应用滚动偏移
  for _, content in ipairs(content_to_draw) do
    if y_offset + content.height > 0 and y_offset < selection_area.h then  -- 只绘制可见区域内的内容
      if content.type == "text" then
        -- 使用白色文本（适配黑色背景）
        gfx.set(1, 1, 1, 1)
        -- 检查文本是否完全在可见区域内
        if y_offset >= 0 and y_offset + 20 <= selection_area.h then
          -- 应用缩进(如果有)
          local x_offset = content.is_header and 15 or 5
          gfx.x, gfx.y = selection_area.x + x_offset, selection_area.y + y_offset
          gfx.drawstr(content.text)
        elseif y_offset < 0 and y_offset + 20 > 0 then
          -- 文本部分可见（顶部被截断）
          local x_offset = content.is_header and 15 or 5
          gfx.x, gfx.y = selection_area.x + x_offset, selection_area.y
          gfx.drawstr(content.text)
        elseif y_offset < selection_area.h and y_offset + 20 > selection_area.h then
          -- 文本部分可见（底部被截断）
          local x_offset = content.is_header and 15 or 5
          gfx.x, gfx.y = selection_area.x + x_offset, selection_area.y + y_offset
          gfx.drawstr(content.text)
        end
      elseif content.type == "separator" then
        -- 绘制分隔线
        if y_offset >= 0 and y_offset + content.height <= selection_area.h then
          gfx.set(0.5, 0.5, 0.5, 0.5)  -- 灰色半透明
          local line_y = selection_area.y + y_offset + content.height/2
          gfx.line(selection_area.x + 20, line_y, selection_area.x + selection_area.w - 20, line_y)
          gfx.set(1, 1, 1, 1)  -- 使用白色文本
        end
      elseif content.type == "wrap" then
        -- 对于换行文本，使用text_utils的wrap_text函数但限制在可见区域内
        gfx.setfont(1, font_name, font_size)  -- 使用固定的界面字体大小，而不是内容字体大小
        -- 设置白色文本
        gfx.set(1, 1, 1, 1)
        local visible_y = math.max(0, y_offset)
        local y_pos = selection_area.y + visible_y
        -- 确保不会绘制超出底部边界的内容
        if y_pos < selection_area.y + selection_area.h then
          text_utils.wrap_text(
            content.text,
            max_width,
            selection_area.x + 5,
            y_pos,
            true,
            selection_area.y,
            selection_area.y + selection_area.h,
            gfx
          )
        end
      elseif content.type == "complex" then
        -- 对于复合元素，使用text_utils模块的wrap_text函数但限制在可见区域内
        gfx.setfont(1, font_name, font_size)  -- 使用固定的界面字体大小，而不是内容字体大小
        local visible_y = math.max(0, y_offset)
        local y_pos = selection_area.y + visible_y

        -- 确保不会绘制超出底部边界的内容
        if y_pos < selection_area.y + selection_area.h then
          -- 先绘制前缀
          gfx.set(1, 1, 1, 1)  -- 使用白色文本

          -- 计算X坐标，考虑缩进
          local indent = content.indent or 0
          local x_pos = selection_area.x + 5 + indent
          gfx.x = x_pos
          gfx.y = y_pos

          -- 如果有前缀，绘制前缀
          if content.prefix and content.prefix ~= "" then
            gfx.drawstr(content.prefix)
          end

          -- 对长文本特殊处理，分段绘制避免单次处理过多内容
          local text_to_draw = content.content
          if #text_to_draw > 200 then
            -- 将长文本分成多行预处理，提高渲染效率
            local lines = {}
            local current_line = ""
            local current_width = 0
            
            -- 确保内容最大宽度不超过实际可用宽度
            local safe_content_max_width = math.min(content.content_max_width, selection_area.w - 30)

            -- 逐字符测量并分行
            local i = 1
            while i <= #text_to_draw do
              local byte = text_to_draw:byte(i)
              local char_len = 1
              if byte >= 0xC0 and byte <= 0xDF then char_len = 2
              elseif byte >= 0xE0 and byte <= 0xEF then char_len = 3
              elseif byte >= 0xF0 and byte <= 0xF7 then char_len = 4 end

              local c = text_to_draw:sub(i, i + char_len - 1)
              local char_width = gfx.measurestr(c)

              if current_width + char_width > safe_content_max_width then
                -- 换行
                table.insert(lines, current_line)
                current_line = c
                current_width = char_width
              else
                -- 继续当前行
                current_line = current_line .. c
                current_width = current_width + char_width
              end

              i = i + char_len
            end

            -- 添加最后一行
            if current_line ~= "" then
              table.insert(lines, current_line)
            end

            -- 绘制处理好的各行文本
            local line_height = math.max(20, font_size + 2)
            local line_y = y_pos

            -- 第一行需要和前缀在同一行
            if #lines > 0 and line_y >= selection_area.y and line_y < selection_area.y + selection_area.h then
              local x_offset = (content.prefix and content.prefix ~= "") and content.prefix_width or 0
              local indent = content.indent or 0
              gfx.x = selection_area.x + 5 + x_offset + indent
              gfx.y = line_y
              gfx.set(1, 1, 1, 1)  -- 使用白色文本
              gfx.drawstr(lines[1])
              line_y = line_y + line_height
            end

            -- 绘制剩余的行
            for i = 2, #lines do
              local line = lines[i]
              -- 检查是否在可见区域内
              if line_y >= selection_area.y and line_y < selection_area.y + selection_area.h then
                local indent = content.indent or 0
                gfx.x = selection_area.x + 10 + indent -- 应用额外缩进
                gfx.y = line_y
                gfx.set(1, 1, 1, 1)  -- 使用白色文本
                gfx.drawstr(line)
              end
              line_y = line_y + line_height

              -- 如果已超出可见区域，退出循环
              if line_y > selection_area.y + selection_area.h then
                break
              end
            end
          else
            -- 对短文本使用原始的wrap_text函数
            local indent = content.indent or 0
            local x_pos = selection_area.x + 5 + content.prefix_width + indent
            
            -- 确保内容最大宽度不超过实际可用宽度
            local safe_content_max_width = math.min(content.content_max_width, selection_area.w - 30)

            text_utils.wrap_text(
              text_to_draw,
              safe_content_max_width,
              x_pos,
              y_pos,
              true,
              selection_area.y,
              selection_area.y + selection_area.h,
              gfx
            )
          end
        end
      end
    end
    y_offset = y_offset + content.height
  end
end

-- 辅助函数：获取CV角色对的分类数据，无论是否交换位置都保持一致的显示
function get_cv_role_categories(pairs, is_reversed)
  local cv_categories = {}  -- 用于存储按CV分类的角色
  local cv_order = {}       -- 用于记录CV的顺序

  -- 遍历所有CV角色对，按CV进行分类
  -- 无论是否选择了交换位置复选框，都以相同的方式显示
  for i, pair in ipairs(pairs) do
    -- 始终保持角色在右边，CV在左边的显示方式，忽略is_reversed参数
    local cv_name = pair.cv
    local role_name = pair.role

    if not cv_categories[cv_name] then
      cv_categories[cv_name] = {}
      table.insert(cv_order, cv_name)
    end
    table.insert(cv_categories[cv_name], {role = role_name, index = i})
  end

  return cv_categories, cv_order
end

-- 添加一个辅助函数，用于清除文本中的颜色标签和删除线标签
function clean_text_tags(text)
  if not text then return "" end
  
  -- 清除颜色标签 [#RRGGBB] 和 [#]
  local cleaned = text:gsub("%[#[0-9A-Fa-f]+%]", ""):gsub("%[#%]", "")
  
  -- 清除背景色标签 [bg#RRGGBB] 和 [bg#]
  cleaned = cleaned:gsub("%[bg#[0-9A-Fa-f]+%]", ""):gsub("%[bg#%]", "")
  
  -- 清除删除线标签 [x] 和 [/x]
  cleaned = cleaned:gsub("%[x%]", ""):gsub("%[/x%]", "")
  
  -- 清除章节标记 [CHAPTER] 和 [/CHAPTER]
  cleaned = cleaned:gsub("%[CHAPTER%]", ""):gsub("%[/CHAPTER%]", "")
  
  return cleaned
end
