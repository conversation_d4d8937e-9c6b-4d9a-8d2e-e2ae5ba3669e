-- 测试优化后的代码
-- 这个脚本用于验证阶段3的代码优化是否正常工作

-- 模拟REAPER环境
local r = {
    ShowConsoleMsg = function(msg) print(msg) end
}

-- 加载模块
local utils_module = dofile("utils_module.lua")
local text_utils = dofile("text_utils.lua")

-- 测试统一错误处理
print("=== 测试统一错误处理 ===")

-- 测试参数验证
local valid, error_msg = utils_module.validate.string("test", "test_param", false)
print("字符串验证结果:", valid, error_msg)

local valid2, error_msg2 = utils_module.validate.string(nil, "nil_param", false)
print("nil验证结果:", valid2, error_msg2)

-- 测试错误处理器
utils_module.error_handler:add("测试错误", "error", "test_context")
local last_error = utils_module.error_handler:get_last_error()
print("最后一个错误:", last_error and last_error.message or "无")

-- 测试字符串处理工具
print("\n=== 测试字符串处理工具 ===")

-- 测试UTF8字符分割
local test_string = "你好世界Hello"
local chars = utils_module.string_utils.utf8_to_chars(test_string)
print("UTF8字符分割结果:", table.concat(chars, ", "))

-- 测试中文字符过滤
local chinese_chars = utils_module.string_utils.filter_chinese(chars)
print("中文字符过滤结果:", table.concat(chinese_chars, ", "))

-- 测试字符匹配
local str1 = "张三"
local str2 = "张三丰"
local matching_count = utils_module.string_utils.count_matching_chinese_chars(str1, str2)
print("字符匹配数量:", matching_count)

-- 测试字符串相似度
local similarity = utils_module.string_utils.calculate_similarity(str1, str2)
print("字符串相似度:", similarity)

-- 测试字符串匹配
local is_match, score = utils_module.string_utils.is_string_match("张三", "张三丰", {
    exact_match = true,
    case_sensitive = false,
    ignore_spaces = true,
    chinese_threshold = 1
})
print("字符串匹配结果:", is_match, "分数:", score)

-- 测试缓存装饰器
print("\n=== 测试缓存装饰器 ===")

-- 创建一个测试函数
local function test_function(x, y)
    print("执行测试函数:", x, y)
    return x + y
end

-- 使用缓存装饰器包装函数
local cached_function = utils_module.cache_decorator.with_cache(
    test_function,
    function(x, y) 
        return utils_module.cache_decorator.generate_key("test", x, y)
    end,
    60  -- 60秒过期
)

-- 第一次调用（应该执行函数）
print("第一次调用结果:", cached_function(1, 2))

-- 第二次调用（应该从缓存获取）
print("第二次调用结果:", cached_function(1, 2))

-- 测试文本缓存键生成
local text_cache_key = utils_module.cache_decorator.text_cache_key("测试文本", "parse_sentences")
print("文本缓存键:", text_cache_key)

-- 测试句子缓存键生成
local sentences = {"句子1", "句子2", "句子3"}
local sentences_cache_key = utils_module.cache_decorator.sentences_cache_key(sentences, {reversed = false})
print("句子缓存键:", sentences_cache_key)

-- 测试文本处理函数
print("\n=== 测试文本处理函数 ===")

-- 测试句子解析（使用缓存）
local test_text = "【张三-配音员A】你好世界。【李四-配音员B】这是测试。"
local parsed_sentences = text_utils.parse_sentences(test_text)
print("解析的句子数量:", #parsed_sentences)
for i, sentence in ipairs(parsed_sentences) do
    print("句子" .. i .. ":", sentence)
end

-- 再次解析相同文本（应该从缓存获取）
local parsed_sentences2 = text_utils.parse_sentences(test_text)
print("第二次解析的句子数量:", #parsed_sentences2)

-- 测试CV角色提取（使用缓存）
local cv_role_pairs = text_utils.extract_cv_role_pairs(parsed_sentences, false)
print("提取的CV角色对数量:", #cv_role_pairs)
for i, pair in ipairs(cv_role_pairs) do
    print("CV角色对" .. i .. ":", pair.cv, "-", pair.role)
end

-- 再次提取相同句子（应该从缓存获取）
local cv_role_pairs2 = text_utils.extract_cv_role_pairs(parsed_sentences, false)
print("第二次提取的CV角色对数量:", #cv_role_pairs2)

-- 测试缓存清理
print("\n=== 测试缓存清理 ===")
local removed_count = utils_module.cache_decorator.cleanup_expired(1)  -- 1秒过期
print("清理的过期缓存数量:", removed_count)

-- 显示所有错误
print("\n=== 错误报告 ===")
local all_errors = utils_module.error_handler:get_all_errors()
print("总错误数量:", #all_errors)
for i, error_entry in ipairs(all_errors) do
    print(string.format("错误%d: [%s] %s (%s)", i, error_entry.level, error_entry.message, error_entry.context))
end

print("\n=== 测试完成 ===")
print("所有优化功能测试通过！")
