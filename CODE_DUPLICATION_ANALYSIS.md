# 代码重复分析报告

## 发现的重复代码模式

### 1. 字符串处理函数重复 🔴 严重

#### 问题描述
在 `button_module.lua` 中发现了两个几乎完全相同的字符串匹配函数：

**位置1**: `button_module.lua` 行1867-1888
**位置2**: `button_module.lua` 行2285-2306

```lua
-- 重复的函数1
local function count_matching_chars(chars1, chars2)
  local count = 0
  for _, char1 in ipairs(chars1) do
    for _, char2 in ipairs(chars2) do
      if char1 == char2 then
        count = count + 1
        break
      end
    end
  end
  return count
end

-- 重复的函数2 (完全相同)
local function count_matching_chars(chars1, chars2)
  local count = 0
  for _, char1 in ipairs(chars1) do
    for _, char2 in ipairs(chars2) do
      if char1 == char2 then
        count = count + 1
        break
      end
    end
  end
  return count
end
```

**影响**: 
- 代码维护困难
- 增加文件大小
- 逻辑不一致风险

**解决方案**: 提取到 `utils_module.lua` 作为通用函数

### 2. 错误处理模式重复 🟡 中等

#### 问题描述
多个模块中都有相似的错误处理逻辑：

**text_utils.lua**:
```lua
local error_handler = {
    errors = {},
    add = function(self, message, level) ... end,
    clear = function(self) ... end,
    get_last_error = function(self) ... end
}
```

**utils_module.lua**:
```lua
function utils_module.safe_call(func, ...)
  if not func or type(func) ~= "function" then
    return false, "错误: 无效的函数参数"
  end
  local status, result = pcall(func, ...)
  if not status then
    utils_module.error("函数执行失败: %s", tostring(result))
    return false, "错误: " .. tostring(result)
  end
  return true, result
end
```

**影响**:
- 错误处理不统一
- 重复的错误处理逻辑

**解决方案**: 统一使用 `utils_module` 的错误处理机制

### 3. 缓存模式重复 🟡 中等

#### 问题描述
多个地方都有相似的缓存逻辑：

**text_utils.lua**:
```lua
-- 使用文本的哈希作为缓存键
local cache_key = "parse_sentences_" .. utils_module.hash_string(text)
if cache_module.has(cache_key) then
    return cache_module.get(cache_key)
end
```

**text_utils.lua** (另一处):
```lua
-- 使用句子哈希作为缓存键
local cache_key = "cv_role_pairs_" .. sentences_hash .. "_" .. #sentences
if cache_module.has(cache_key) then
    return cache_module.get(cache_key)
end
```

**影响**:
- 缓存策略不一致
- 重复的缓存检查代码

**解决方案**: 创建统一的缓存装饰器函数

### 4. 文件操作重复 🟡 中等

#### 问题描述
多个模块中都有相似的文件操作逻辑：

**utils_module.lua**:
```lua
local file, err = io.open(path .. "/.test_write", "w")
if not file then
    if r.JS_Process_ExecuteCommand then
        r.JS_Process_ExecuteCommand('mkdir "' .. path .. '" 2>nul', false)
    else
        local cmd = 'start /b "" cmd /c "mkdir "' .. path .. '" > nul 2>&1"'
        os.execute(cmd)
    end
end
```

**word_module.lua**:
```lua
if r.JS_Process_ExecuteCommand then
    r.JS_Process_ExecuteCommand('rmdir /S /Q "' .. temp_dir .. '" 2>nul', false)
else
    os.execute('rmdir /S /Q "' .. temp_dir .. '" 2>nul')
end
```

**影响**:
- 文件操作不一致
- 平台兼容性处理重复

**解决方案**: 在 `utils_module` 中创建统一的文件操作函数

### 5. 参数验证重复 🟢 轻微

#### 问题描述
多个函数都有相似的参数验证逻辑：

```lua
-- 模式1
if not text or type(text) ~= "string" then
    return {}
end

-- 模式2  
if not sentences or type(sentences) ~= "table" then
    return {}
end

-- 模式3
if not str or type(str) ~= "string" then 
    return ""
end
```

**影响**:
- 验证逻辑不统一
- 代码冗余

**解决方案**: 创建通用的参数验证函数

## 优化建议

### 立即处理 (高优先级)

1. **合并重复的字符串处理函数**
   - 将 `count_matching_chars` 提取到 `utils_module.lua`
   - 添加 `utf8_to_chars` 和 `filter_chinese` 等通用函数

2. **统一错误处理机制**
   - 移除 `text_utils.lua` 中的 `error_handler`
   - 统一使用 `utils_module` 的错误处理

### 中期处理 (中优先级)

3. **创建缓存装饰器**
   ```lua
   function utils_module.with_cache(func, cache_key_generator)
       return function(...)
           local key = cache_key_generator(...)
           if cache_module.has(key) then
               return cache_module.get(key)
           end
           local result = func(...)
           cache_module.set(key, result)
           return result
       end
   end
   ```

4. **统一文件操作**
   ```lua
   function utils_module.execute_command(cmd)
       if r.JS_Process_ExecuteCommand then
           return r.JS_Process_ExecuteCommand(cmd, false)
       else
           return os.execute(cmd)
       end
   end
   ```

### 长期处理 (低优先级)

5. **创建参数验证工具**
   ```lua
   function utils_module.validate_params(params, schema)
       -- 通用参数验证逻辑
   end
   ```

## 预期效果

### 代码减少
- `button_module.lua`: 减少约50行重复代码
- `text_utils.lua`: 减少约30行重复代码
- 总计: 减少约100-150行重复代码

### 维护性提升
- 统一的错误处理机制
- 一致的缓存策略
- 标准化的文件操作

### 性能提升
- 减少重复计算
- 优化缓存使用
- 统一的字符串处理

## 实施计划

1. **第1天**: 处理字符串函数重复
2. **第2天**: 统一错误处理机制  
3. **第3天**: 创建缓存装饰器
4. **第4天**: 统一文件操作
5. **第5天**: 测试和验证

这些优化将显著提升代码质量和维护性。
