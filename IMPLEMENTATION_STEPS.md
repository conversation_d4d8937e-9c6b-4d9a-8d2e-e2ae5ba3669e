# 具体实施步骤

## 阶段1: 主脚本重构 (1-2天)

### 步骤1.1: 创建UI模块 (4小时)

#### 创建 `ui_module.lua`
```bash
# 需要从 mark.lua 移动的函数 (约1500行)
- render()                    # 主渲染函数 (约200行)
- draw_ui()                   # UI绘制 (约300行)
- draw_sentences_list()       # 句子列表 (约400行)
- draw_chapter_list()         # 章节列表 (约200行)
- draw_selection_content()    # 选择内容 (约150行)
- draw_search_ui()           # 搜索界面 (约100行)
- calculate_sentence_height() # 高度计算 (约50行)
- ensure_sentence_heights_cached() # 缓存管理 (约100行)
```

#### 模块结构
```lua
-- ui_module.lua
local ui_module = {}

-- 依赖注入
local style_module, text_utils, gfx

function ui_module.init(deps)
    style_module = deps.style_module
    text_utils = deps.text_utils
    gfx = deps.gfx
    return ui_module
end

-- 主渲染函数
function ui_module.render(app_state)
    -- 移动的渲染逻辑
end

-- 其他UI函数...

return ui_module
```

### 步骤1.2: 创建事件模块 (4小时)

#### 创建 `event_module.lua`
```bash
# 需要从 mark.lua 移动的函数 (约800行)
- handle_mouse_events()       # 鼠标事件 (约300行)
- handle_keyboard_events()    # 键盘事件 (约200行)
- handle_window_resize()      # 窗口事件 (约100行)
- handle_cv_role_checkbox_click() # 复选框事件 (约50行)
- perform_search()            # 搜索事件 (约100行)
- goto_next_search_result()   # 搜索导航 (约50行)
```

#### 模块结构
```lua
-- event_module.lua
local event_module = {}

-- 事件处理状态
local event_state = {
    last_mouse_x = 0,
    last_mouse_y = 0,
    mouse_down = false,
    -- 其他状态...
}

function event_module.init(deps)
    -- 初始化依赖
    return event_module
end

function event_module.handle_events(app_state)
    -- 统一的事件处理入口
    handle_mouse_events(app_state)
    handle_keyboard_events(app_state)
    handle_window_events(app_state)
end

return event_module
```

### 步骤1.3: 精简主脚本 (2小时)

#### 重构后的 `mark.lua` 结构
```lua
-- mark.lua (精简到约1200行)

-- 模块加载
local modules = module_loader.load_all({
    show_init_message = false
})

-- 获取模块引用
local ui_module = modules.ui_module
local event_module = modules.event_module
local utils_module = modules.utils_module
-- 其他模块...

-- 应用状态管理
local app_state = {
    -- 全局状态变量
    sentences = {},
    cv_role_pairs = {},
    selected_cv = "",
    selected_role = "",
    -- 其他状态...
    
    -- 状态管理方法
    update_state = function(self, key, value)
        self[key] = value
        self.force_redraw = true
    end,
    
    get_state = function(self, key)
        return self[key]
    end
}

-- 初始化函数
function init()
    -- 初始化gfx
    -- 初始化模块
    -- 加载保存的数据
    return true
end

-- 主循环
function main_loop()
    -- 渲染UI
    ui_module.render(app_state)
    
    -- 处理事件
    event_module.handle_events(app_state)
    
    -- 检查退出条件
    if app_state.should_exit then
        return false
    end
    
    return true
end

-- 清理函数
function cleanup()
    -- 保存数据
    -- 清理资源
end

-- 主函数
function main()
    if not init() then 
        return 
    end
    
    while main_loop() do
        -- 主循环
    end
    
    cleanup()
end

-- 启动应用
main()
```

## 阶段2: 小模块整合 (0.5天)

### 步骤2.1: 合并章节模块 (2小时)

#### 将 `chapter_module.lua` 合并到 `text_utils.lua`
```lua
-- text_utils.lua 中添加
text_utils.chapter = {
    extract_chapters = function(sentences)
        -- 从 chapter_module.lua 移动的代码
    end,
    
    get_chapter_by_sentence_idx = function(sentence_idx)
        -- 从 chapter_module.lua 移动的代码
    end,
    
    jump_to_chapter = function(chapter_idx, scroll_callback)
        -- 从 chapter_module.lua 移动的代码
    end,
    
    chinese_to_number = function(chinese_num)
        -- 从 chapter_module.lua 移动的代码
    end
}
```

### 步骤2.2: 合并对轨脚本 (2小时)

#### 将 `track_align_script.lua` 合并到 `button_module.lua`
```lua
-- button_module.lua 中添加
button_module.track_align = {
    cv_to_track_map = {
        ["旁白"] = 1,
        ["角色A"] = 2,
        -- 其他映射...
    },
    
    align_track_by_cv = function(cv_name, is_right_click)
        -- 从 track_align_script.lua 移动的代码
    end,
    
    handle_track_align_click = function(selected_cv, selected_role, is_right_click)
        -- 处理对轨按钮点击
        return button_module.track_align.align_track_by_cv(selected_cv, is_right_click)
    end
}
```

## 阶段3: 代码优化 (1天)

### 步骤3.1: 统一错误处理 (2小时)

#### 移除重复的错误处理代码
```lua
-- 在 utils_module.lua 中统一错误处理
utils_module.error_handler = {
    add_error = function(message, level, context)
        -- 统一的错误添加逻辑
    end,
    
    get_last_error = function()
        -- 获取最后一个错误
    end,
    
    clear_errors = function()
        -- 清除所有错误
    end,
    
    with_error_handling = function(func, error_message)
        -- 错误处理装饰器
        return function(...)
            local success, result = pcall(func, ...)
            if not success then
                utils_module.error_handler.add_error(error_message or "函数执行失败", "error")
                return nil, result
            end
            return result
        end
    end
}
```

### 步骤3.2: 优化字符串处理 (3小时)

#### 提取重复的字符串函数到 utils_module
```lua
-- utils_module.lua 中添加
utils_module.string_utils = {
    -- 从 button_module.lua 移动重复的函数
    utf8_to_chars = function(str)
        -- UTF8字符分割
    end,
    
    filter_chinese = function(chars)
        -- 过滤中文字符
    end,
    
    count_matching_chars = function(chars1, chars2)
        -- 计算匹配字符数
    end,
    
    calculate_similarity = function(str1, str2)
        -- 计算字符串相似度
    end
}
```

### 步骤3.3: 改进缓存机制 (3小时)

#### 创建统一的缓存装饰器
```lua
-- utils_module.lua 中添加
utils_module.cache_decorator = {
    with_cache = function(func, cache_key_generator, ttl)
        return function(...)
            local args = {...}
            local cache_key = cache_key_generator(unpack(args))
            
            -- 检查缓存
            if utils_module.cache_has(cache_key) then
                return utils_module.cache_get(cache_key)
            end
            
            -- 执行函数
            local result = func(unpack(args))
            
            -- 存储到缓存
            utils_module.cache_add(cache_key, result)
            
            return result
        end
    end,
    
    generate_key = function(prefix, ...)
        local parts = {prefix}
        for i, v in ipairs({...}) do
            table.insert(parts, tostring(v))
        end
        return table.concat(parts, "_")
    end
}
```

## 阶段4: 模块依赖优化 (0.5天)

### 步骤4.1: 清理模块映射 (2小时)

#### 更新 `module_loader.lua`
```lua
-- 移除已合并模块的映射
local module_mapping = {
    -- 移除这些已合并的映射
    -- button_helpers = "style_module", -- 已合并
    -- mark_function = "button_module", -- 已合并
    -- data_persist_module = "excel_module", -- 已合并
    -- playback_control = "utils_module", -- 已合并
    -- cache_module = "utils_module" -- 已合并
    -- chapter_module = "text_utils", -- 新合并
    -- track_align_script = "button_module" -- 新合并
}

-- 更新模块加载顺序
local module_load_order = {
    "utils_module",      -- 1. 工具模块
    "style_module",      -- 2. 样式模块
    "text_utils",        -- 3. 文本处理 (包含章节功能)
    "excel_module",      -- 4. Excel处理 (包含数据持久化)
    "word_module",       -- 5. Word文档处理
    "button_module",     -- 6. 按钮模块 (包含打标和对轨功能)
    "ui_module",         -- 7. UI模块 (新增)
    "event_module"       -- 8. 事件模块 (新增)
}
```

### 步骤4.2: 优化依赖关系 (2小时)

#### 重新设计模块依赖
```lua
-- 更新依赖配置
local module_dependencies = {
    style_module = {"utils_module"},
    text_utils = {"utils_module"},
    excel_module = {"utils_module", "text_utils"},
    word_module = {"utils_module"},
    button_module = {"utils_module", "style_module", "text_utils"},
    ui_module = {"utils_module", "style_module", "text_utils", "button_module"},
    event_module = {"utils_module", "ui_module", "button_module"}
}
```

## 测试计划

### 功能测试 (每个阶段完成后)
1. **UI渲染测试**: 确保所有界面元素正常显示
2. **事件响应测试**: 确保鼠标、键盘事件正常
3. **模块加载测试**: 确保所有模块正确加载
4. **数据操作测试**: 确保保存/加载功能正常
5. **性能测试**: 确保启动时间和响应速度

### 回归测试清单
- [ ] 文档读取功能
- [ ] 剪贴板功能
- [ ] 句子解析功能
- [ ] CV角色提取功能
- [ ] 搜索功能
- [ ] 章节功能
- [ ] 对轨功能
- [ ] 打标功能
- [ ] Excel导出功能
- [ ] 数据保存/加载功能

## 风险控制

### 备份策略
- 每个阶段开始前创建完整备份
- 使用Git进行版本控制
- 保留原始文件副本

### 回滚计划
- 如果测试失败，立即回滚到上一个稳定版本
- 分析失败原因，修复后重新开始
- 保持小步快跑的原则

这个实施计划将确保优化过程的安全性和可控性。
