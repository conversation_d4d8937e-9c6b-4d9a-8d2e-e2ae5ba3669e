-- 调试渲染错误脚本
-- 用于确定具体的错误位置和原因

-- 模拟REAPER环境
local r = {
    ShowConsoleMsg = function(msg) print(msg) end,
    time_precise = function() return os.clock() end,
    GetSelectedMediaItem = function() return nil end,
    GetMainHwnd = function() return nil end,
    defer = function(func) end,
    GetPlayState = function() return 0 end,
    Main_OnCommand = function() end,
    Master_GetPlayRate = function() return 1.0 end,
    CSurf_OnPlayRateChange = function() end,
    GetToggleCommandStateEx = function() return 1 end
}

-- 模拟gfx环境
local gfx = {
    init = function() return true end,
    dock = function() end,
    setfont = function() end,
    clear = 0,
    w = 1200,
    h = 800,
    mouse_x = 100,
    mouse_y = 100,
    mouse_cap = 0,
    mouse_wheel = 0,
    getchar = function() return 0 end,
    set = function() end,
    rect = function() end,
    drawstr = function() end,
    measurestr = function() return 50 end,
    line = function() end,
    update = function() end,
    getfont = function() return 1, "Arial", 18 end
}

-- 设置全局变量
_G.reaper = r
_G.gfx = gfx

print("=== 调试渲染错误 ===")

-- 检查button_module.lua的第159行
print("\n1. 检查button_module.lua第159行")

local file = io.open("button_module.lua", "r")
if file then
    local lines = {}
    for line in file:lines() do
        table.insert(lines, line)
    end
    file:close()
    
    print("第155-165行内容:")
    for i = 155, 165 do
        if lines[i] then
            print(string.format("%d: %s", i, lines[i]))
        end
    end
else
    print("无法打开button_module.lua文件")
end

-- 加载模块并测试
print("\n2. 加载模块并测试")

local utils_module = dofile("utils_module.lua")
local style_module = dofile("style_module.lua")
local text_utils = dofile("text_utils.lua")
local button_module = dofile("button_module.lua")

print("✓ 所有模块加载成功")

-- 创建应用状态
local app_state = utils_module.app_state.create()
print("✓ 应用状态创建成功")
print("  current_playrate:", app_state.current_playrate)

-- 测试draw_rate_buttons函数
print("\n3. 直接测试draw_rate_buttons函数")

local test_ui = {
    rate_minus_button = {x = 10, y = 10, w = 30, h = 25},
    rate_display_area = {x = 45, y = 10, w = 50, h = 25},
    rate_plus_button = {x = 100, y = 10, w = 30, h = 25},
    rate_reset_button = {x = 135, y = 10, w = 40, h = 25}
}

-- 测试各种参数组合
local test_cases = {
    {
        name = "正常参数",
        params = {test_ui.rate_minus_button, test_ui.rate_display_area, test_ui.rate_plus_button, test_ui.rate_reset_button, 1.0}
    },
    {
        name = "nil播放速率",
        params = {test_ui.rate_minus_button, test_ui.rate_display_area, test_ui.rate_plus_button, test_ui.rate_reset_button, nil}
    },
    {
        name = "错误参数顺序（旧版本）",
        params = {test_ui.rate_minus_button, test_ui.rate_plus_button, test_ui.rate_display_area, 1.0}
    },
    {
        name = "应用状态中的播放速率",
        params = {test_ui.rate_minus_button, test_ui.rate_display_area, test_ui.rate_plus_button, test_ui.rate_reset_button, app_state.current_playrate}
    }
}

for _, test_case in ipairs(test_cases) do
    print("  测试: " .. test_case.name)
    local success, error_msg = pcall(function()
        button_module.draw_rate_buttons(unpack(test_case.params))
    end)
    
    if success then
        print("    ✓ 成功")
    else
        print("    ✗ 失败: " .. tostring(error_msg))
        -- 如果这里失败，说明问题仍然存在
        if error_msg:find("bad argument #2 to 'format'") then
            print("    >>> 这是我们要修复的错误！")
            print("    >>> 参数详情:")
            for i, param in ipairs(test_case.params) do
                print(string.format("      参数%d: %s (类型: %s)", i, tostring(param), type(param)))
            end
        end
    end
end

-- 测试UI模块
print("\n4. 测试UI模块")

local ui_module = dofile("ui_module.lua")
local event_module = dofile("event_module.lua")

local deps = {
    style_module = style_module,
    text_utils = text_utils,
    button_module = button_module,
    utils_module = utils_module,
    gfx = gfx,
    r = r
}

ui_module.init(deps)
deps.ui_module = ui_module
event_module.init(deps)

print("✓ UI和事件模块初始化成功")

-- 尝试渲染
print("\n5. 测试渲染")

local render_success, render_error = pcall(function()
    ui_module.render(app_state)
end)

if render_success then
    print("✓ 渲染成功")
else
    print("✗ 渲染失败: " .. tostring(render_error))
    
    -- 分析错误
    if render_error:find("button_module.lua:159") then
        print(">>> 确认错误位置在button_module.lua第159行")
        print(">>> 这表明可能有其他地方调用了draw_rate_buttons")
    end
end

-- 检查是否有其他调用
print("\n6. 检查所有draw_rate_buttons调用")

local files_to_check = {"ui_module.lua", "mark_new.lua", "mark.lua"}

for _, filename in ipairs(files_to_check) do
    local file = io.open(filename, "r")
    if file then
        local content = file:read("*all")
        file:close()
        
        local line_num = 0
        for line in content:gmatch("[^\r\n]+") do
            line_num = line_num + 1
            if line:find("draw_rate_buttons") then
                print(string.format("  %s:%d: %s", filename, line_num, line:gsub("^%s+", "")))
            end
        end
    end
end

print("\n=== 调试完成 ===")
print("如果仍然出现错误，请检查:")
print("1. 是否运行的是正确的文件版本")
print("2. 是否有其他地方调用了draw_rate_buttons")
print("3. 是否需要重新加载REAPER脚本")
