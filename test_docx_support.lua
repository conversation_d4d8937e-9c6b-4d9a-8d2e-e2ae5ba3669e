-- 测试DOCX文件支持脚本
-- 验证修复后的文档读取功能是否支持.docx文件

-- 导入必要的库
local r = reaper

-- 获取脚本路径
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"

-- 安全加载函数
local function safe_load_module(file_path)
  local success, result = pcall(dofile, file_path)
  if not success then
    r.ShowConsoleMsg("模块加载失败: " .. file_path .. " - " .. tostring(result) .. "\n")
    return {}
  end
  return result
end

-- 主函数
local function main()
  r.ShowConsoleMsg("=== DOCX文件支持测试 ===\n")
  
  -- 加载必要的模块
  local button_module = safe_load_module(script_dir .. "button_module.lua")
  local word_module = safe_load_module(script_dir .. "word_module.lua")
  
  if not button_module then
    r.ShowConsoleMsg("✗ button_module加载失败\n")
    return
  end
  
  if not word_module then
    r.ShowConsoleMsg("✗ word_module加载失败\n")
    return
  end
  
  r.ShowConsoleMsg("✓ 所有模块加载成功\n")
  
  -- 检查关键函数是否存在
  if not button_module.handle_document_button then
    r.ShowConsoleMsg("✗ handle_document_button函数不存在\n")
    return
  end
  
  if not word_module.parse_docx then
    r.ShowConsoleMsg("✗ parse_docx函数不存在\n")
    return
  end
  
  r.ShowConsoleMsg("✓ 关键函数检查通过\n")
  
  -- 测试文档读取功能
  r.ShowConsoleMsg("测试文档读取功能...\n")
  
  -- 创建测试回调函数
  local test_content = ""
  local test_sentences = {}
  local test_cv_pairs = {}
  
  local callbacks = {
    handle_text_content = function(content)
      test_content = content
      r.ShowConsoleMsg("✓ 接收到文档内容，长度: " .. #content .. " 字符\n")
    end,
    
    parse_sentences = function()
      -- 简单的句子分割测试
      if test_content and test_content ~= "" then
        for sentence in test_content:gmatch("[^。！？\n]+[。！？]?") do
          if sentence:trim and sentence:trim() ~= "" then
            table.insert(test_sentences, sentence:trim())
          elseif sentence ~= "" then
            table.insert(test_sentences, sentence)
          end
        end
        r.ShowConsoleMsg("✓ 解析了 " .. #test_sentences .. " 个句子\n")
      end
    end,
    
    extract_cv_role_pairs = function()
      -- 简单的CV角色对提取测试
      if test_content and test_content ~= "" then
        for cv_role in test_content:gmatch("【(.-)】") do
          if cv_role:find("-") then
            local cv, role = cv_role:match("(.-)%-(.+)")
            if cv and role then
              table.insert(test_cv_pairs, {cv = cv:trim and cv:trim() or cv, role = role:trim and role:trim() or role})
            end
          end
        end
        r.ShowConsoleMsg("✓ 提取了 " .. #test_cv_pairs .. " 个CV角色对\n")
      end
    end
  }
  
  -- 添加字符串trim函数（如果不存在）
  if not string.trim then
    function string:trim()
      return self:match("^%s*(.-)%s*$")
    end
  end
  
  -- 测试文档按钮处理函数
  r.ShowConsoleMsg("调用handle_document_button函数...\n")
  
  local result = button_module.handle_document_button(callbacks)
  
  r.ShowConsoleMsg("处理结果: " .. tostring(result) .. "\n")
  
  -- 显示测试结果
  if test_content ~= "" then
    r.ShowConsoleMsg("\n=== 测试结果 ===\n")
    r.ShowConsoleMsg("✓ 文档内容读取成功\n")
    r.ShowConsoleMsg("✓ 内容长度: " .. #test_content .. " 字符\n")
    r.ShowConsoleMsg("✓ 句子数量: " .. #test_sentences .. " 个\n")
    r.ShowConsoleMsg("✓ CV角色对数量: " .. #test_cv_pairs .. " 个\n")
    
    -- 显示前几个句子作为示例
    if #test_sentences > 0 then
      r.ShowConsoleMsg("\n前3个句子示例:\n")
      for i = 1, math.min(3, #test_sentences) do
        r.ShowConsoleMsg(i .. ". " .. test_sentences[i] .. "\n")
      end
    end
    
    -- 显示CV角色对作为示例
    if #test_cv_pairs > 0 then
      r.ShowConsoleMsg("\nCV角色对示例:\n")
      for i = 1, math.min(3, #test_cv_pairs) do
        r.ShowConsoleMsg(i .. ". CV: " .. test_cv_pairs[i].cv .. ", 角色: " .. test_cv_pairs[i].role .. "\n")
      end
    end
    
    -- 显示内容预览
    local preview = test_content:sub(1, 200)
    if #test_content > 200 then
      preview = preview .. "..."
    end
    r.ShowConsoleMsg("\n内容预览:\n" .. preview .. "\n")
    
    r.ShowConsoleMsg("\n🎉 DOCX文件支持测试成功！\n")
    r.ShowConsoleMsg("✅ .docx文件可以正确读取和解析\n")
    r.ShowConsoleMsg("✅ 文档内容可以正确提取\n")
    r.ShowConsoleMsg("✅ 句子解析功能正常\n")
    r.ShowConsoleMsg("✅ CV角色对提取功能正常\n")
    
  else
    r.ShowConsoleMsg("\n⚠️ 测试结果:\n")
    if result:find("用户取消") then
      r.ShowConsoleMsg("用户取消了文件选择，这是正常的\n")
    elseif result:find("Word模块加载失败") then
      r.ShowConsoleMsg("✗ Word模块加载失败，请检查word_module.lua文件\n")
    elseif result:find("无法解析Word文档") then
      r.ShowConsoleMsg("✗ Word文档解析失败，可能是文件格式问题\n")
    else
      r.ShowConsoleMsg("✗ 其他错误: " .. result .. "\n")
    end
  end
  
  r.ShowConsoleMsg("\n=== 功能验证说明 ===\n")
  r.ShowConsoleMsg("1. 如果选择了.docx文件，应该能正确解析内容\n")
  r.ShowConsoleMsg("2. 如果选择了.txt文件，应该能正确读取内容\n")
  r.ShowConsoleMsg("3. 文档内容应该包含颜色标签和删除线标签\n")
  r.ShowConsoleMsg("4. CV角色对应该能正确提取【角色-CV】格式\n")
  r.ShowConsoleMsg("5. 句子应该能正确分割和解析\n")
  
  r.ShowConsoleMsg("\n=== 测试完成 ===\n")
end

-- 运行测试
main()
