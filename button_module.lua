-- 按钮模块 - 处理按钮相关的逻辑和行为
local r = reaper

-- 获取脚本路径
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"

-- 加载模块加载器
local module_loader = dofile(script_dir .. "module_loader.lua")

-- 初始化按钮模块
local button_module = {}

-- 模块依赖项
local style_module = nil
local utils_module = nil
local playback_control = nil
local text_utils = nil  -- 直接使用text_utils
local excel_module = nil
local cache_module = nil
local word_module = nil  -- 添加word模块依赖
-- mark_module已合并到button_module中

-- 初始化函数，接收依赖项
function button_module.init(deps, config)
  -- 检查并设置依赖模块
  if deps then
    utils_module = deps.utils_module or module_loader.load("utils_module")
    style_module = deps.style_module or module_loader.load("style_module")
    cache_module = deps.cache_module or module_loader.load("cache_module")

    -- 其他可选依赖
    playback_control = deps.playback_control or module_loader.load("playback_control", false)
    text_utils = deps.text_utils or module_loader.load("text_utils", false)  -- 直接使用text_utils
    excel_module = deps.excel_module or module_loader.load("excel_module", false)
    -- mark_module已合并到button_module中
    word_module = deps.word_module or module_loader.load("word_module", false)  -- 加载word模块
  else
    -- 如果未提供依赖，直接通过module_loader加载
    utils_module = module_loader.load("utils_module")
    style_module = module_loader.load("style_module")
    cache_module = module_loader.load("cache_module")
    playback_control = module_loader.load("playback_control", false)
    text_utils = module_loader.load("text_utils", false)  -- 直接使用text_utils
    excel_module = module_loader.load("excel_module", false)
    -- mark_module已合并到button_module中
    word_module = module_loader.load("word_module", false)  -- 加载word模块
  end

  -- 初始化按钮状态
  button_module.init_button_states()

  -- 返回初始化后的按钮模块



return button_module
end

-- 添加按钮状态管理
local button_states = {}

-- 初始化按钮状态管理
function button_module.init_button_states()
  button_states = {
    last_click_time = 0,
    click_cooldown = 0.1,  -- 点击冷却时间（秒）
    hover_states = {},
    active_states = {},

    is_button_ready = function(self)
      local current_time = os.clock()
      if current_time - self.last_click_time < self.click_cooldown then
        return false
      end
      self.last_click_time = current_time
      return true
    end,

    set_hover = function(self, button_id, is_hovering)
      self.hover_states[button_id] = is_hovering
    end,

    is_hovering = function(self, button_id)
      return self.hover_states[button_id] or false
    end,

    set_active = function(self, button_id, is_active)
      self.active_states[button_id] = is_active
    end,

    is_active = function(self, button_id)
      return self.active_states[button_id] or false
    end
  }
end

-- 判断点是否在矩形内的辅助函数
function button_module.is_point_in_rect(x, y, rect)
  -- 优先使用工具模块的函数
  if utils_module and utils_module.is_point_in_rect then
    return utils_module.is_point_in_rect(x, y, rect)
  end

  -- 如果工具模块未加载，使用内部实现
  return x >= rect.x and x <= rect.x + rect.w and y >= rect.y and y <= rect.y + rect.h
end

-- ============ 按钮样式相关函数 ============

-- 绘制播放按钮
function button_module.draw_play_button(button, is_playing)
  if not style_module then return end

  local color = is_playing and style_module.colors.button_pause or style_module.colors.button_play
  local text = is_playing and "暂停" or "播放"
  style_module.draw_button(button, text, color)
end

-- 绘制打标按钮
-- 绘制块标按钮（继承原打标记按钮功能）
function button_module.draw_block_mark_button(button, enabled)
  if not style_module then return end

  -- 安全获取颜色，添加备用颜色
  local button_mark_color = (style_module.colors and style_module.colors.button_mark) or {r = 0.7, g = 0.5, b = 0.2, a = 1}
  local button_disabled_color = (style_module.colors and style_module.colors.button_disabled) or {r = 0.5, g = 0.5, b = 0.5, a = 1}
  local color = enabled and button_mark_color or button_disabled_color
  style_module.draw_button(button, "块标", color)
end

-- 绘制区标按钮（暂时只有界面，功能后续添加）
function button_module.draw_region_mark_button(button, enabled)
  if not style_module then return end

  -- 安全获取颜色，添加备用颜色
  local button_mark_color = (style_module.colors and style_module.colors.button_mark) or {r = 0.7, g = 0.5, b = 0.2, a = 1}
  local button_disabled_color = (style_module.colors and style_module.colors.button_disabled) or {r = 0.5, g = 0.5, b = 0.5, a = 1}
  local color = enabled and button_mark_color or button_disabled_color
  style_module.draw_button(button, "区标", color)
end

-- 绘制EXCEL按钮
function button_module.draw_excel_button(button, enabled)
  if not style_module then return end

  local color = enabled and style_module.colors.button_excel or style_module.colors.button_disabled
  style_module.draw_button(button, "写入报告", color)
end

-- 绘制速率相关按钮
function button_module.draw_rate_buttons(rate_minus_button, rate_display_area, rate_plus_button, rate_reset_button, current_playrate)
  if not style_module then return end

  -- 减速按钮
  if rate_minus_button then
    style_module.draw_button(rate_minus_button, "-", style_module.colors.button_rate_minus)
  end

  -- 速率显示
  if rate_display_area then
    -- 确保current_playrate有有效值
    local safe_playrate = current_playrate or 1.0
    if type(safe_playrate) ~= "number" then
      safe_playrate = 1.0
    end

    local formatted_rate = string.format("%.1fx", safe_playrate)
    style_module.set_color(style_module.colors.text)
    local rate_text_w = gfx.measurestr(formatted_rate)
    gfx.x, gfx.y = rate_display_area.x + (rate_display_area.w - rate_text_w) / 2, rate_display_area.y + 7
    gfx.drawstr(formatted_rate)
  end

  -- 加速按钮
  if rate_plus_button then
    style_module.draw_button(rate_plus_button, "+", style_module.colors.button_rate_plus)
  end

  -- 重置速率按钮
  if rate_reset_button then
    style_module.draw_button(rate_reset_button, "1.0x", style_module.colors.button_rate)
  end
end

-- 绘制文档和剪贴板按钮
function button_module.draw_document_buttons(document_button, clipboard_button)
  if not style_module then return end

  -- 使用增强金属质感按钮绘制读取文档按钮
  style_module.draw_enhanced_metal_button(document_button, "读取文档", style_module.colors.button_document)
  -- 使用原来的金属效果按钮绘制剪贴板按钮
  style_module.draw_button(clipboard_button, "读取剪贴板", style_module.colors.button_clipboard)
end

-- 绘制开/AU/区名/文名/轨色/分轨按钮
function button_module.draw_open_au_buttons(open_csv_button, au_button, region_name_button, file_name_button, track_color_button, track_split_button)
  if not style_module then return end

  -- 绘制开按钮（打开CSV）
  if open_csv_button then
    style_module.draw_button(open_csv_button, "开", style_module.colors.button_open_csv)
  end

  -- 绘制AU按钮
  if au_button then
    style_module.draw_button(au_button, "AU", style_module.colors.button_au)
  end

  -- 绘制区名按钮
  if region_name_button then
    style_module.draw_button(region_name_button, "区名", style_module.colors.button_region_name)
  end

  -- 绘制文名按钮
  if file_name_button then
    style_module.draw_button(file_name_button, "文名", style_module.colors.button_file_name)
  end

  -- 绘制轨色按钮
  if track_color_button then
    style_module.draw_button(track_color_button, "轨色", style_module.colors.button_track_color)
  end

  -- 绘制分轨按钮
  if track_split_button then
    style_module.draw_button(track_split_button, "分轨", style_module.colors.button_track_split)
  end
end

-- 绘制对轨按钮
function button_module.draw_track_align_button(track_align_button, is_track_align_enabled)
  if not style_module then return end

  -- 确定按钮颜色 - 使用与其他按钮一致的样式
  local color
  if is_track_align_enabled then
    -- 打开时使用绿色
    color = style_module.colors.button_track_split
  else
    -- 关闭时使用灰色
    color = style_module.colors.button_disabled
  end

  -- 使用统一的按钮绘制函数
  style_module.draw_button(track_align_button, "轨", color)
end

-- 绘制章节按钮
function button_module.draw_chapter_button(chapter_button, is_chapter_list_visible)
  if not style_module then return end

  -- 确定按钮颜色 - 使用与其他按钮一致的样式
  local color = style_module.colors.button

  -- 如果章节列表可见，使用不同颜色来指示状态
  if is_chapter_list_visible then
    color = {
      r = 0.3,
      g = 0.7,
      b = 0.4,
      a = 1
    }
  end

  -- 使用统一的按钮绘制函数
  style_module.draw_button(chapter_button, "章", color)
end

-- 绘制字体大小调整按钮和显示
function button_module.draw_font_buttons(font_size_label, font_decrease_button, font_increase_button, font_size_display, current_font_size)
  if not style_module then return end

  -- 绘制字体大小标签
  style_module.set_color(style_module.colors.text)
  gfx.x, gfx.y = font_size_label.x, font_size_label.y + 5
  gfx.drawstr("字体大小:")

  -- 绘制字体缩小按钮
  style_module.draw_button(font_decrease_button, "-", style_module.colors.button_font)

  -- 绘制字体放大按钮
  style_module.draw_button(font_increase_button, "+", style_module.colors.button_font)

  -- 显示当前字体大小 - 确保直接显示传入的字体大小
  style_module.set_color(style_module.colors.input_bg)
  gfx.rect(font_size_display.x, font_size_display.y, font_size_display.w, font_size_display.h)
  style_module.set_color(style_module.colors.text)

  -- 直接使用传入的current_font_size，确保显示的值与实际使用的值一致
  local size_text = tostring(current_font_size)
  local size_text_w = gfx.measurestr(size_text)
  gfx.x, gfx.y = font_size_display.x + (font_size_display.w - size_text_w) / 2, font_size_display.y + 5
  gfx.drawstr(size_text)
end

-- 绘制输入区域
function button_module.draw_input_area(input, text, placeholder, label, label_y_offset)
  if not style_module then return end

  -- 绘制标签
  if label then
    style_module.set_color(style_module.colors.text)
    gfx.x, gfx.y = input.x, input.y - (label_y_offset or 20)
    gfx.drawstr(label)
  end

  -- 绘制输入区域
  style_module.draw_input(input, text, placeholder)
end

-- 绘制处理建议输入框和下拉按钮
function button_module.draw_suggestion_input(input, text, placeholder, label)
  if not style_module then return end

  -- 绘制标签
  if label then
    style_module.set_color(style_module.colors.text)
    gfx.x, gfx.y = input.x, input.y - 20
    gfx.drawstr(label)
  end

  -- 绘制输入框外边框（深色）
  style_module.set_color({r = 0.05, g = 0.05, b = 0.05, a = 1})
  gfx.rect(input.x, input.y, input.w, input.h)

  -- 绘制输入框内部背景
  style_module.set_color(style_module.colors.input_bg)
  gfx.rect(input.x + 1, input.y + 1, input.w - 2, input.h - 2)

  -- 绘制金属效果的高光渐变（上半部分）
  local gradient_height = input.h / 4
  for i = 0, gradient_height do
    local alpha = 0.2 * (1 - i / gradient_height)  -- 从顶部开始逐渐减小透明度
    style_module.set_color({r = 1, g = 1, b = 1, a = alpha})
    gfx.rect(input.x + 1, input.y + 1 + i, input.w - 2, 1)
  end

  -- 绘制文本
  style_module.set_color(style_module.colors.text)

  -- 估算文字高度（通常字体高度约为当前字体大小的0.8）
  -- 添加更强的防御性编程
  local _, _, raw_font_size = gfx.getfont()
  -- 确保font_size有一个有效值，如果gfx.getfont()失败则使用默认值
  local font_size = raw_font_size or (style_module and style_module.font_size) or 18
  local text_h = font_size * 0.8

  -- 计算垂直居中位置
  local center_y = input.y + (input.h - text_h) / 2

  gfx.x, gfx.y = input.x + 5, center_y

  -- 避免文本超出输入框区域
  local max_text_width = input.w - 10
  local text_to_draw = text ~= "" and text or placeholder

  -- 测量文本宽度并截断过长的文本
  local text_width = gfx.measurestr(text_to_draw)
  if text_width > max_text_width then
    -- 简单截断处理，添加省略号
    local truncated_text = ""
    local test_text = text_to_draw
    while gfx.measurestr(test_text .. "...") > max_text_width and #test_text > 1 do
      test_text = string.sub(test_text, 1, #test_text - 1)
    end
    text_to_draw = test_text .. "..."
  end

  gfx.drawstr(text_to_draw)

  -- 绘制下拉按钮指示器（小三角形）
  local triangle_size = 6
  local triangle_x = input.x + input.w - 15
  local triangle_y = input.y + (input.h - triangle_size) / 2

  -- 绘制下拉指示器的小三角形
  style_module.set_color(style_module.colors.text)
  gfx.triangle(
    triangle_x, triangle_y,
    triangle_x + triangle_size, triangle_y,
    triangle_x + triangle_size/2, triangle_y + triangle_size
  )
end

-- 绘制下拉菜单
function button_module.draw_suggestion_dropdown(input, options, hover_idx)
  if not style_module then return end

  -- 计算下拉列表位置和大小
  local dropdown_x = input.x
  local dropdown_y = input.y + input.h

  -- 计算下拉菜单宽度（不再添加箭头空间）
  local max_option_width = 0
  for _, option in ipairs(options) do
    local option_width = gfx.measurestr(option) + 10  -- 减少右边距，因为不再需要箭头空间
    max_option_width = math.max(max_option_width, option_width)
  end

  -- 确保下拉菜单宽度不小于最小宽度且不超过输入框宽度
  local dropdown_w = math.min(input.w, math.max(100, max_option_width))
  local dropdown_h = #options * 20 + 5  -- 每项20像素高加上底部间距

  -- 检查界面边界
  local screen_w, screen_h = gfx.w, gfx.h

  -- 确保下拉菜单不会超出屏幕右侧
  if dropdown_x + dropdown_w > screen_w then
    dropdown_x = screen_w - dropdown_w
  end

  -- 确保下拉菜单不会超出屏幕底部
  if dropdown_y + dropdown_h > screen_h then
    -- 如果下方空间不足，则尝试在输入框上方显示
    if input.y > dropdown_h then
      dropdown_y = input.y - dropdown_h
    else
      -- 如果上下都不够，则限制高度使其不超出底部
      dropdown_h = screen_h - dropdown_y - 5
    end
  end

  -- 绘制下拉列表外边框（深色）
  style_module.set_color({r = 0.05, g = 0.05, b = 0.05, a = 1})
  gfx.rect(dropdown_x, dropdown_y, dropdown_w, dropdown_h)

  -- 绘制下拉列表内部背景（深色）
  style_module.set_color(style_module.colors.dropdown_bg)
  gfx.rect(dropdown_x + 1, dropdown_y + 1, dropdown_w - 2, dropdown_h - 2)

  -- 绘制金属效果的高光渐变（上半部分）
  local gradient_height = dropdown_h / 8
  for i = 0, gradient_height do
    local alpha = 0.3 * (1 - i / gradient_height)  -- 从顶部开始逐渐减小透明度
    style_module.set_color({r = 1, g = 1, b = 1, a = alpha})
    gfx.rect(dropdown_x + 1, dropdown_y + 1 + i, dropdown_w - 2, 1)
  end

  -- 绘制选项
  for i, option in ipairs(options) do
    -- 检查是否在可视区域内
    local option_y = dropdown_y + (i-1) * 20
    if option_y < dropdown_y + dropdown_h then
      -- 检查鼠标是否悬停在此选项上
      if i == hover_idx then
        -- 绘制悬停高亮背景（使用金属效果）
        local option_rect = {
          x = dropdown_x + 2,
          y = option_y,
          w = dropdown_w - 4,
          h = 20
        }

        -- 使用金属高亮函数
        style_module.draw_metallic_highlight(
          option_rect.x,
          option_rect.y,
          option_rect.w,
          option_rect.h,
          style_module.colors.button,
          true
        )
      end

      -- 绘制选项文本
      style_module.set_color(style_module.colors.text)

      -- 设置字体
      gfx.setfont(1, style_module.font_name, style_module.font_size)

      -- 估算文字高度
      local _, _, font_size = gfx.getfont()
      font_size = font_size or style_module.font_size or 20
      local text_h = font_size * 0.9  -- 增加系数，使文字更加居中

      -- 计算垂直居中位置
      local center_y = option_y + (20 - text_h) / 2 - 1  -- 微调垂直位置，使文字更加居中

      gfx.x, gfx.y = dropdown_x + 5, center_y
      gfx.drawstr(option)
    end
  end
end

-- 检查鼠标是否在下拉菜单选项上
function button_module.check_dropdown_hover(input, options, mouse_x, mouse_y)
  local dropdown_x = input.x
  local dropdown_y = input.y + input.h
  local dropdown_w = input.w
  local dropdown_h = #options * 20 + 5

  -- 应用与draw_suggestion_dropdown相同的边界检查
  local screen_w, screen_h = gfx.w, gfx.h

  -- 确保下拉菜单不会超出屏幕右侧
  if dropdown_x + dropdown_w > screen_w then
    dropdown_x = screen_w - dropdown_w
  end

  -- 确保下拉菜单不会超出屏幕底部
  if dropdown_y + dropdown_h > screen_h then
    -- 如果下方空间不足，则尝试在输入框上方显示
    if input.y > dropdown_h then
      dropdown_y = input.y - dropdown_h
    else
      -- 如果上下都不够，则限制高度
      dropdown_h = screen_h - dropdown_y - 5
    end
  end

  -- 检查鼠标是否在下拉菜单区域内
  if mouse_x >= dropdown_x and mouse_x <= dropdown_x + dropdown_w and
     mouse_y >= dropdown_y and mouse_y <= dropdown_y + dropdown_h then
    -- 计算鼠标悬停在哪个选项上
    local rel_y = mouse_y - dropdown_y
    local hover_idx = math.floor(rel_y / 20) + 1

    -- 确保索引有效
    if hover_idx >= 1 and hover_idx <= #options then
      return hover_idx
    end
  end

  return -1  -- 未悬停在任何选项上
end

-- ============ 按钮行为相关函数 ============

-- 从 button_helpers.lua 合并的功能

-- 打开CSV文件
function button_module.open_csv_file()
    -- 获取REAPER的Scripts目录
    local reaper_path = r.GetResourcePath()
    -- 构建CSV文件路径
    local csv_path = reaper_path .. "/Scripts/审听报告.csv"

    -- 检查文件是否存在（只读模式）
    local file = io.open(csv_path, "r")
    if file then
        -- 文件存在，关闭句柄
        file:close()

        -- 使用JS API打开文件（如果可用）
        if r.JS_ShellExecute then
            -- 使用JS_ShellExecute打开文件，SW_SHOWNORMAL(1)参数确保正常显示窗口
            r.JS_ShellExecute(csv_path:gsub("/", "\\"), "", "", "open", 1)
            if utils_module and utils_module.info then utils_module.info("正在打开审听报告.csv (使用JS_ShellExecute)") end
            return "正在打开审听报告.csv (使用JS_ShellExecute)"
        else
            -- 降级到标准方法，但使用0参数隐藏CMD窗口
            r.ExecProcess('cmd.exe /c start "" "' .. csv_path:gsub("/", "\\") .. '"', 0)
            if utils_module and utils_module.info then utils_module.info("正在打开审听报告.csv") end
            return "正在打开审听报告.csv"
        end
    else
        -- 文件不存在，显示错误消息
        r.MB("无法找到审听报告.csv文件，请先使用写入功能创建报告。", "文件不存在", 0)
        if utils_module and utils_module.warn then utils_module.warn("错误：审听报告.csv文件不存在") end
        return "错误：审听报告.csv文件不存在"
    end
end

-- 运行AU脚本
function button_module.run_au_script()
    -- 构建完整的脚本路径
    -- script_dir 应该已经在 button_module.lua 的顶部定义了
    local au_script_path = script_dir .. "JHKAU.lua"

    -- 检查脚本是否存在
    local file = io.open(au_script_path, "r")
    if file then
        file:close()

        -- 如果有JS API可用，使用JS_ShellExecute运行脚本
        if r.JS_ShellExecute then
            -- 使用JS_ShellExecute以"open"方式打开Lua脚本，REAPER会自动用Lua解释器执行它
            -- 使用SW_SHOWNORMAL(1)参数确保正常显示窗口，但不显示CMD窗口
            r.JS_ShellExecute(au_script_path, "", "", "open", 1)
            if utils_module and utils_module.info then utils_module.info("已执行JHKAU.lua脚本 (使用JS_ShellExecute)") end
            return "已执行JHKAU.lua脚本 (使用JS_ShellExecute)"
        else
            -- 降级到直接执行脚本
            dofile(au_script_path)
            if utils_module and utils_module.info then utils_module.info("已执行JHKAU.lua脚本") end
            return "已执行JHKAU.lua脚本"
        end
    else
        if utils_module and utils_module.error then utils_module.error("错误：JHKAU.lua脚本不存在，请确保脚本位于脚本目录下") end
        return "错误：JHKAU.lua脚本不存在，请确保脚本位于脚本目录下"
    end
end


-- 处理读取文档按钮点击
function button_module.handle_document_button(callbacks)
  -- 尝试使用word_module解析docx文件
  if word_module then
    -- 使用REAPER的GetUserFileNameForRead选择docx文件
    local retval, file_path = r.GetUserFileNameForRead("", "选择Word文档", "docx")

    if retval then
      -- 解析docx文件
      local text_content = word_module.parse_docx(file_path)

      if text_content and type(text_content) == "string" and not text_content:match("^无法") then
        -- 将解析后的文本内容传递给回调函数
        if callbacks and callbacks.handle_text_content then
          -- 直接将文本内容传递给自定义处理函数
          callbacks.handle_text_content(text_content)
          return "已读取Word文档内容"
        elseif callbacks and callbacks.parse_sentences and callbacks.extract_cv_role_pairs then
          -- 使用传统的处理方法
          -- 将文本内容设置到clipboard_text中
          if callbacks.set_clipboard_text then
            callbacks.set_clipboard_text(text_content)
          end

          -- 解析句子和CV角色对
          callbacks.parse_sentences()
          callbacks.extract_cv_role_pairs()
          return "已读取Word文档内容"
        end
      else
        -- 显示错误消息
        local error_msg = "无法解析Word文档"
        if type(text_content) == "string" then
          error_msg = text_content
        end
        r.MB(error_msg, "文档读取错误", 0)
        return error_msg
      end
    else
      return "用户取消了文件选择"
    end
  else
    -- 降级到传统的文本文件读取方式
    if callbacks and callbacks.read_text_file and
       callbacks.parse_sentences and callbacks.extract_cv_role_pairs then
      callbacks.read_text_file()
      callbacks.parse_sentences()
      callbacks.extract_cv_role_pairs()
      return "已读取文本文件内容"
    end
  end

  return "处理文档按钮失败：回调函数未设置或模块未加载"
end

-- 处理剪贴板按钮点击
function button_module.handle_clipboard_button(callbacks)
  if callbacks and callbacks.get_clipboard and
     callbacks.parse_sentences and callbacks.extract_cv_role_pairs then
    callbacks.get_clipboard()
    callbacks.parse_sentences()
    callbacks.extract_cv_role_pairs()
    return "已读取剪贴板内容"
  end
  return "处理剪贴板按钮失败：回调函数未设置"
end

-- 处理CV角色列表点击
function button_module.handle_cv_role_click(cv_role_pairs, clicked_index, state)
  if not cv_role_pairs or not clicked_index or not state then
    return "参数错误"
  end

  if clicked_index > 0 and cv_role_pairs[clicked_index] then
    local selected = cv_role_pairs[clicked_index]
    state.selected_cv = selected.cv
    state.selected_role = selected.role

    -- 更新按钮状态
    button_states:set_active("cv_role_" .. clicked_index, true)

    return string.format("已选择: 角色: %s - CV: %s", state.selected_role, state.selected_cv)
  end

  return ""
end

-- 处理错误输入区域点击
function button_module.handle_error_input_click(current_error_note)
  local ok, user_input = r.GetUserInputs("输入错误描述", 1, "错误描述:", current_error_note or "")
  if ok then
    return user_input
  end
  return current_error_note or ""
end

-- 处理正确表达输入区域点击
function button_module.handle_correct_input_click(current_correct_note)
  local ok, user_input = r.GetUserInputs("输入正确表达", 1, "正确表达:", current_correct_note or "")
  if ok then
    return user_input
  end
  return current_correct_note or ""
end

-- 处理集数输入区域点击
function button_module.handle_episode_input_click(current_episode_number, auto_episode)
  local default_value = auto_episode or current_episode_number or ""
  local ok, user_input = r.GetUserInputs("输入集数", 1, "集数:", default_value)
  if ok then
    return user_input
  end
  return current_episode_number or ""
end

-- 处理写入报告按钮点击
function button_module.handle_excel_button_click(params)
  -- 确认excel_module已正确加载
  if not excel_module or type(excel_module) ~= "table" or not excel_module.handle_excel_button_click then
    return "报告模块未正确加载，无法写入数据"
  end

  local result = excel_module.handle_excel_button_click(params)

  -- 根据返回消息显示不同类型的弹窗提示
  if result:find("已写入报告文件") then
    -- 写入成功的情况
    local message = "报告数据写入成功！\n\n"
    message = message .. "集数: " .. (params.episode_number ~= "" and params.episode_number or "未指定") .. "\n"
    message = message .. "角色: " .. params.selected_role .. "\n"
    message = message .. "CV: " .. params.selected_cv .. "\n"
    message = message .. "错误时间: " .. (params.marked_relative_time ~= "" and params.marked_relative_time or "未标记") .. "\n"
    message = message .. "处理建议: " .. (params.process_suggestion ~= "" and params.process_suggestion or "未指定")

    r.MB(message, "写入成功", 0)
  elseif result:find("写入操作太频繁") then
    -- 操作过于频繁的情况
    r.MB("操作过于频繁，请稍后再试。", "请稍候", 0)
  elseif result:find("CSV文件被占用") then
    -- 文件被占用的情况已在excel_module中处理弹窗，这里不需要重复显示
    -- 但仍保留代码以便未来可能的调整
  elseif result:find("写入验证失败") then
    -- 写入验证失败的情况
    r.MB("数据写入过程中发生问题，请检查CSV文件是否已被保存并可访问。\n\n" .. result, "写入验证失败", 0)
  elseif result ~= "" then
    -- 其他错误情况
    r.MB("写入报告时发生错误：\n\n" .. result, "写入失败", 0)
  end

  return result
end

-- 处理播放/暂停按钮点击
function button_module.handle_play_button_click()
  -- 确认playback_control已正确加载
  if not playback_control or type(playback_control) ~= "table" or not playback_control.toggle_play then
    return "播放控制模块未正确加载", false
  end

  local is_playing = playback_control.toggle_play()
  return is_playing and "播放中" or "已暂停", is_playing
end

-- 处理减速按钮点击
function button_module.handle_rate_minus_button_click()
  -- 确认playback_control已正确加载
  if not playback_control or type(playback_control) ~= "table" or not playback_control.adjust_playrate then
    return "播放控制模块未正确加载", 1.0
  end

  local new_rate = playback_control.adjust_playrate(-0.1)
  return "播放速率: " .. playback_control.format_playrate(new_rate) .. "x", new_rate
end

-- 处理加速按钮点击
function button_module.handle_rate_plus_button_click()
  -- 确认playback_control已正确加载
  if not playback_control or type(playback_control) ~= "table" or not playback_control.adjust_playrate then
    return "播放控制模块未正确加载", 1.0
  end

  local new_rate = playback_control.adjust_playrate(0.1)
  return "播放速率: " .. playback_control.format_playrate(new_rate) .. "x", new_rate
end

-- 处理重置速率按钮点击（重置为1.0）
function button_module.handle_rate_reset_button_click()
  -- 确认playback_control已正确加载
  if not playback_control or type(playback_control) ~= "table" or not playback_control.reset_playrate then
    return "播放控制模块未正确加载", 1.0
  end

  local new_rate = playback_control.reset_playrate()
  -- 确保返回正确的速率值
  return "播放速率已重置为: 1.0x", 1.0
end



-- 从 mark_function.lua 合并的功能

-- 添加标记到项目
local function add_marker_to_project(position, name)
  -- 获取当前项目
  local project = 0 -- 0表示当前活动项目

  -- 获取轨道颜色
  local track_color = 0  -- 默认颜色

  -- 尝试从当前选中的音频块获取轨道颜色
  local selected_item = r.GetSelectedMediaItem(0, 0)
  if selected_item then
    local track = r.GetMediaItem_Track(selected_item)
    if track then
      track_color = r.GetTrackColor(track)
    end
  end

  -- 如果没有选中音频块，尝试从光标位置获取轨道颜色
  if track_color == 0 then
    -- 获取光标位置的轨道
    local track_count = r.CountTracks(0)
    for i = 0, track_count - 1 do
      local track = r.GetTrack(0, i)
      if track then
        -- 检查轨道是否被选中或包含光标位置的音频块
        local item_count = r.CountTrackMediaItems(track)
        for j = 0, item_count - 1 do
          local item = r.GetTrackMediaItem(track, j)
          local item_start = r.GetMediaItemInfo_Value(item, "D_POSITION")
          local item_end = item_start + r.GetMediaItemInfo_Value(item, "D_LENGTH")

          -- 如果光标位置在这个音频块范围内
          if position >= item_start and position <= item_end then
            track_color = r.GetTrackColor(track)
            break
          end
        end
        if track_color ~= 0 then break end
      end
    end
  end

  -- 添加标记，使用轨道颜色
  local marker_idx = r.AddProjectMarker2(project, false, position, 0, name, -1, track_color)

  if marker_idx >= 0 then
    return true, marker_idx
  else
    return false, "添加标记失败"
  end
end

-- 添加标记到音频块
local function add_marker_to_item(item, position, name)
  if not item then
    return false, "无效的音频块"
  end

  -- 获取当前活动的take
  local take = r.GetActiveTake(item)
  if not take then
    return false, "无法获取音频块的take"
  end

  -- 获取项目起始位置
  local item_start = r.GetMediaItemInfo_Value(item, "D_POSITION")

  -- 计算相对位置
  local relative_pos = position - item_start

  -- 添加标记到take
  local marker_idx = r.SetTakeMarker(take, -1, name, relative_pos, 1)

  if marker_idx >= 0 then
    return true, marker_idx
  else
    return false, "添加音频块标记失败"
  end
end

-- 主标记函数 - 现在是 button_module 的一部分
function button_module.mark_item(role, cv, error_note, state)
  -- 参数验证
  if not role or role == "" then
    return false, "角色不能为空"
  end

  if not cv or cv == "" then
    return false, "CV不能为空"
  end

  if not error_note or error_note == "" then
    return false, "处理建议不能为空"
  end

  -- 获取当前选中的项目
  local selected_item = r.GetSelectedMediaItem(0, 0)

  if not selected_item then
    return false, "请先选择一个音频块!"
  end

  -- 获取选中项目的位置
  local item_pos = r.GetMediaItemInfo_Value(selected_item, "D_POSITION")

  -- 获取项目内部时间
  local take = r.GetActiveTake(selected_item)
  local item_start = r.GetMediaItemInfo_Value(selected_item, "D_POSITION")
  local cursor_pos = r.GetCursorPosition()
  local relative_pos = cursor_pos - item_start

  -- 确保光标位置在项目范围内
  if relative_pos < 0 then
    relative_pos = 0
  end

  local item_length = r.GetMediaItemInfo_Value(selected_item, "D_LENGTH")
  if relative_pos > item_length then
    relative_pos = item_length
  end

  -- 格式化时间 (确保 utils_module 已在 button_module.init 中加载)
  if not utils_module or not utils_module.format_time then
    r.ShowConsoleMsg("错误: utils_module.format_time 在 button_module.mark_item 中不可用!\n")
    return false, "内部错误：时间格式化功能不可用"
  end
  local formatted_time = utils_module.format_time(cursor_pos)
  local formatted_relative_time = utils_module.format_time(relative_pos)

  -- 构建标记名称，不包含选择的文本内容
  local marker_name = string.format("[%s-%s] %s (错误时间: %s)",
                                   role,
                                   cv,
                                   error_note,
                                   formatted_relative_time)

  -- 添加项目标记
  local project_success, project_result = add_marker_to_project(cursor_pos, marker_name)

  -- 添加音频块标记
  local item_success, item_result = add_marker_to_item(selected_item, cursor_pos, marker_name)

  if project_success then
    local message = ""

    -- 只在音频块标记失败时添加提示信息
    if not item_success then
      message = "注意: 音频块标记添加失败: " .. tostring(item_result)
    end

    return true, message, project_result, formatted_relative_time, relative_pos
  else
    -- 项目标记失败
    return false, "标记错误失败: " .. tostring(project_result)
  end
end

-- 公共文本清理函数
function button_module.clean_selected_text(selected_text_content)
  if not selected_text_content or selected_text_content == "" then
    return ""
  end

  -- 清理文本中的格式标记和角色信息
  if text_utils and text_utils.clean_cv_role_tags then
    selected_text_content = text_utils.clean_cv_role_tags(selected_text_content)
  end

  -- 移除角色信息【角色-CV】格式
  selected_text_content = selected_text_content:gsub("【.-】", "")

  -- 移除删除线标记内容 [x]...[/x]
  selected_text_content = selected_text_content:gsub("%[x%](.-)%[/x%]", "%1")

  -- 移除画外音标记
  selected_text_content = selected_text_content:gsub("【画外音】", "")

  -- 清理多余的空白字符（包括全角空格和特殊空白字符）
  selected_text_content = selected_text_content:gsub("[ 　%s]+", " ")  -- 替换所有空白字符为单个空格
  selected_text_content = selected_text_content:gsub("^%s+", ""):gsub("%s+$", "")  -- 移除首尾空白

  -- 确保文本是有效的UTF-8编码
  local function ensure_utf8(str)
    if not str or str == "" then return "" end

    -- 移除无效的UTF-8字节序列
    local result = ""
    local i = 1
    while i <= #str do
      local byte = string.byte(str, i)
      local char_len = 1

      -- 判断UTF-8字符长度
      if byte >= 240 then
        char_len = 4
      elseif byte >= 224 then
        char_len = 3
      elseif byte >= 192 then
        char_len = 2
      elseif byte >= 128 then
        -- 无效的UTF-8起始字节，跳过
        i = i + 1
        goto continue
      end

      -- 检查是否有足够的字节
      if i + char_len - 1 <= #str then
        local char = str:sub(i, i + char_len - 1)
        -- 验证UTF-8字符的有效性
        if char_len == 1 or (char_len > 1 and string.byte(char, 2) and string.byte(char, 2) >= 128 and string.byte(char, 2) <= 191) then
          result = result .. char
        end
      end

      i = i + char_len
      ::continue::
    end

    return result
  end

  selected_text_content = ensure_utf8(selected_text_content)

  return selected_text_content
end

-- 处理块标按钮点击（继承原打标记按钮功能）
function button_module.handle_block_mark_button_click(state)
  -- 额外检查是否已选择音频块（即使界面层已经进行了检查）
  local selected_item = r.GetSelectedMediaItem(0, 0)
  if not selected_item then
    return "请先选择一个音频块!", "", 0
  end

  -- 额外检查是否已选择角色和CV（即使界面层已经进行了检查）
  if not state.selected_cv or state.selected_cv == "" or not state.selected_role or state.selected_role == "" then
    return "请先选择角色和CV!", "", 0
  end

  -- 额外检查处理建议是否已填写
  if not state.process_suggestion or state.process_suggestion == "" then
    return "请填写处理建议!", "", 0
  end

  -- 调用原有的打标记功能
  return button_module.handle_mark_button_click(state)
end

-- 处理区标按钮点击（功能后续添加）
function button_module.handle_region_mark_button_click(state)
  -- 参数验证
  if not state.selected_role or state.selected_role == "" then
    return "角色不能为空", "", 0
  end

  if not state.selected_cv or state.selected_cv == "" then
    return "CV不能为空", "", 0
  end

  if not state.process_suggestion or state.process_suggestion == "" then
    return "处理建议不能为空", "", 0
  end

  -- 获取当前光标位置
  local cursor_pos = r.GetCursorPosition()

  -- 查找光标所在的region
  local region_count = r.CountProjectMarkers(0)

  local current_region = nil

  for i = 0, region_count - 1 do
    local retval, isrgn, pos, rgnend, name, markrgnindexnumber, color = r.EnumProjectMarkers3(0, i)

    if retval and isrgn and cursor_pos >= pos and cursor_pos <= rgnend then
      current_region = {
        pos = pos,
        rgnend = rgnend,
        name = name,
        index = markrgnindexnumber
      }
      break
    end
  end

  if not current_region then
    return "光标不在任何region内，请将光标放在region范围内", "", 0
  end

  -- 计算光标在region内的相对时间
  local relative_pos = cursor_pos - current_region.pos

  -- 确保相对位置在有效范围内
  if relative_pos < 0 then
    relative_pos = 0
  end

  local region_length = current_region.rgnend - current_region.pos
  if relative_pos > region_length then
    relative_pos = region_length
  end

  -- 格式化时间
  if not utils_module or not utils_module.format_time then
    return "内部错误：时间格式化功能不可用", "", 0
  end

  local formatted_relative_time = utils_module.format_time(relative_pos)

  -- 构建标记名称，不包含选择的文本内容
  local marker_name = string.format("[%s-%s] %s (错误时间: %s)",
                                   state.selected_role,
                                   state.selected_cv,
                                   state.process_suggestion,
                                   formatted_relative_time)

  -- 标记名称已构建

  -- 添加项目标记
  local project_success, project_result = add_marker_to_project(cursor_pos, marker_name)

  if project_success then
    return "区标添加成功", formatted_relative_time, relative_pos
  else
    return "区标添加失败: " .. tostring(project_result), "", 0
  end
end

-- 处理打标按钮点击（保留原有功能供块标按钮调用）
function button_module.handle_mark_button_click(state)
  -- 额外检查是否已选择音频块（即使界面层已经进行了检查）
  local selected_item = r.GetSelectedMediaItem(0, 0)
  if not selected_item then
    return "请先选择一个音频块!", "", 0
  end

  -- 额外检查是否已选择角色和CV（即使界面层已经进行了检查）
  if not state.selected_cv or state.selected_cv == "" or not state.selected_role or state.selected_role == "" then
    return "请先选择角色和CV!", "", 0
  end

  -- 额外检查处理建议是否已填写
  if not state.process_suggestion or state.process_suggestion == "" then
    return "请填写处理建议!", "", 0
  end

  -- 直接调用内部的mark_item函数，不再依赖外部模块
  local success, message, marker_idx, formatted_relative_time, relative_pos =
    button_module.mark_item(state.selected_role, state.selected_cv, state.process_suggestion, state)

  -- 更严格地检查标记是否成功
  if success and marker_idx and marker_idx >= 0 then
    -- 确认标记确实添加成功了（marker_idx是有效的标记索引）
    return message, formatted_relative_time or "", relative_pos or 0
  else
    -- 标记失败，返回详细的错误信息
    local error_msg = "标记添加失败: "
    if message and message ~= "" then
      error_msg = error_msg .. message
    else
      error_msg = error_msg .. "未知错误"
    end
    return error_msg, "", 0
  end
end

-- 处理文本内容区域点击
function button_module.handle_content_area_click(sentences, hover_sentence_idx, state)
  if hover_sentence_idx > 0 and hover_sentence_idx <= #sentences then
    local clicked_text = sentences[hover_sentence_idx]
    local debug_message = ""

    -- 检查是否处于多选模式（使用全局变量is_ctrl_down表示鼠标右键状态）
    -- local is_ctrl_down = (gfx.mouse_cap & 4) == 4  -- 注释掉局部变量检测，使用传入的全局变量
    if state.is_ctrl_down then
      -- 查找当前句子是否已在选择列表中
      local is_already_selected = false
      local index_in_selection = 0

      for i, idx in ipairs(state.selected_indices or {}) do
        if idx == hover_sentence_idx then
          is_already_selected = true
          index_in_selection = i
          break
        end
      end

      -- 初始化selected_texts和selected_indices（如果不存在）
      state.selected_texts = state.selected_texts or {}
      state.selected_indices = state.selected_indices or {}

      -- 如果已选中，则取消选择
      if is_already_selected then
        table.remove(state.selected_indices, index_in_selection)
        table.remove(state.selected_texts, index_in_selection)

        -- 更新组合文本
        local combined_text = #state.selected_texts > 0 and table.concat(state.selected_texts, "\n") or ""
        return combined_text, "已取消选择句子 #" .. hover_sentence_idx
      else
        -- 如果未选中，则添加到选择列表
        table.insert(state.selected_indices, hover_sentence_idx)
        table.insert(state.selected_texts, clicked_text)

        -- 只有在第一次选择或没有其他选择时更新CV和角色
        if #state.selected_indices == 1 then
          -- 使用text_utils提取角色和CV信息
          if text_utils and text_utils.extract_cv_role_from_text then
            -- 传入is_cv_role_reversed参数，确保正确解析角色和CV
            local role, cv = text_utils.extract_cv_role_from_text(clicked_text, state.is_cv_role_reversed)
            -- 函数返回的是role, cv，所以直接赋值
            state.selected_role = role
            state.selected_cv = cv
          else
            -- text_utils不可用时，只能设置默认值
            state.selected_role = "旁白"
            state.selected_cv = "旁白"
          end
        end

        -- 更新角色和CV信息
        if text_utils and text_utils.extract_cv_role_from_text then
          -- 传入is_cv_role_reversed参数，确保正确解析角色和CV
          local role, cv = text_utils.extract_cv_role_from_text(clicked_text, state.is_cv_role_reversed)
          -- 函数返回的是role, cv，所以直接赋值
          state.selected_role = role
          state.selected_cv = cv
        else
          -- text_utils不可用时，只能设置默认值
          state.selected_role = "旁白"
          state.selected_cv = "旁白"
        end

        -- 更新组合文本
        local combined_text = table.concat(state.selected_texts, "\n")
        return combined_text, "已选择句子 #" .. hover_sentence_idx
      end
    else
      -- 单选模式：清除之前的所有选择，只选择当前句子
      state.selected_indices = {hover_sentence_idx}
      state.selected_texts = {clicked_text}

      -- 更新角色和CV信息
      if text_utils and text_utils.extract_cv_role_from_text then
        -- 传入is_cv_role_reversed参数，确保正确解析角色和CV
        local role, cv = text_utils.extract_cv_role_from_text(clicked_text, state.is_cv_role_reversed)
        -- 函数返回的是role, cv，所以直接赋值
        state.selected_role = role
        state.selected_cv = cv
      else
        -- text_utils不可用时，只能设置默认值
        state.selected_role = "旁白"
        state.selected_cv = "旁白"
      end

      return clicked_text, debug_message
    end
  end

  return "", ""
end

-- 处理字体增大按钮点击
function button_module.handle_font_increase_click(current_font_size)
  -- 使用固定的字体大小调整步长，每次调整2个单位使变化更明显
  -- 特别是对于21-22号字体这样变化不明显的区间
  local step = 2

  -- 限制最大字体大小为30
  local new_size = math.min(30, current_font_size + step)
  return new_size, string.format("字体大小已调整为: %d", new_size)
end

-- 处理字体减小按钮点击
function button_module.handle_font_decrease_click(current_font_size)
  -- 使用固定的字体大小调整步长，每次调整2个单位使变化更明显
  -- 特别是对于21-22号字体这样变化不明显的区间
  local step = 2

  -- 限制最小字体大小为12
  local new_size = math.max(12, current_font_size - step)
  return new_size, string.format("字体大小已调整为: %d", new_size)
end

-- 优化按钮点击处理函数
function button_module.handle_button_click(button_id, params, callbacks)
  if not button_states.is_button_ready or not button_states:is_button_ready() then
    return "操作过于频繁，请稍后再试"
  end

  local handlers = {
    document = function()



return button_module.handle_document_button(callbacks)
    end,
    clipboard = function()



return button_module.handle_clipboard_button(callbacks)
    end,
    excel = function()



return button_module.handle_excel_button_click(params)
    end,
    mark = function()



return button_module.handle_mark_button_click(params)
    end
  }

  local handler = handlers[button_id]
  if handler then
    return handler()
  end

  return "未知的按钮类型"
end

-- 优化输入处理函数
function button_module.handle_input_click(input_type, current_value, options)
  options = options or {}

  local input_configs = {
    error = {
      title = "输入错误描述",
      prompt = "错误描述:",
      default = current_value or ""
    },
    correct = {
      title = "输入正确表达",
      prompt = "正确表达:",
      default = current_value or ""
    },
    episode = {
      title = "输入集数",
      prompt = "集数:",
      default = options.auto_episode or current_value or ""
    }
  }

  local config = input_configs[input_type]
  if not config then
    return current_value or ""
  end

  local ok, user_input = r.GetUserInputs(config.title, 1, config.prompt, config.default)
  if ok then
    return user_input
  end

  return current_value or ""
end



-- 处理打开CSV文件按钮点击事件
function button_module.handle_open_csv_button_click()
  -- 尝试打开CSV文件
  local csv_file = "审听报告.csv"

  -- 使用一个本地函数来减少upvalue数量
  local function open_file(file)
    if r.file_exists(file) then
      -- 使用JS API打开文件（如果可用）
      if r.JS_ShellExecute then
        r.JS_ShellExecute(file, "", "", "open", 1)
        return "打开文件: " .. file .. " (使用JS_ShellExecute)"
      else
        -- 降级到标准方法，但使用0参数隐藏CMD窗口
        r.ExecProcess('cmd.exe /c start "" "' .. file .. '"', 0)
        return "打开文件: " .. file
      end
    else
      return "文件不存在: " .. file
    end
  end

  return open_file(csv_file)
end

-- 处理AU按钮点击事件
function button_module.handle_au_button_click()
  -- 使用一个本地函数来减少upvalue数量
  local function run_script(script)
    if r.file_exists(script) then
      -- 使用JS API执行脚本（如果可用）
      if r.JS_ShellExecute then
        -- 使用JS_ShellExecute以"open"方式打开Lua脚本，REAPER会自动用Lua解释器执行它
        r.JS_ShellExecute(script, "", "", "open", 1)
        return "执行脚本: " .. script .. " (使用JS_ShellExecute)"
      else
        -- 降级到标准方法，但使用ExecProcess隐藏CMD窗口
        r.ExecProcess('cmd.exe /c start "" lua "' .. script .. '"', 0)
        return "执行脚本: " .. script
      end
    else
      return "脚本不存在: " .. script
    end
  end

  -- 尝试执行AU脚本
  local au_script = "JHKAU.lua"
  return run_script(au_script)
end

-- 处理区名按钮点击事件
function button_module.handle_region_name_button_click(chapters)
  -- 检查章节列表是否为空
  if not chapters or #chapters == 0 then
    r.MB("没有找到章节信息，请先加载文档。", "错误", 0)
    return "错误：没有找到章节信息"
  end

  -- 获取当前项目中的所有区间
  local regions = {}
  local region_count = r.CountProjectMarkers(0)

  for i = 0, region_count - 1 do
    local retval, isrgn, pos, rgnend, name, markrgnindexnumber, color = r.EnumProjectMarkers3(0, i)
    if retval and isrgn then
      table.insert(regions, {
        index = markrgnindexnumber,
        name = name,
        pos = pos,
        rgnend = rgnend,
        color = color
      })
    end
  end

  if #regions == 0 then
    r.MB("当前项目中没有区间。", "错误", 0)
    return "错误：当前项目中没有区间"
  end

  -- 创建章节映射表
  local chapter_map = {}
  for _, chapter in ipairs(chapters) do
    local chapter_number = chapter.number
    if chapter_number then
      -- 存储原始数字
      local number_str = tostring(chapter_number)
      chapter_map[number_str] = chapter.title

      -- 如果数字小于100，添加带前导零的版本
      if tonumber(number_str) < 100 then
        -- 添加一个前导零的版本
        chapter_map[string.format("%02d", chapter_number)] = chapter.title

        -- 添加两个前导零的版本
        chapter_map[string.format("%03d", chapter_number)] = chapter.title
      end
    end
  end

  -- 章节映射表创建完成

  -- 计数器
  local renamed_count = 0
  local not_found_count = 0

  -- 开始撤销块
  r.Undo_BeginBlock()

  -- 遍历所有区间，修改名称
  for _, region in ipairs(regions) do
    -- 提取区间名中的数字
    -- 修改正则表达式，支持多种格式：
    -- 1. 纯数字（可能有前导零）："001", "42"
    -- 2. 数字前面有空格：" 001", " 42"
    -- 3. 数字后跟空格和其他内容："001 章节名", "42 标题"
    -- 4. 数字后跟连字符和其他内容："001-章节名", "42-标题"
    local region_number = region.name:match("^0*(%d+)$") or  -- 纯数字
                          region.name:match("^%s+0*(%d+)$") or  -- 数字前有空格
                          region.name:match("^0*(%d+)%s") or -- 数字后跟空格
                          region.name:match("^%s+0*(%d+)%s") or -- 数字前后都有空格
                          region.name:match("^0*(%d+)[%-_]") or -- 数字后跟连字符或下划线
                          region.name:match("^%s+0*(%d+)[%-_]") -- 数字前有空格，后跟连字符或下划线

    if region_number then
      -- 去除前导零，将字符串转换为数字再转回字符串
      local normalized_number = tostring(tonumber(region_number))

      -- 查找对应的章节名
      local chapter_title = chapter_map[normalized_number]
      if not chapter_title then
        -- 尝试使用原始数字再次查找
        chapter_title = chapter_map[region_number]
      end

      if chapter_title then
        -- 修改区间名为章节名
        r.SetProjectMarker4(0, region.index, true, region.pos, region.rgnend, chapter_title, 0, region.color)
        renamed_count = renamed_count + 1
      else
        not_found_count = not_found_count + 1
        -- 未找到对应章节
      end
    end
  end

  -- 结束撤销块
  r.Undo_EndBlock("重命名区间为章节名", -1)

  -- 显示结果
  local message = string.format("已重命名 %d 个区间。\n", renamed_count)
  if not_found_count > 0 then
    message = message .. string.format("有 %d 个区间未找到对应的章节。", not_found_count)
  end

  r.MB(message, "区间重命名完成", 0)
  return message
end

-- 处理文名按钮点击事件
function button_module.handle_file_name_button_click(chapters)
  -- 检查章节列表是否为空
  if not chapters or #chapters == 0 then
    r.MB("没有找到章节信息，请先加载文档。", "错误", 0)
    return "错误：没有找到章节信息"
  end

  -- 获取选中的音频块数量
  local selected_item_count = r.CountSelectedMediaItems(0)
  if selected_item_count == 0 then
    r.MB("请先选择至少一个音频块。", "错误", 0)
    return "错误：未选择音频块"
  end

  -- 选中了多个音频块

  -- 开始撤销块
  r.Undo_BeginBlock()

  -- 用于统计成功和失败的计数器
  local success_count = 0
  local fail_count = 0
  local processed_items = {}

  -- 遍历所有选中的音频块
  for i = 0, selected_item_count - 1 do
    local selected_item = r.GetSelectedMediaItem(0, i)
    if selected_item then
      -- 获取音频块的文件名
      local take = r.GetActiveTake(selected_item)
      if take then
        local source = r.GetMediaItemTake_Source(take)
        if source then
          local filename = r.GetMediaSourceFileName(source, "")
          if filename and filename ~= "" then
            -- 提取文件名（不包括路径和扩展名）
            local file_basename = filename:match("([^/\\]+)%.[^.]+$") or filename:match("([^/\\]+)$") or filename

            -- 记录处理的项目
            table.insert(processed_items, {
              item = selected_item,
              take = take,
              filename = file_basename
            })
          else
            fail_count = fail_count + 1
            -- 无法获取文件名
          end
        else
          fail_count = fail_count + 1
          -- 无法获取源文件
        end
      else
        fail_count = fail_count + 1
        -- 无效的take
      end
    end
  end

  -- 如果没有有效的音频块，则显示错误并返回
  if #processed_items == 0 then
    r.Undo_EndBlock("尝试重命名音频块", -1)
    r.MB("没有找到有效的音频块。", "错误", 0)
    return "错误：没有有效的音频块"
  end

  -- 现在我们已经收集了所有需要处理的音频块，直接进入处理阶段

  -- 创建章节映射表
  local chapter_map = {}
  for _, chapter in ipairs(chapters) do
    local chapter_number = chapter.number
    if chapter_number then
      -- 存储原始数字
      local number_str = tostring(chapter_number)
      chapter_map[number_str] = chapter.title

      -- 如果数字小于100，添加带前导零的版本
      if tonumber(number_str) < 100 then
        -- 添加一个前导零的版本
        chapter_map[string.format("%02d", chapter_number)] = chapter.title

        -- 添加两个前导零的版本
        chapter_map[string.format("%03d", chapter_number)] = chapter.title
      end
    end
  end

  -- 章节映射表创建完成

  -- 处理每个音频块
  for _, item_data in ipairs(processed_items) do
    local current_file_basename = item_data.filename

    -- 从文件名中提取数字
    -- 尝试多种模式来提取章节编号
    local current_file_number = nil

    -- 1. 尝试匹配完全由数字组成的文件名（可能有前导零）
    current_file_number = current_file_basename:match("^0*(%d+)$")

    -- 2. 如果上面的匹配失败，尝试匹配文件名中的数字部分
    if not current_file_number then
      -- 尝试匹配各种常见的章节编号模式
      current_file_number = current_file_basename:match("第(%d+)章") or
                            current_file_basename:match("Chapter(%d+)") or
                            current_file_basename:match("[Cc]h(%d+)") or
                            current_file_basename:match("(%d+)[章话集]") or
                            current_file_basename:match("^(%d+)[_-]") or
                            current_file_basename:match("[_-](%d+)[_-]") or
                            current_file_basename:match("[_-](%d+)$")
    end

    -- 3. 如果上面的匹配都失败，尝试匹配文件名中的任何数字序列
    if not current_file_number then
      current_file_number = current_file_basename:match("(%d+)")
    end

    -- 如果提取到了数字
    if current_file_number then
      -- 去除前导零，将字符串转换为数字再转回字符串
      local normalized_number = tostring(tonumber(current_file_number))

      -- 查找对应的章节名
      local chapter_title = chapter_map[normalized_number]
      if not chapter_title then
        -- 尝试使用原始数字再次查找
        chapter_title = chapter_map[current_file_number]
      end

      if chapter_title then
        -- 修改音频块的名称
        r.GetSetMediaItemTakeInfo_String(item_data.take, "P_NAME", chapter_title, true)
        success_count = success_count + 1
        -- 成功重命名
      else
        fail_count = fail_count + 1
        -- 未找到对应章节
      end
    else
      fail_count = fail_count + 1
      -- 无法提取数字
    end
  end

  -- 结束撤销块
  r.Undo_EndBlock("重命名音频块为章节名", -1)

  -- 显示结果
  local message = string.format("处理了 %d 个音频块，成功: %d，失败: %d", #processed_items, success_count, fail_count)

  r.MB(message, "音频块重命名完成", 0)
  return message
end

-- 处理轨色按钮点击事件
function button_module.handle_track_color_button_click(sentences, cv_role_pairs, is_cv_role_reversed)
  -- 检查参数
  if not sentences or #sentences == 0 then
    r.MB("没有找到文本内容，请先加载文档。", "错误", 0)
    return "错误：没有找到文本内容"
  end

  -- 检查CV角色列表是否有效
  local cv_role_valid = cv_role_pairs and #cv_role_pairs > 0

  -- 开始撤销块
  r.Undo_BeginBlock()

  -- 用于统计成功和失败的计数器
  local success_count = 0
  local processed_count = 0
  local created_count = 0  -- 新增：创建的轨道计数
  local renamed_count = 0  -- 新增：重命名的轨道计数

  -- 创建CV-颜色映射表和颜色出现次数统计
  local cv_colors = {}
  local cv_color_counts = {}

  -- 使用与角色CV列表相同的分类逻辑
  local cv_categories = {}
  local cv_order = {}
  local cv_names = {}

  -- 先获取CV角色分类，就像角色CV列表那样
  if cv_role_pairs and #cv_role_pairs > 0 then
    -- 使用与mark.lua中get_cv_role_categories相同的逻辑
    for i, pair in ipairs(cv_role_pairs) do
      local cv_name = pair.cv
      local role_name = pair.role

      if cv_name and cv_name ~= "" then
        cv_names[cv_name] = true

        if not cv_categories[cv_name] then
          cv_categories[cv_name] = {}
          table.insert(cv_order, cv_name)
        end
        table.insert(cv_categories[cv_name], {role = role_name, index = i})
      end
    end
  end

  -- 遍历所有句子，查找带有颜色标记的CV段落
  for _, sentence in ipairs(sentences) do
    -- 遍历所有已知的CV角色对，查找匹配的角色标签
    for _, pair in ipairs(cv_role_pairs) do
      local cv_name = pair.cv
      local role_name = pair.role

      if cv_name and cv_name ~= "" and role_name and role_name ~= "" then
        -- 构建角色标签的匹配模式：【角色-CV】或【CV-角色】（根据is_cv_role_reversed）
        local pattern1, pattern2
        if is_cv_role_reversed then
          -- 交换位置：【CV-角色】
          pattern1 = "【[^】]*" .. cv_name:gsub("([%^%$%(%)%%%.%[%]%*%+%-%?])", "%%%1") .. "[^】]*-[^】]*" .. role_name:gsub("([%^%$%(%)%%%.%[%]%*%+%-%?])", "%%%1") .. "[^】]*】"
          pattern2 = "【[^】]*" .. role_name:gsub("([%^%$%(%)%%%.%[%]%*%+%-%?])", "%%%1") .. "[^】]*-[^】]*" .. cv_name:gsub("([%^%$%(%)%%%.%[%]%*%+%-%?])", "%%%1") .. "[^】]*】"
        else
          -- 默认：【角色-CV】
          pattern1 = "【[^】]*" .. role_name:gsub("([%^%$%(%)%%%.%[%]%*%+%-%?])", "%%%1") .. "[^】]*-[^】]*" .. cv_name:gsub("([%^%$%(%)%%%.%[%]%*%+%-%?])", "%%%1") .. "[^】]*】"
          pattern2 = "【[^】]*" .. cv_name:gsub("([%^%$%(%)%%%.%[%]%*%+%-%?])", "%%%1") .. "[^】]*-[^】]*" .. role_name:gsub("([%^%$%(%)%%%.%[%]%*%+%-%?])", "%%%1") .. "[^】]*】"
        end

        -- 尝试匹配两种可能的模式
        local cv_start, cv_end = sentence:find(pattern1)
        if not cv_start then
          cv_start, cv_end = sentence:find(pattern2)
        end

        if cv_start then
          -- 在角色标记前查找最近的颜色标记
          local before_cv = sentence:sub(1, cv_start - 1)
          local colors_before = {}
          for color in before_cv:gmatch("%[bg#([0-9A-Fa-f]+)%]") do
            table.insert(colors_before, color)
          end

          -- 在角色标记后查找最近的颜色标记
          local after_cv = sentence:sub(cv_end + 1)
          local color_after = after_cv:match("%[bg#([0-9A-Fa-f]+)%]")

          -- 优先使用角色标记前最近的颜色（最后一个），如果没有则使用角色标记后的第一个颜色
          local bg_color = nil
          if #colors_before > 0 then
            bg_color = colors_before[#colors_before]
          elseif color_after then
            bg_color = color_after
          end

          -- 如果找到了颜色，记录到CV颜色统计中
          if bg_color and #bg_color == 6 then
            -- 初始化CV的颜色统计
            if not cv_colors[cv_name] then
              cv_colors[cv_name] = {}
              cv_color_counts[cv_name] = {}
            end

            -- 统计颜色出现次数
            if not cv_color_counts[cv_name][bg_color] then
              cv_color_counts[cv_name][bg_color] = 1
            else
              cv_color_counts[cv_name][bg_color] = cv_color_counts[cv_name][bg_color] + 1
            end

             -- 将十六进制颜色转换为REAPER颜色格式
             local r_val = tonumber(bg_color:sub(1, 2), 16)
             local g_val = tonumber(bg_color:sub(3, 4), 16)
             local b_val = tonumber(bg_color:sub(5, 6), 16)

             -- 使用REAPER的ColorToNative函数转换颜色
             -- 转换为REAPER颜色格式
             local native_color = r.ColorToNative(r_val, g_val, b_val) | 0x1000000

             -- 存储到CV的颜色列表
             cv_colors[cv_name][bg_color] = {
               hex = bg_color,
               native = native_color,
               count = cv_color_counts[cv_name][bg_color]
             }
           end
         end
       end
     end
   end

  -- 为旁白添加默认白色（但只有在旁白是有效CV时才添加）
  if cv_names["旁白"] then
    local narrator_native_color = r.ColorToNative(255, 255, 255) | 0x1000000
    cv_colors["旁白"] = {
      ["FFFFFF"] = {
        hex = "FFFFFF",
        native = narrator_native_color,
        count = 1
      }
    }
    cv_color_counts["旁白"] = { ["FFFFFF"] = 1 }
  end

  -- 为每个CV选择出现次数最多的颜色
  local cv_color_map = {}

  for cv, colors in pairs(cv_colors) do
    local max_count = 0
    local most_common_color = nil

    for hex, color_info in pairs(colors) do
      if color_info.count > max_count then
        max_count = color_info.count
        most_common_color = color_info
      end
    end

    if most_common_color then
      cv_color_map[cv] = most_common_color
    end
  end

  -- 获取项目中的所有轨道
  local track_count = r.CountTracks(0)

  -- 轨道匹配信息
  local track_match_info = {}

  -- 记录已匹配的CV
  local matched_cvs = {}

  -- 记录已存在的轨道名称，用于检查是否存在同名轨道
  local existing_track_names = {}

  -- 使用统一的中文字符匹配算法
  local function count_matching_chinese_chars(str1, str2)
    return utils_module.string_utils.count_matching_chinese_chars(str1, str2)
  end

  -- 遍历所有轨道
  for i = 0, track_count - 1 do
    local track = r.GetTrack(0, i)
    if track then
      processed_count = processed_count + 1

      -- 获取轨道名称
      local _, track_name = r.GetTrackName(track)

      -- 记录已存在的轨道名称
      existing_track_names[track_name] = true

      -- 查找轨道名称中的CV名称
      local found_cv = nil
      local similar_cv = nil
      local max_matching_chars = 0

      -- 先检查是否是旁白轨道
      if track_name:find("旁白") then
        found_cv = "旁白"
      else
        -- 使用CV分类进行匹配（优先级最高）
        for _, cv_name in ipairs(cv_order) do
          if cv_name ~= "旁白" then
            -- 尝试多种匹配方式
            if track_name:find(cv_name) or
               track_name:lower():find(cv_name:lower()) or
               track_name:find(cv_name:gsub(" ", "")) then
              found_cv = cv_name
              break
            else
              -- 检查中文字符匹配度
              local matching_chars = count_matching_chinese_chars(track_name, cv_name)
              if matching_chars > max_matching_chars and matching_chars >= 1 then
                max_matching_chars = matching_chars
                similar_cv = cv_name
              end
            end
          end
        end

        -- 如果没有匹配到，再检查轨道名称是否匹配某个角色名（可能轨道名是角色名而不是CV名）
        if not found_cv then
          for cv_name, roles in pairs(cv_categories) do
            for _, role_info in ipairs(roles) do
              if track_name:find(role_info.role) or
                 track_name:lower():find(role_info.role:lower()) or
                 track_name:find(role_info.role:gsub(" ", "")) then
                found_cv = cv_name  -- 使用该角色对应的CV
                break
              else
                -- 检查角色名的中文字符匹配度
                local matching_chars = count_matching_chinese_chars(track_name, role_info.role)
                if matching_chars > max_matching_chars and matching_chars >= 1 then
                  max_matching_chars = matching_chars
                  similar_cv = cv_name
                end
              end
            end
            if found_cv then break end
          end
        end

        -- 如果还没有匹配到，最后尝试使用颜色映射中的CV名称
        if not found_cv then
          for cv, color_info in pairs(cv_color_map) do
            if cv ~= "旁白" and not cv_categories[cv] then  -- 避免重复检查已在分类中的CV
              -- 尝试多种匹配方式
              if track_name:find(cv) or
                 track_name:lower():find(cv:lower()) or
                 track_name:find(cv:gsub(" ", "")) then
                found_cv = cv
                break
              else
                -- 检查中文字符匹配度
                local matching_chars = count_matching_chinese_chars(track_name, cv)
                if matching_chars > max_matching_chars and matching_chars >= 1 then
                  max_matching_chars = matching_chars
                  similar_cv = cv
                end
              end
            end
          end
        end
      end

      -- 如果找到了匹配的CV，设置轨道颜色
      if found_cv then
        local color_info = cv_color_map[found_cv]

        -- 记录已匹配的CV
        matched_cvs[found_cv] = true

        -- 获取当前轨道颜色
        local old_color = r.GetTrackColor(track)

        -- 检查轨道名是否需要更新为CV名称（确保轨道名就是CV名称）
        if track_name ~= found_cv then
          -- 重命名轨道为纯CV名称
          r.GetSetMediaTrackInfo_String(track, "P_NAME", found_cv, true)
          renamed_count = renamed_count + 1
        end

        -- 尝试多种方法设置轨道颜色
        -- 方法1: 直接使用SetTrackColor
        r.SetTrackColor(track, color_info.native)

        -- 方法2: 如果方法1失败，尝试使用不同的颜色格式
        local new_color = r.GetTrackColor(track)
        if new_color == old_color then
          -- 尝试不同的颜色格式
          local alt_color = r.ColorToNative(
            tonumber(color_info.hex:sub(1, 2), 16),
            tonumber(color_info.hex:sub(3, 4), 16),
            tonumber(color_info.hex:sub(5, 6), 16)
          )
          r.SetTrackColor(track, alt_color)
        end

        -- 再次获取轨道颜色，检查是否成功设置
        local new_color_after = r.GetTrackColor(track)

        -- 检查颜色是否已更改
        local success = false
        if new_color_after ~= old_color then
          success_count = success_count + 1
          success = true
        end

        table.insert(track_match_info, {
          track_number = i + 1,
          track_name = track_name,
          cv = found_cv,
          color = "#" .. color_info.hex,
          old_color = old_color,
          new_color = new_color_after,
          success = success
        })
      -- 如果找到了相似的CV，先判断是否有匹配2个及以上字符的
      elseif similar_cv and max_matching_chars >= 2 then
        local color_info = cv_color_map[similar_cv]

        -- 记录原始轨道名用于日志
        local original_track_name = track_name

        -- 重命名轨道（只保留CV名称）
        r.GetSetMediaTrackInfo_String(track, "P_NAME", similar_cv, true)

        -- 记录已匹配的CV
        matched_cvs[similar_cv] = true

        -- 获取当前轨道颜色
        local old_color = r.GetTrackColor(track)

        -- 设置轨道颜色
        r.SetTrackColor(track, color_info.native)

        -- 再次获取轨道颜色，检查是否成功设置
        local new_color_after = r.GetTrackColor(track)

        -- 检查颜色是否已更改
        local success = false
        if new_color_after ~= old_color then
          success_count = success_count + 1
          success = true
        end

        -- 增加重命名计数
        renamed_count = renamed_count + 1

        table.insert(track_match_info, {
          track_number = i + 1,
          track_name = similar_cv,
          old_track_name = original_track_name,
          cv = similar_cv,
          color = "#" .. color_info.hex,
          old_color = old_color,
          new_color = new_color_after,
          success = success,
          is_renamed = true,  -- 标记为重命名的轨道
          match_quality = "高(2+字符)"  -- 标记匹配质量
        })
      -- 如果只有匹配1个字符的CV
      elseif similar_cv and max_matching_chars == 1 then
        local color_info = cv_color_map[similar_cv]

        -- 记录原始轨道名用于日志
        local original_track_name = track_name

        -- 重命名轨道（只保留CV名称）
        r.GetSetMediaTrackInfo_String(track, "P_NAME", similar_cv, true)

        -- 记录已匹配的CV
        matched_cvs[similar_cv] = true

        -- 获取当前轨道颜色
        local old_color = r.GetTrackColor(track)

        -- 设置轨道颜色
        r.SetTrackColor(track, color_info.native)

        -- 再次获取轨道颜色，检查是否成功设置
        local new_color_after = r.GetTrackColor(track)

        -- 检查颜色是否已更改
        local success = false
        if new_color_after ~= old_color then
          success_count = success_count + 1
          success = true
        end

        -- 增加重命名计数
        renamed_count = renamed_count + 1

        table.insert(track_match_info, {
          track_number = i + 1,
          track_name = similar_cv,
          old_track_name = original_track_name,
          cv = similar_cv,
          color = "#" .. color_info.hex,
          old_color = old_color,
          new_color = new_color_after,
          success = success,
          is_renamed = true,  -- 标记为重命名的轨道
          match_quality = "低(1字符)"  -- 标记匹配质量
        })
      end
    end
  end

  -- 新增功能：为未匹配的CV创建新轨道
  -- 首先检查是否需要创建旁白轨道，并将其放在第一轨
  if not matched_cvs["旁白"] and cv_color_map["旁白"] and not existing_track_names["旁白"] and cv_categories["旁白"] then
    -- 在第一轨位置创建旁白轨道
    r.InsertTrackAtIndex(0, true)
    local narrator_track = r.GetTrack(0, 0)

    if narrator_track then
      -- 设置轨道名称
      r.GetSetMediaTrackInfo_String(narrator_track, "P_NAME", "旁白", true)

      -- 设置轨道颜色
      r.SetTrackColor(narrator_track, cv_color_map["旁白"].native)

      -- 增加创建计数
      created_count = created_count + 1
      success_count = success_count + 1

      table.insert(track_match_info, {
        track_number = 1,
        track_name = "旁白",
        cv = "旁白",
        color = "#" .. cv_color_map["旁白"].hex,
        old_color = 0,
        new_color = cv_color_map["旁白"].native,
        success = true,
        is_new = true  -- 标记为新创建的轨道
      })

      -- 标记旁白已匹配
      matched_cvs["旁白"] = true
    end

    -- 由于插入了一个轨道，需要更新轨道总数
    track_count = r.CountTracks(0)
  end

  -- 为其他未匹配的CV创建新轨道（在项目末尾）
  -- 使用CV分类的顺序来创建轨道
  for _, cv in ipairs(cv_order) do
    local color_info = cv_color_map[cv]
    -- 只有在未匹配且不存在同名轨道，且有颜色信息的情况下才创建新轨道
    if color_info and not matched_cvs[cv] and not existing_track_names[cv] and cv ~= "旁白" then
      -- 创建新轨道
      r.InsertTrackAtIndex(track_count + created_count - (matched_cvs["旁白"] and created_count > 0 and 1 or 0), true)
      local new_track = r.GetTrack(0, track_count + created_count - (matched_cvs["旁白"] and created_count > 0 and 1 or 0))

      if new_track then
        -- 设置轨道名称
        r.GetSetMediaTrackInfo_String(new_track, "P_NAME", cv, true)

        -- 设置轨道颜色
        r.SetTrackColor(new_track, color_info.native)

        -- 增加创建计数
        created_count = created_count + 1
        success_count = success_count + 1

        table.insert(track_match_info, {
          track_number = track_count + created_count,
          track_name = cv,
          cv = cv,
          color = "#" .. color_info.hex,
          old_color = 0,
          new_color = color_info.native,
          success = true,
          is_new = true  -- 标记为新创建的轨道
        })
      end
    end
  end

  -- 结束撤销块
  r.Undo_EndBlock("设置轨道颜色", -1)

  -- 显示结果
  local message = string.format("处理了 %d 个轨道，成功: %d", processed_count, success_count)
  if created_count > 0 then
    message = message .. string.format("，新创建: %d", created_count)
  end
  if renamed_count > 0 then
    message = message .. string.format("，重命名: %d", renamed_count)
    message = message .. "\n已将轨道名称统一为对应CV名称"
  end
  r.MB(message, "轨道颜色设置完成", 0)
  return message
end

-- 处理分轨按钮点击事件
function button_module.handle_track_split_button_click()
  -- 获取当前光标位置
  local cursor_pos = r.GetCursorPosition()

  -- 获取选中的音频块数量
  local selected_count = r.CountSelectedMediaItems(0)

  if selected_count == 0 then
    r.MB("请先选择要分轨的音频块！", "提示", 0)
    return "错误：没有选中音频块"
  end

  -- 开始撤销块
  r.Undo_BeginBlock()

  -- 收集选中的音频块信息
  local items_info = {}
  for i = 0, selected_count - 1 do
    local item = r.GetSelectedMediaItem(0, i)
    if item then
      local take = r.GetActiveTake(item)
      if take then
        local item_name = r.GetTakeName(take)
        if item_name and item_name ~= "" then
          -- 提取数字用于排序
          local number = tonumber(item_name:match("(%d+)")) or 0

          table.insert(items_info, {
            item = item,
            name = item_name,
            number = number
          })
        end
      end
    end
  end

  if #items_info == 0 then
    r.MB("选中的音频块没有有效的名称！", "提示", 0)
    r.Undo_EndBlock("分轨操作", -1)
    return "错误：没有有效的音频块名称"
  end

  -- 使用统一的中文字符匹配算法
  local function count_matching_chinese_chars_for_split(str1, str2)
    return utils_module.string_utils.count_matching_chinese_chars(str1, str2)
  end

  -- 轨道匹配算法
  local function find_matching_track(item_name)
    local track_count = r.CountTracks(0)
    local best_track = nil
    local best_match_score = 0

    for i = 0, track_count - 1 do
      local track = r.GetTrack(0, i)
      local _, track_name = r.GetSetMediaTrackInfo_String(track, "P_NAME", "", false)

      if track_name and track_name ~= "" then
        -- 使用统一的字符串匹配工具
        local match_options = {
          exact_match = true,
          case_sensitive = false,
          ignore_spaces = true,
          chinese_threshold = 1
        }

        local is_match, match_score = utils_module.string_utils.is_string_match(track_name, item_name, match_options)
        local exact_match = match_score >= 700  -- 700分以上认为是精确匹配

        -- 更新最佳匹配
        if exact_match or match_score > best_match_score then
          best_track = track
          best_match_score = match_score
          if exact_match then
            break
          end
        end
      end
    end

    return best_track
  end

  -- 轨道排序功能
  local function sort_tracks_by_duration()
    local track_count = r.CountTracks(0)
    if track_count <= 2 then
      return -- 轨道数量不足，无需排序
    end

    local track_durations = {}

    -- 从第2轨开始计算每个轨道的音频块总时长
    for i = 1, track_count - 1 do
      local track = r.GetTrack(0, i)
      local total_duration = 0
      local item_count = r.CountTrackMediaItems(track)

      for j = 0, item_count - 1 do
        local item = r.GetTrackMediaItem(track, j)
        local item_length = r.GetMediaItemInfo_Value(item, "D_LENGTH")
        total_duration = total_duration + item_length
      end

      table.insert(track_durations, {
        track = track,
        duration = total_duration,
        original_index = i
      })
    end

    -- 按时长从长到短排序
    table.sort(track_durations, function(a, b)
      return a.duration > b.duration
    end)

    -- 重新排列轨道顺序
    for i = #track_durations, 1, -1 do
      local track_info = track_durations[i]
      local target_index = i

      -- 获取当前轨道的实际索引
      local current_index = -1
      local total_tracks = r.CountTracks(0)
      for j = 0, total_tracks - 1 do
        if r.GetTrack(0, j) == track_info.track then
          current_index = j
          break
        end
      end

      if current_index ~= -1 and current_index ~= target_index then
        r.SetOnlyTrackSelected(track_info.track)
        r.ReorderSelectedTracks(target_index, 0)
      end
    end
  end

  -- 按数字排序音频块信息
  table.sort(items_info, function(a, b)
    return a.number < b.number
  end)

  local matched_count = 0
  local unmatched_count = 0
  local unmatched_names = {}

  -- 创建未匹配轨道（如果需要）
  local unmatched_track = nil

  -- 处理每个音频块
  for _, item_info in ipairs(items_info) do
    local target_track = find_matching_track(item_info.name)

    if target_track then
      -- 移动音频块到目标轨道
      r.MoveMediaItemToTrack(item_info.item, target_track)
      matched_count = matched_count + 1
    else
      -- 如果还没有创建未匹配轨道，则创建一个
      if not unmatched_track then
        r.InsertTrackAtIndex(r.CountTracks(0), false)
        unmatched_track = r.GetTrack(0, r.CountTracks(0) - 1)
        r.GetSetMediaTrackInfo_String(unmatched_track, "P_NAME", "未匹配", true)
      end

      -- 移动音频块到未匹配轨道
      r.MoveMediaItemToTrack(item_info.item, unmatched_track)
      unmatched_count = unmatched_count + 1
      table.insert(unmatched_names, item_info.name)
    end
  end

  -- 将所有音频块对齐到光标位置
  local all_items = {}
  for i = 0, r.CountMediaItems(0) - 1 do
    local item = r.GetMediaItem(0, i)
    if item then
      table.insert(all_items, item)
    end
  end

  -- 按音频块长度从长到短排序
  table.sort(all_items, function(a, b)
    local length_a = r.GetMediaItemInfo_Value(a, "D_LENGTH")
    local length_b = r.GetMediaItemInfo_Value(b, "D_LENGTH")
    return length_a > length_b
  end)

  -- 将所有音频块移动到光标位置
  for _, item in ipairs(all_items) do
    r.SetMediaItemInfo_Value(item, "D_POSITION", cursor_pos)
  end

  -- 对每个轨道内的音频块按数字排序
  local track_count = r.CountTracks(0)
  for i = 0, track_count - 1 do
    local track = r.GetTrack(0, i)
    local item_count = r.CountTrackMediaItems(track)

    if item_count > 1 then
      local track_items = {}

      -- 收集轨道内的音频块信息
      for j = 0, item_count - 1 do
        local item = r.GetTrackMediaItem(track, j)
        local take = r.GetActiveTake(item)
        if take then
          local item_name = r.GetTakeName(take)
          local number = tonumber(item_name:match("(%d+)")) or 0
          local start_time = r.GetMediaItemInfo_Value(item, "D_POSITION")

          table.insert(track_items, {
            item = item,
            name = item_name,
            number = number,
            start_time = start_time
          })
        end
      end

      -- 按数字排序
      table.sort(track_items, function(a, b)
        return a.number < b.number
      end)

      -- 重新排列音频块位置
      local current_time = track_items[1].start_time
      for k, track_item in ipairs(track_items) do
        r.SetMediaItemInfo_Value(track_item.item, "D_POSITION", current_time)
        local item_length = r.GetMediaItemInfo_Value(track_item.item, "D_LENGTH")
        current_time = current_time + item_length
      end
    end
  end

  -- 轨道排序
  sort_tracks_by_duration()

  -- 结束撤销块
  r.Undo_EndBlock("分轨操作", -1)

  -- 显示结果
  local message = string.format("分轨完成！\n匹配: %d 个\n未匹配: %d 个", matched_count, unmatched_count)
  if unmatched_count > 0 then
    message = message .. "\n\n未匹配的音频块：\n" .. table.concat(unmatched_names, "\n")
  end

  r.MB(message, "分轨结果", 0)

  return message
end

-- ============ 缺失的按钮处理函数 ============

-- 创建区域标记（自动）
function button_module.create_region_for_auto(app_state)
  if not r then return "REAPER API不可用" end

  local start_time = r.GetCursorPosition()
  local end_time = start_time + 5.0  -- 默认5秒长度

  -- 创建区域名称
  local region_name = string.format("%s-%s", app_state.selected_cv or "未知CV", app_state.selected_role or "未知角色")
  if app_state.process_suggestion and app_state.process_suggestion ~= "" then
    region_name = region_name .. " [" .. app_state.process_suggestion .. "]"
  end

  -- 创建区域
  local region_idx = r.AddProjectMarker2(0, true, start_time, end_time, region_name, -1, 0)

  if region_idx >= 0 then
    return "区域标记创建成功: " .. region_name
  else
    return "区域标记创建失败"
  end
end

-- 导出到Excel
function button_module.export_to_excel(app_state)
  if not app_state.selected_texts or #app_state.selected_texts == 0 then
    return "没有选中的文本可导出"
  end

  -- 简单的CSV导出实现
  local csv_content = "序号,CV,角色,文本内容,错误描述,正确表达,集数,处理建议\n"

  for i, text in ipairs(app_state.selected_texts) do
    local clean_text = text:gsub('"', '""')  -- 转义双引号
    local line = string.format('%d,"%s","%s","%s","%s","%s","%s","%s"\n',
      i,
      app_state.selected_cv or "",
      app_state.selected_role or "",
      clean_text,
      app_state.error_note or "",
      app_state.correct_note or "",
      app_state.episode_number or "",
      app_state.process_suggestion or ""
    )
    csv_content = csv_content .. line
  end

  -- 保存到文件
  local file_path = r.GetProjectPath("") .. "审听报告_" .. os.date("%Y%m%d_%H%M%S") .. ".csv"
  local file = io.open(file_path, "w")
  if file then
    file:write(csv_content)
    file:close()
    return "报告已导出到: " .. file_path
  else
    return "导出失败: 无法创建文件"
  end
end

-- 处理文档按钮
function button_module.handle_document_button(callbacks)
  if not r then return "REAPER API不可用" end

  -- 打开文件对话框
  local retval, file_path = r.GetUserFileNameForRead("", "选择文档文件", "*.docx\0*.docx\0*.txt\0*.txt\0*.*\0*.*\0\0")

  if not retval or not file_path or file_path == "" then
    return "未选择文件"
  end

  local content = ""
  local success = false

  -- 检查文件类型并使用相应的处理方式
  if file_path:lower():match("%.docx$") then
    -- 处理.docx文件
    -- 尝试加载word_module
    local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
    local script_dir = script_path:match("(.+[\\/])") or "./"

    local word_module_success, word_module = pcall(dofile, script_dir .. "word_module.lua")

    if word_module_success and word_module and word_module.parse_docx then
      -- 使用word_module解析docx文件
      local parsed_content = word_module.parse_docx(file_path)

      if parsed_content and type(parsed_content) == "string" and not parsed_content:match("^无法") then
        content = parsed_content
        success = true
      else
        return "无法解析Word文档: " .. (parsed_content or "未知错误")
      end
    else
      return "Word模块加载失败，无法处理.docx文件。请确保word_module.lua文件存在。"
    end
  else
    -- 处理普通文本文件
    local file = io.open(file_path, "r")
    if not file then
      return "无法打开文件: " .. file_path
    end

    content = file:read("*all")
    file:close()

    if content and content ~= "" then
      success = true
    else
      return "文件内容为空"
    end
  end

  if not success or content == "" then
    return "文件读取失败或内容为空"
  end

  -- 调用回调函数处理内容
  if callbacks.handle_text_content then
    callbacks.handle_text_content(content)
  end

  if callbacks.parse_sentences then
    callbacks.parse_sentences()
  end

  if callbacks.extract_cv_role_pairs then
    callbacks.extract_cv_role_pairs()
  end

  return "文档读取成功: " .. file_path
end

-- 处理剪贴板按钮
function button_module.handle_clipboard_button(callbacks)
  if not callbacks.get_clipboard then
    return "剪贴板回调函数不可用"
  end

  -- 获取剪贴板内容
  callbacks.get_clipboard()

  -- 解析内容
  if callbacks.parse_sentences then
    callbacks.parse_sentences()
  end

  if callbacks.extract_cv_role_pairs then
    callbacks.extract_cv_role_pairs()
  end

  return "剪贴板内容读取成功"
end

-- 打开CSV文件
function button_module.open_csv_file()
  if not r then return "REAPER API不可用" end

  local retval, file_path = r.GetUserFileNameForRead("", "选择CSV报告文件", "CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*")

  if not retval or not file_path or file_path == "" then
    return "未选择文件"
  end

  -- 尝试用系统默认程序打开CSV文件
  if r.CF_ShellExecute then
    r.CF_ShellExecute(file_path)
    return "正在打开CSV文件: " .. file_path
  else
    return "无法打开文件，请手动打开: " .. file_path
  end
end

-- 打开CSV文件（备用函数，主要使用style_module中的实现）
function button_module.open_csv_file()
  if not r then return "REAPER API不可用" end

  -- 获取REAPER的Scripts目录
  local reaper_path = r.GetResourcePath()
  -- 构建CSV文件路径
  local csv_path = reaper_path .. "/Scripts/审听报告.csv"

  -- 检查文件是否存在
  local file = io.open(csv_path, "r")
  if file then
    file:close()

    -- 使用JS API打开文件（如果可用）
    if r.JS_ShellExecute then
      r.JS_ShellExecute(csv_path:gsub("/", "\\"), "", "", "open", 1)
      return "正在打开审听报告.csv (使用JS_ShellExecute)"
    else
      r.ExecProcess('cmd.exe /c start "" "' .. csv_path:gsub("/", "\\") .. '"', 0)
      return "正在打开审听报告.csv"
    end
  else
    return "错误：审听报告.csv文件不存在"
  end
end

-- 运行AU脚本（备用函数，主要使用style_module中的实现）
function button_module.run_au_script()
  if not r then return "REAPER API不可用" end

  -- 获取脚本目录
  local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
  local script_dir = script_path:match("(.+[\\/])") or "./"
  local au_script_path = script_dir .. "JHKAU.lua"

  -- 检查脚本是否存在
  local file = io.open(au_script_path, "r")
  if file then
    file:close()

    -- 如果有JS API可用，使用JS_ShellExecute运行脚本
    if r.JS_ShellExecute then
      r.JS_ShellExecute(au_script_path, "", "", "open", 1)
      return "已执行JHKAU.lua脚本 (使用JS_ShellExecute)"
    else
      -- 降级到直接执行脚本
      dofile(au_script_path)
      return "已执行JHKAU.lua脚本"
    end
  else
    return "错误：JHKAU.lua脚本不存在"
  end
end

-- 处理区名按钮点击
function button_module.handle_region_name_button_click(chapters)
  if not r then return "REAPER API不可用" end

  local ok, region_name = r.GetUserInputs("设置区域名称", 1, "区域名称:", "")
  if ok and region_name ~= "" then
    -- 获取当前选中的区域或创建新区域
    local start_time = r.GetCursorPosition()
    local end_time = start_time + 5.0

    local region_idx = r.AddProjectMarker2(0, true, start_time, end_time, region_name, -1, 0)
    if region_idx >= 0 then
      return "区域名称设置成功: " .. region_name
    else
      return "区域名称设置失败"
    end
  end

  return "取消设置区域名称"
end

-- 处理文名按钮点击
function button_module.handle_file_name_button_click(chapters)
  if not r then return "REAPER API不可用" end

  local ok, file_name = r.GetUserInputs("设置文件名", 1, "文件名:", "")
  if ok and file_name ~= "" then
    -- 设置项目标题
    r.Main_OnCommand(40016, 0)  -- File: Project settings
    return "文件名设置提示已显示"
  end

  return "取消设置文件名"
end





return button_module