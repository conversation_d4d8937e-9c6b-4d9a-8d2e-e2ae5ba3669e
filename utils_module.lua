-- 通用工具模块 - 存放各种通用功能函数
-- 此模块被其他模块引用，提供通用的工具函数
-- 该模块包含无依赖的基础功能，因此所有其他模块都可以安全地依赖它

local r = reaper  -- 添加REAPER API引用

local utils_module = {}

-- 模块版本信息
utils_module.version = {
  major = 1,
  minor = 0,
  patch = 0,
  string = "1.0.0"
}

-- 日志级别定义
utils_module.LOG_LEVEL = {
  DEBUG = 1,
  INFO = 2,
  WARN = 3,
  ERROR = 4,
  NONE = 5
}

-- 当前日志级别，默认为INFO
local current_log_level = utils_module.LOG_LEVEL.INFO

-- 设置日志级别
-- @param level 日志级别，使用utils_module.LOG_LEVEL中的值
function utils_module.set_log_level(level)
  if type(level) == "number" and
     level >= utils_module.LOG_LEVEL.DEBUG and
     level <= utils_module.LOG_LEVEL.NONE then
    current_log_level = level
    return true
  end
  return false
end

-- 内部日志函数
-- @param level 日志级别
-- @param message 日志消息
-- @param ... 格式化参数
local function log(level, message, ...)
  if level < current_log_level then return end

  local level_str = "INFO"
  if level == utils_module.LOG_LEVEL.DEBUG then level_str = "DEBUG"
  elseif level == utils_module.LOG_LEVEL.WARN then level_str = "WARN"
  elseif level == utils_module.LOG_LEVEL.ERROR then level_str = "ERROR"
  end

  local formatted_msg = message
  if ... then
    formatted_msg = string.format(message, ...)
  end

  local timestamp = os.date("%Y-%m-%d %H:%M:%S")
  local log_msg = string.format("[%s] [%s] %s\n", timestamp, level_str, formatted_msg)

  r.ShowConsoleMsg(log_msg)
end

-- 公开的日志函数
function utils_module.debug(message, ...) log(utils_module.LOG_LEVEL.DEBUG, message, ...) end
function utils_module.info(message, ...) log(utils_module.LOG_LEVEL.INFO, message, ...) end
function utils_module.warn(message, ...) log(utils_module.LOG_LEVEL.WARN, message, ...) end
function utils_module.error(message, ...) log(utils_module.LOG_LEVEL.ERROR, message, ...) end

-- 初始化函数，接收可选的配置参数
-- @param deps 依赖模块或配置参数表
-- @param config 全局配置参数表
-- @return 工具模块自身
function utils_module.init(deps, config)
  -- 兼容两种调用方式
  local cfg = {}

  -- 如果deps是配置，使用它
  if deps and type(deps) == "table" and not deps.utils_module then
    cfg = deps
  end

  -- 如果提供了第二个参数，合并配置
  if config and type(config) == "table" then
    for k, v in pairs(config) do
      cfg[k] = v
    end
  end

  -- 应用配置
  if cfg.log_level then
    utils_module.set_log_level(cfg.log_level)
  end

  -- 仅当show_init_message为true时才显示初始化信息
  if cfg.show_init_message then
    utils_module.info("工具模块初始化完成，版本: %s", utils_module.version.string)
  end

  return utils_module
end

-- 判断点是否在矩形内的辅助函数
-- @param x 点的X坐标
-- @param y 点的Y坐标
-- @param rect 包含x,y,w,h属性的矩形表
-- @return boolean 如果点在矩形内则返回true，否则返回false
function utils_module.is_point_in_rect(x, y, rect)
  if not rect or type(rect) ~= "table" or
     not rect.x or not rect.y or not rect.w or not rect.h then
    utils_module.warn("is_point_in_rect: 无效的矩形参数")
    return false
  end

  return x >= rect.x and x <= rect.x + rect.w and
         y >= rect.y and y <= rect.y + rect.h
end

-- 设置颜色辅助函数
-- @param color 包含r,g,b,a属性的颜色表
function utils_module.set_color(color)
  if not color or type(color) ~= "table" then
    utils_module.warn("set_color: 无效的颜色参数")
    return
  end

  local r_val = color.r or 0
  local g_val = color.g or 0
  local b_val = color.b or 0
  local a_val = color.a or 1

  gfx.set(r_val, g_val, b_val, a_val)
end

-- =====================================================
-- 统一错误处理机制
-- =====================================================

-- 错误处理器
utils_module.error_handler = {
    errors = {},
    max_errors = 100,  -- 最大错误数量

    -- 添加错误
    add = function(self, message, level, context)
        local error_entry = {
            message = message or "未知错误",
            level = level or "error",
            context = context or "",
            timestamp = os.time(),
            time_str = os.date("%H:%M:%S")
        }

        table.insert(self.errors, error_entry)

        -- 限制错误数量，避免内存泄漏
        if #self.errors > self.max_errors then
            table.remove(self.errors, 1)
        end

        -- 根据错误级别输出到控制台
        if level == "error" then
            utils_module.error("[%s] %s %s", error_entry.time_str, message, context and ("(" .. context .. ")") or "")
        elseif level == "warn" then
            utils_module.warn("[%s] %s %s", error_entry.time_str, message, context and ("(" .. context .. ")") or "")
        end

        return error_entry
    end,

    -- 清除所有错误
    clear = function(self)
        self.errors = {}
    end,

    -- 获取最后一个错误
    get_last_error = function(self)
        return self.errors[#self.errors]
    end,

    -- 获取所有错误
    get_all_errors = function(self)
        return self.errors
    end,

    -- 获取特定级别的错误
    get_errors_by_level = function(self, level)
        local filtered = {}
        for _, error_entry in ipairs(self.errors) do
            if error_entry.level == level then
                table.insert(filtered, error_entry)
            end
        end
        return filtered
    end,

    -- 错误处理装饰器
    with_error_handling = function(self, func, error_message, context)
        return function(...)
            local success, result = pcall(func, ...)
            if not success then
                self:add(error_message or "函数执行失败", "error", context)
                return nil, result
            end
            return result
        end
    end
}

-- =====================================================
-- 字符串处理工具
-- =====================================================

-- 字符串处理工具集
utils_module.string_utils = {
    -- 将UTF-8字符串转换为字符数组
    utf8_to_chars = function(str)
        if not str or type(str) ~= "string" then
            return {}
        end

        local chars = {}
        local pos = 1
        while pos <= #str do
            local byte = string.byte(str, pos)
            local char_len = 1
            if byte >= 240 then -- 4 bytes
                char_len = 4
            elseif byte >= 224 then -- 3 bytes
                char_len = 3
            elseif byte >= 192 then -- 2 bytes
                char_len = 2
            end
            table.insert(chars, string.sub(str, pos, pos + char_len - 1))
            pos = pos + char_len
        end
        return chars
    end,

    -- 过滤出中文字符
    filter_chinese = function(chars)
        if not chars or type(chars) ~= "table" then
            return {}
        end

        local chinese = {}
        for _, char in ipairs(chars) do
            local b1 = string.byte(char, 1)
            if b1 >= 224 and b1 <= 239 then
                table.insert(chinese, char)
            end
        end
        return chinese
    end,

    -- 计算两个字符数组中相同字符的数量
    count_matching_chars = function(chars1, chars2)
        if not chars1 or not chars2 or type(chars1) ~= "table" or type(chars2) ~= "table" then
            return 0
        end

        local count = 0
        for _, char1 in ipairs(chars1) do
            for _, char2 in ipairs(chars2) do
                if char1 == char2 then
                    count = count + 1
                    break
                end
            end
        end
        return count
    end,

    -- 计算两个字符串中匹配的中文字符数量
    count_matching_chinese_chars = function(str1, str2)
        if not str1 or not str2 or type(str1) ~= "string" or type(str2) ~= "string" then
            return 0
        end

        local chars1 = utils_module.string_utils.utf8_to_chars(str1)
        local chars2 = utils_module.string_utils.utf8_to_chars(str2)
        local chinese1 = utils_module.string_utils.filter_chinese(chars1)
        local chinese2 = utils_module.string_utils.filter_chinese(chars2)

        return utils_module.string_utils.count_matching_chars(chinese1, chinese2)
    end,

    -- 计算字符串相似度 (0-1之间)
    calculate_similarity = function(str1, str2)
        if not str1 or not str2 or type(str1) ~= "string" or type(str2) ~= "string" then
            return 0
        end

        if str1 == str2 then
            return 1
        end

        local chars1 = utils_module.string_utils.utf8_to_chars(str1)
        local chars2 = utils_module.string_utils.utf8_to_chars(str2)
        local chinese1 = utils_module.string_utils.filter_chinese(chars1)
        local chinese2 = utils_module.string_utils.filter_chinese(chars2)

        if #chinese1 == 0 and #chinese2 == 0 then
            return 0
        end

        local matching = utils_module.string_utils.count_matching_chars(chinese1, chinese2)
        local total = math.max(#chinese1, #chinese2)

        return total > 0 and (matching / total) or 0
    end,

    -- 多种匹配方式检查字符串是否匹配
    is_string_match = function(str1, str2, options)
        if not str1 or not str2 or type(str1) ~= "string" or type(str2) ~= "string" then
            return false, 0
        end

        options = options or {}
        local exact_match = options.exact_match ~= false  -- 默认启用精确匹配
        local case_sensitive = options.case_sensitive ~= false  -- 默认区分大小写
        local ignore_spaces = options.ignore_spaces == true  -- 默认不忽略空格
        local chinese_threshold = options.chinese_threshold or 1  -- 中文字符匹配阈值

        -- 1. 精确匹配
        if exact_match and str1 == str2 then
            return true, 1000
        end

        -- 2. 包含匹配
        if str1:find(str2) or str2:find(str1) then
            return true, 900
        end

        -- 3. 大小写不敏感匹配
        if not case_sensitive then
            if str1:lower():find(str2:lower()) or str2:lower():find(str1:lower()) then
                return true, 800
            end
        end

        -- 4. 去空格匹配
        if ignore_spaces then
            local str1_no_space = str1:gsub(" ", "")
            local str2_no_space = str2:gsub(" ", "")
            if str1_no_space:find(str2_no_space) or str2_no_space:find(str1_no_space) then
                return true, 700
            end
        end

        -- 5. 中文字符匹配度
        local matching_chars = utils_module.string_utils.count_matching_chinese_chars(str1, str2)
        if matching_chars >= chinese_threshold then
            return true, matching_chars * 100
        end

        return false, 0
    end
}

-- =====================================================
-- 缓存装饰器工具
-- =====================================================

-- 缓存装饰器
utils_module.cache_decorator = {
    -- 为函数添加缓存功能
    with_cache = function(func, cache_key_generator, ttl)
        if not func or type(func) ~= "function" then
            utils_module.error_handler:add("缓存装饰器需要有效的函数", "error", "cache_decorator.with_cache")
            return func
        end

        ttl = ttl or 300  -- 默认5分钟过期

        return function(...)
            local args = {...}
            local cache_key

            -- 生成缓存键
            if cache_key_generator and type(cache_key_generator) == "function" then
                cache_key = cache_key_generator(unpack(args))
            else
                -- 默认缓存键生成策略
                cache_key = utils_module.cache_decorator.generate_key("func_cache", unpack(args))
            end

            -- 检查缓存
            if utils_module.cache_has(cache_key) then
                local cached_data = utils_module.cache_get(cache_key)
                if cached_data and cached_data.timestamp and (os.time() - cached_data.timestamp) < ttl then
                    return cached_data.result
                else
                    -- 缓存过期，删除
                    utils_module.cache_remove(cache_key)
                end
            end

            -- 执行函数
            local result = func(unpack(args))

            -- 存储到缓存
            utils_module.cache_add(cache_key, {
                result = result,
                timestamp = os.time()
            })

            return result
        end
    end,

    -- 生成缓存键
    generate_key = function(prefix, ...)
        local parts = {prefix}
        for i, v in ipairs({...}) do
            if type(v) == "table" then
                -- 对于表类型，生成简单的哈希
                table.insert(parts, utils_module.hash_string(tostring(v)))
            else
                table.insert(parts, tostring(v))
            end
        end
        return table.concat(parts, "_")
    end,

    -- 为文本处理函数创建专用缓存键生成器
    text_cache_key = function(text, additional_params)
        local key_parts = {"text_cache"}

        -- 使用文本哈希作为主键
        if text and type(text) == "string" then
            table.insert(key_parts, utils_module.hash_string(text))
        end

        -- 添加额外参数
        if additional_params then
            if type(additional_params) == "table" then
                for k, v in pairs(additional_params) do
                    table.insert(key_parts, tostring(k) .. "=" .. tostring(v))
                end
            else
                table.insert(key_parts, tostring(additional_params))
            end
        end

        return table.concat(key_parts, "_")
    end,

    -- 为句子处理函数创建专用缓存键生成器
    sentences_cache_key = function(sentences, additional_params)
        local key_parts = {"sentences_cache"}

        -- 使用句子数量和前几个句子的哈希
        if sentences and type(sentences) == "table" then
            table.insert(key_parts, tostring(#sentences))

            -- 使用前3个句子生成哈希（避免处理过大的数据）
            local sample_text = ""
            for i = 1, math.min(3, #sentences) do
                if sentences[i] then
                    sample_text = sample_text .. sentences[i]
                end
            end

            if sample_text ~= "" then
                table.insert(key_parts, utils_module.hash_string(sample_text))
            end
        end

        -- 添加额外参数
        if additional_params then
            if type(additional_params) == "table" then
                for k, v in pairs(additional_params) do
                    table.insert(key_parts, tostring(k) .. "=" .. tostring(v))
                end
            else
                table.insert(key_parts, tostring(additional_params))
            end
        end

        return table.concat(key_parts, "_")
    end,

    -- 清理过期缓存
    cleanup_expired = function(ttl)
        ttl = ttl or 300  -- 默认5分钟
        local current_time = os.time()
        local removed_count = 0

        for key, value in pairs(cache_entries) do
            if value and type(value) == "table" and value.timestamp then
                if (current_time - value.timestamp) > ttl then
                    utils_module.cache_remove(key)
                    removed_count = removed_count + 1
                end
            end
        end

        return removed_count
    end
}

-- 安全执行函数，捕获错误
-- @param func 要执行的函数
-- @param ... 传递给func的参数
-- @return boolean 执行是否成功
-- @return any 如果成功则返回func的返回值，否则返回错误信息
function utils_module.safe_call(func, ...)
  if not func or type(func) ~= "function" then
    utils_module.error_handler:add("无效的函数参数", "error", "safe_call")
    return false, "错误: 无效的函数参数"
  end

  local status, result = pcall(func, ...)
  if not status then
    utils_module.error_handler:add("函数执行失败: " .. tostring(result), "error", "safe_call")
    utils_module.error("函数执行失败: %s", tostring(result))
    return false, "错误: " .. tostring(result)
  end
  return true, result
end

-- 参数验证工具
utils_module.validate = {
    -- 验证字符串参数
    string = function(value, name, allow_empty)
        if not value then
            utils_module.error_handler:add(string.format("参数 %s 不能为 nil", name or "unknown"), "error", "validate.string")
            return false, string.format("参数 %s 不能为 nil", name or "unknown")
        end
        if type(value) ~= "string" then
            utils_module.error_handler:add(string.format("参数 %s 必须是字符串，当前类型: %s", name or "unknown", type(value)), "error", "validate.string")
            return false, string.format("参数 %s 必须是字符串", name or "unknown")
        end
        if not allow_empty and value == "" then
            utils_module.error_handler:add(string.format("参数 %s 不能为空字符串", name or "unknown"), "error", "validate.string")
            return false, string.format("参数 %s 不能为空字符串", name or "unknown")
        end
        return true
    end,

    -- 验证表参数
    table = function(value, name, allow_empty)
        if not value then
            utils_module.error_handler:add(string.format("参数 %s 不能为 nil", name or "unknown"), "error", "validate.table")
            return false, string.format("参数 %s 不能为 nil", name or "unknown")
        end
        if type(value) ~= "table" then
            utils_module.error_handler:add(string.format("参数 %s 必须是表，当前类型: %s", name or "unknown", type(value)), "error", "validate.table")
            return false, string.format("参数 %s 必须是表", name or "unknown")
        end
        if not allow_empty and next(value) == nil then
            utils_module.error_handler:add(string.format("参数 %s 不能为空表", name or "unknown"), "error", "validate.table")
            return false, string.format("参数 %s 不能为空表", name or "unknown")
        end
        return true
    end,

    -- 验证数字参数
    number = function(value, name, min, max)
        if not value then
            utils_module.error_handler:add(string.format("参数 %s 不能为 nil", name or "unknown"), "error", "validate.number")
            return false, string.format("参数 %s 不能为 nil", name or "unknown")
        end
        if type(value) ~= "number" then
            utils_module.error_handler:add(string.format("参数 %s 必须是数字，当前类型: %s", name or "unknown", type(value)), "error", "validate.number")
            return false, string.format("参数 %s 必须是数字", name or "unknown")
        end
        if min and value < min then
            utils_module.error_handler:add(string.format("参数 %s 不能小于 %s", name or "unknown", min), "error", "validate.number")
            return false, string.format("参数 %s 不能小于 %s", name or "unknown", min)
        end
        if max and value > max then
            utils_module.error_handler:add(string.format("参数 %s 不能大于 %s", name or "unknown", max), "error", "validate.number")
            return false, string.format("参数 %s 不能大于 %s", name or "unknown", max)
        end
        return true
    end
}

-- 去除字符串首尾空白
-- @param str 输入字符串
-- @return string 去除首尾空白后的字符串
function utils_module.trim(str)
  if not str or type(str) ~= "string" then return "" end
  return str:match("^%s*(.-)%s*$")
end

-- 格式化时间为分:秒.毫秒
-- @param time_in_seconds 时间（秒）
-- @return string 格式化的时间字符串
function utils_module.format_time(time_in_seconds)
  if not time_in_seconds or type(time_in_seconds) ~= "number" then
    return "00:00.000"
  end

  local minutes = math.floor(time_in_seconds / 60)
  local seconds = math.floor(time_in_seconds % 60)
  local milliseconds = math.floor((time_in_seconds % 1) * 1000)
  return string.format("%02d:%02d.%03d", minutes, seconds, milliseconds)
end

-- 安全加载文件
-- @param file_path 文件路径
-- @param fallback_value 加载失败时返回的备用值（默认为空表）
-- @return table 加载的模块或fallback_value（如果加载失败）
function utils_module.safe_dofile(file_path, fallback_value)
  if not file_path or type(file_path) ~= "string" then
    utils_module.error("safe_dofile: 无效的文件路径")
    return fallback_value or {}
  end

  local success, result = pcall(dofile, file_path)
  if not success then
    utils_module.error("加载文件失败: %s, 错误: %s", file_path, tostring(result))
    return fallback_value or {}
  end

  return result
end

-- 获取当前脚本路径
-- @return string 脚本所在目录路径
function utils_module.get_script_path()
  local script_path = debug.getinfo(2, "S").source:match("^@(.+)$") or ""
  return script_path:match("(.+[\\/])") or "./"
end

-- 获取当前选择音频块集数
-- @return string|nil 提取的集数或nil（如果未找到）
-- @return string 成功或错误消息
function utils_module.get_episode_from_selected_item()
  local item = r.GetSelectedMediaItem(0, 0)  -- 获取第一个选中的项目
  if not item then
    return nil, "未找到选中的音频项目"
  end

  local take = r.GetActiveTake(item)
  if not take then
    return nil, "未找到活动的take"
  end

  local item_name = r.GetTakeName(take)
  if not item_name then
    return nil, "未找到项目名称"
  end

  -- 尝试从名称中提取数字
  local number = item_name:match("%d+")
  if number then
    return number, "成功从音频块名称提取集数: " .. number
  end

  return nil, "未能从音频块名称中提取数字"
end

-- 检查文件是否存在
-- @param file_path 文件路径
-- @return boolean 文件是否存在
function utils_module.file_exists(file_path)
  if not file_path then return false end

  local file = io.open(file_path, "r")
  if file then
    file:close()
    return true
  end
  return false
end

-- 创建目录（包括多级目录）
-- @param path 要创建的目录路径
-- @return boolean 是否成功创建
-- @return string 成功或错误消息
function utils_module.create_directory(path)
  if not path then
    return false, "无效的路径"
  end

  -- 使用Lua的方式创建目录，避免使用CMD命令
  -- 首先检查目录是否已存在
  if utils_module.file_exists(path) then
    return true, "目录已存在: " .. path
  end

  -- 对于嵌套目录，需要确保父目录存在
  local parent_path = path:match("(.+)[\\/][^\\/]+$")
  if parent_path and not utils_module.file_exists(parent_path) then
    local success, message = utils_module.create_directory(parent_path)
    if not success then
      return false, "创建父目录失败: " .. message
    end
  end

  -- 在Windows系统上，使用Lua的文件IO操作，避免使用CMD
  local file, err = io.open(path .. "/.test_write", "w")
  if not file then
    -- 创建目录失败，尝试使用JS API（如果可用）
    if r.JS_Process_ExecuteCommand then
      -- 使用JS_Process_ExecuteCommand执行命令，确保完全隐藏窗口
      r.JS_Process_ExecuteCommand('mkdir "' .. path .. '" 2>nul', false)

      -- 检查目录是否创建成功
      local test_file = io.open(path .. "/.test_write", "w")
      if test_file then
        test_file:close()
        os.remove(path .. "/.test_write")
        return true, "目录创建成功: " .. path
      else
        utils_module.error("创建目录失败: %s", path)
        return false, "创建目录失败: " .. path
      end
    else
      -- 降级到os.execute，但使用隐藏窗口
      -- 使用start /b命令来隐藏命令行窗口
      local cmd = 'start /b "" cmd /c "mkdir "' .. path .. '" > nul 2>&1"'
      local result = os.execute(cmd)

      -- 检查是否成功
      if result then
        return true, "目录创建成功: " .. path
      else
        utils_module.error("创建目录失败: %s", path)
        return false, "创建目录失败: " .. path
      end
    end
  else
    -- 关闭并删除测试文件
    file:close()
    os.remove(path .. "/.test_write")
    return true, "目录创建成功: " .. path
  end
end

-- 深度复制表
-- @param orig 原始表
-- @return table 复制后的表
function utils_module.deep_copy(orig)
  local orig_type = type(orig)
  local copy
  if orig_type == 'table' then
    copy = {}
    for orig_key, orig_value in next, orig, nil do
      copy[utils_module.deep_copy(orig_key)] = utils_module.deep_copy(orig_value)
    end
  else
    copy = orig
  end
  return copy
end

-- 合并两个表（浅合并）
-- @param t1 目标表
-- @param t2 源表
-- @return table 合并后的表
function utils_module.merge_tables(t1, t2)
  local result = utils_module.deep_copy(t1 or {})

  if not t2 then return result end

  for k, v in pairs(t2) do
    result[k] = v
  end

  return result
end

-- 保存数据到文件
-- @param file_path 文件路径
-- @param data 要保存的数据（字符串）
-- @return boolean 是否成功保存
-- @return string 成功或错误消息
function utils_module.save_data_to_file(file_path, data)
  if not file_path or not data then
    return false, "无效的文件路径或数据"
  end

  -- 尝试创建目录（如果不存在）
  local dir_path = file_path:match("(.+[\\/])")
  if dir_path and not utils_module.file_exists(dir_path) then
    local success, message = utils_module.create_directory(dir_path)
    if not success then
      return false, "创建目录失败: " .. message
    end
  end

  -- 打开文件进行写入
  local file, err = io.open(file_path, "w")
  if not file then
    return false, "无法打开文件进行写入: " .. tostring(err)
  end

  -- 写入数据
  local success, write_err = pcall(function() file:write(data) end)
  file:close()

  if not success then
    return false, "写入文件时出错: " .. tostring(write_err)
  end

  return true, "数据成功保存到: " .. file_path
end

-- 从文件加载数据
-- @param file_path 文件路径
-- @return boolean 是否成功加载
-- @return string 加载的数据或错误消息
function utils_module.load_data_from_file(file_path)
  if not file_path then
    return false, "无效的文件路径"
  end

  -- 检查文件是否存在
  if not utils_module.file_exists(file_path) then
    return false, "文件不存在: " .. file_path
  end

  -- 打开文件进行读取
  local file, err = io.open(file_path, "r")
  if not file then
    return false, "无法打开文件进行读取: " .. tostring(err)
  end

  -- 读取全部内容
  local content, read_err = file:read("*a")
  file:close()

  if not content then
    return false, "读取文件时出错: " .. tostring(read_err)
  end

  return true, content
end

-- 为字符串创建简单的哈希值
-- @param str 输入字符串
-- @return string 哈希值
function utils_module.hash_string(str)
  if not str or type(str) ~= "string" then
    return "0"
  end

  -- 一个简单的字符串哈希算法
  local hash = 0
  for i = 1, #str do
    hash = (hash * 31 + string.byte(str, i)) % 1000000007
  end

  return tostring(hash)
end

-- 解析删除线标记字符串
-- @param text 包含删除线标记的文本，格式为[x]文本[/x]
-- @return string 原始文本，未找到则返回nil
function utils_module.parse_strike_tag(text)
  if not text or type(text) ~= "string" then
    return nil
  end

  -- 简化格式：[x]文本[/x]
  local content = text:match("%[x%](.-)%[/x%]")

  if not content then
    return nil
  end

  return content
end

-- 解析颜色标记字符串
-- @param text 包含颜色标记的文本，支持多种格式：[#FFFFFF]文本[#]
-- @return table 包含text(原始文本)和color(颜色表)的表，未找到则返回nil
function utils_module.parse_color_tag(text)
  if not text or type(text) ~= "string" then
    return nil
  end

  -- 多种匹配格式
  local hex_color, content

  -- 标准格式：[#FFFFFF]文本[#]
  hex_color, content = text:match("%[#([0-9A-Fa-f]+)%](.-)%[#%]")

  -- 如果标准格式没找到，尝试只有开始标签的格式：[#FFFFFF]文本
  if not hex_color or not content then
    hex_color, content = text:match("%[#([0-9A-Fa-f]+)%](.+)")
  end

  -- 如果还没找到，尝试是否是更简化的格式：#FFFFFF:文本
  if not hex_color or not content then
    hex_color, content = text:match("#([0-9A-Fa-f]+):(.+)")
  end

  -- 如果还没找到，检查是否只有结束标签 [#]
  if not hex_color or not content then
    local end_tag = text:match("%[#%]")
    if end_tag then
      -- 使用默认颜色和全部文本内容
      hex_color = "FBE4DE" -- 使用一个默认的浅粉色
      content = text:gsub("%[#%]", "") -- 移除结束标签
    end
  end

  if not hex_color or not content then
    return nil
  end

  -- 将十六进制颜色转换为RGB格式
  local r, g, b = 0.5, 0.5, 0.5 -- 默认值

  -- 尝试解析颜色值
  local function try_parse_hex()
    local success = true
    if #hex_color == 6 then
      -- 防止无效的十六进制值
      r = tonumber(hex_color:sub(1, 2), 16)
      g = tonumber(hex_color:sub(3, 4), 16)
      b = tonumber(hex_color:sub(5, 6), 16)

      if not r or not g or not b then
        success = false
      else
        r = r / 255
        g = g / 255
        b = b / 255
      end
    elseif #hex_color == 3 then
      -- 三位十六进制颜色
      r = tonumber(hex_color:sub(1, 1), 16)
      g = tonumber(hex_color:sub(2, 2), 16)
      b = tonumber(hex_color:sub(3, 3), 16)

      if not r or not g or not b then
        success = false
      else
        r = (r * 16 + r) / 255
        g = (g * 16 + g) / 255
        b = (b * 16 + b) / 255
      end
    else
      -- 长度不对
      success = false
    end

    return success
  end

  if not try_parse_hex() then
    -- 如果解析失败，使用默认颜色
    r, g, b = 0.5, 0.5, 0.5
  end

  -- 确保值在有效范围内
  r = math.max(0, math.min(1, r))
  g = math.max(0, math.min(1, g))
  b = math.max(0, math.min(1, b))

  -- 创建颜色表
  local color = {
    r = r,
    g = g,
    b = b,
    a = 1
  }

  return {text = content, color = color}
end

-- =====================================================
-- 播放控制功能（从playback_control.lua合并）
-- =====================================================

-- 格式化速率为字符串
function utils_module.format_playrate(rate)
  return string.format("%.1f", rate)
end

-- 播放/暂停功能
function utils_module.toggle_play()
  -- 直接调用REAPER内部命令ID 40328 (Transport: Play/stop)
  r.Main_OnCommand(40328, 0)

  return r.GetPlayState() == 1 -- 返回更新后的播放状态
end

-- 调整播放速率
function utils_module.adjust_playrate(delta)
  -- 获取当前播放速率
  local current_rate = r.Master_GetPlayRate()

  -- 调整播放速率
  local new_rate = current_rate + delta

  -- 限制播放速率范围在1.0到2.0之间
  if new_rate < 1.0 then new_rate = 1.0 end
  if new_rate > 2.0 then new_rate = 2.0 end

  -- 设置新的播放速率
  r.CSurf_OnPlayRateChange(new_rate)

  return new_rate
end

-- 重置播放速率为1.0
function utils_module.reset_playrate()
  -- 设置播放速率为1.0
  r.CSurf_OnPlayRateChange(1.0)

  return 1.0
end

-- 获取当前播放速率
function utils_module.get_playrate()
  return r.Master_GetPlayRate()
end

-- 检查并确保启用"改变播放速率时保持音频对象的音高"选项
function utils_module.ensure_preserve_pitch()
  -- 检查"改变播放速率时保持音频对象的音高"选项的状态(命令ID 40671)
  local state = r.GetToggleCommandStateEx(0, 40671)

  -- 如果未启用(状态不为1)，则启用它
  if state ~= 1 then
    r.Main_OnCommand(40671, 0)
    return true -- 表示进行了更改
  end

  return false -- 表示没有进行更改(已经启用)
end

-- =====================================================
-- 缓存管理功能（从cache_module.lua合并）
-- =====================================================

-- 缓存配置默认值
local cache_default_config = {
  max_size = 2000,            -- 最大缓存条目数
  cleanup_interval = 60,      -- 清理间隔（秒）
  cache_lifetime = 3600,      -- 缓存生命周期（1小时）
  enable_auto_cleanup = true  -- 是否启用自动清理
}

-- 内部变量
local cache_entries = {}        -- 缓存条目
local access_timestamps = {}    -- 访问时间戳
local last_cleanup_time = 0     -- 上次清理时间
local cache_config = {}         -- 实际使用的配置

-- 初始化缓存功能
-- @param cfg 可选的配置参数
function utils_module.init_cache(cfg)
  -- 合并默认配置和用户配置
  cache_config = {}
  for k, v in pairs(cache_default_config) do
    cache_config[k] = v
  end

  if cfg and type(cfg) == "table" then
    for k, v in pairs(cfg) do
      cache_config[k] = v
    end
  end

  -- 重置缓存状态
  cache_entries = {}
  access_timestamps = {}
  last_cleanup_time = r.time_precise()

  return true
end

-- 添加条目到缓存
-- @param key 缓存键
-- @param value 缓存值
-- @return boolean 操作是否成功
function utils_module.cache_add(key, value)
  if key == nil then return false end

  local current_time = r.time_precise()

  -- 如果缓存已满且键不存在，先清理
  if not cache_entries[key] and utils_module.cache_size() >= cache_config.max_size then
    utils_module.cache_cleanup(true)
  end

  -- 存储值和更新时间戳
  cache_entries[key] = {
    value = value,
    time = current_time
  }

  access_timestamps[key] = current_time

  -- 在添加新条目后，如果启用了自动清理，执行一次清理检查
  if cache_config.enable_auto_cleanup then
    utils_module.cache_cleanup()
  end

  return true
end

-- 从缓存中获取条目
-- @param key 缓存键
-- @return 缓存值，如果不存在则返回nil
function utils_module.cache_get(key)
  if key == nil or not cache_entries[key] then
    return nil
  end

  -- 更新访问时间戳
  access_timestamps[key] = r.time_precise()

  return cache_entries[key].value
end

-- 检查键是否存在
-- @param key 缓存键
-- @return boolean 键是否存在
function utils_module.cache_has(key)
  return key ~= nil and cache_entries[key] ~= nil
end

-- 从缓存中移除条目
-- @param key 缓存键
-- @return boolean 操作是否成功
function utils_module.cache_remove(key)
  if key == nil or not cache_entries[key] then
    return false
  end

  cache_entries[key] = nil
  access_timestamps[key] = nil

  return true
end

-- 获取缓存大小
-- @return number 缓存中的条目数量
function utils_module.cache_size()
  local count = 0
  for _ in pairs(cache_entries) do
    count = count + 1
  end
  return count
end

-- 清空缓存
-- @return number 清除的条目数量
function utils_module.cache_clear()
  local count = utils_module.cache_size()

  cache_entries = {}
  access_timestamps = {}

  return count
end

-- 清除匹配特定模式的缓存条目
-- @param pattern 要匹配的模式
-- @return number 清除的条目数量
function utils_module.cache_clear_pattern(pattern)
  if not pattern or type(pattern) ~= "string" then
    return 0
  end

  local delete_keys = {}

  -- 找出匹配模式的缓存项
  for key, _ in pairs(cache_entries) do
    if type(key) == "string" and key:match(pattern) then
      table.insert(delete_keys, key)
    end
  end

  -- 删除匹配的项
  for _, key in ipairs(delete_keys) do
    cache_entries[key] = nil
    access_timestamps[key] = nil
  end

  return #delete_keys -- 返回清理的条目数
end

-- 清理过期缓存
-- @param force 是否强制清理（忽略清理间隔）
-- @return number 清理的条目数量
function utils_module.cache_cleanup(force)
  local current_time = r.time_precise()

  -- 只在达到清理间隔时或强制清理时执行
  if not force and (current_time - last_cleanup_time) <= cache_config.cleanup_interval then
    return 0
  end

  local delete_keys = {}

  -- 找出超过保留时间的缓存项
  for key, entry in pairs(cache_entries) do
    if (current_time - entry.time) > cache_config.cache_lifetime then
      table.insert(delete_keys, key)
    end
  end

  -- 删除过期项
  for _, key in ipairs(delete_keys) do
    cache_entries[key] = nil
    access_timestamps[key] = nil
  end

  last_cleanup_time = current_time
  return #delete_keys -- 返回清理的条目数
end

-- 获取或计算值，带有自动缓存功能
-- @param key 缓存键
-- @param compute_func 计算函数，用于在缓存不命中时生成值
-- @param ... 传递给compute_func的参数
-- @return 缓存值或计算结果
function utils_module.cache_get_or_compute(key, compute_func, ...)
  if utils_module.cache_has(key) then
    return utils_module.cache_get(key)
  end

  -- 计算值
  local value = compute_func(...)

  -- 缓存结果
  utils_module.cache_add(key, value)

  return value
end

-- 返回工具模块
return utils_module