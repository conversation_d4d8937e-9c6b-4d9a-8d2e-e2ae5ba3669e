-- 测试完整UI显示的脚本
-- 这个脚本可以在REAPER中直接运行

-- 导入必要的库
local r = reaper

-- 获取脚本路径
local script_path = debug.getinfo(1, "S").source:match("^@(.+)$") or ""
local script_dir = script_path:match("(.+[\\/])") or "./"

-- 安全加载函数
local function safe_load_module(file_path)
  local success, result = pcall(dofile, file_path)
  if not success then
    r.ShowConsoleMsg("模块加载失败: " .. file_path .. " - " .. tostring(result) .. "\n")
    return {}
  end
  return result
end

-- 主函数
local function main()
  r.ShowConsoleMsg("=== 测试完整UI显示 ===\n")
  
  -- 加载所有模块
  local utils_module = safe_load_module(script_dir .. "utils_module.lua")
  local style_module = safe_load_module(script_dir .. "style_module.lua")
  local text_utils = safe_load_module(script_dir .. "text_utils.lua")
  local button_module = safe_load_module(script_dir .. "button_module.lua")
  local ui_module = safe_load_module(script_dir .. "ui_module.lua")
  local event_module = safe_load_module(script_dir .. "event_module.lua")
  
  if not utils_module or not style_module or not text_utils or not button_module or not ui_module or not event_module then
    r.ShowConsoleMsg("✗ 模块加载失败\n")
    return
  end
  r.ShowConsoleMsg("✓ 所有模块加载成功\n")
  
  -- 按正确顺序初始化模块
  style_module.init({utils_module = utils_module})
  
  local deps = {
    style_module = style_module,
    text_utils = text_utils,
    button_module = button_module,
    utils_module = utils_module,
    gfx = gfx,
    r = r
  }
  
  ui_module.init(deps)
  deps.ui_module = ui_module
  event_module.init(deps)
  
  if button_module and button_module.init then
    button_module.init({
      utils_module = utils_module,
      text_utils = text_utils,
      style_module = style_module
    })
  end
  
  r.ShowConsoleMsg("✓ 所有模块初始化成功\n")
  
  -- 创建应用状态
  local app_state = utils_module.app_state.create()
  r.ShowConsoleMsg("✓ 应用状态创建成功\n")
  
  -- 检查UI元素
  local ui_elements = style_module.init_ui_elements()
  r.ShowConsoleMsg("✓ UI元素创建成功\n")
  
  -- 检查所有按钮是否存在
  local expected_buttons = {
    "play_button", "block_mark_button", "region_mark_button", "excel_button",
    "rate_minus_button", "rate_display_area", "rate_plus_button", "rate_reset_button",
    "document_button", "clipboard_button", "font_decrease_button", "font_increase_button",
    "open_csv_button", "au_button", "region_name_button", "file_name_button", 
    "track_color_button", "track_split_button", "track_align_button", "chapter_button"
  }
  
  local missing_buttons = {}
  for _, button_name in ipairs(expected_buttons) do
    if not ui_elements[button_name] then
      table.insert(missing_buttons, button_name)
    end
  end
  
  if #missing_buttons > 0 then
    r.ShowConsoleMsg("✗ 缺失的按钮: " .. table.concat(missing_buttons, ", ") .. "\n")
  else
    r.ShowConsoleMsg("✓ 所有按钮都已定义\n")
  end
  
  -- 检查绘制函数是否存在
  local button_functions = {
    "draw_play_button", "draw_block_mark_button", "draw_region_mark_button", 
    "draw_excel_button", "draw_rate_buttons", "draw_font_buttons",
    "draw_document_buttons", "draw_open_au_buttons", "draw_track_align_button", "draw_chapter_button"
  }
  
  local missing_functions = {}
  for _, func_name in ipairs(button_functions) do
    if not button_module[func_name] then
      table.insert(missing_functions, func_name)
    end
  end
  
  if #missing_functions > 0 then
    r.ShowConsoleMsg("✗ 缺失的绘制函数: " .. table.concat(missing_functions, ", ") .. "\n")
  else
    r.ShowConsoleMsg("✓ 所有绘制函数都已定义\n")
  end
  
  -- 初始化窗口并渲染
  local window_success = ui_module.init_window()
  if window_success then
    r.ShowConsoleMsg("✓ 窗口初始化成功\n")
    
    -- 渲染UI
    local render_success, render_error = pcall(function()
      ui_module.render(app_state)
    end)
    
    if render_success then
      r.ShowConsoleMsg("✓ UI渲染成功\n")
      r.ShowConsoleMsg("\n🎉 完整UI测试成功！\n")
      r.ShowConsoleMsg("现在应该可以看到所有按钮：\n")
      r.ShowConsoleMsg("- 播放/暂停按钮\n")
      r.ShowConsoleMsg("- 速率控制按钮（-、显示、+、重置）\n")
      r.ShowConsoleMsg("- 块标、区标按钮\n")
      r.ShowConsoleMsg("- 写入报告按钮\n")
      r.ShowConsoleMsg("- 开、AU、区名、文名、轨色、分轨按钮\n")
      r.ShowConsoleMsg("- 读取文档、读取剪贴板按钮\n")
      r.ShowConsoleMsg("- 字体大小调整按钮\n")
      r.ShowConsoleMsg("- 对轨、章节按钮\n")
      
      -- 启动主循环来保持窗口显示
      local function loop()
        local char = gfx.getchar()
        if char == -1 then
          r.ShowConsoleMsg("窗口已关闭\n")
          return
        end
        
        -- 处理事件和渲染
        local event_success, event_error = pcall(function()
          event_module.handle_events(app_state)
          ui_module.render(app_state)
        end)
        
        if not event_success then
          r.ShowConsoleMsg("事件处理错误: " .. tostring(event_error) .. "\n")
        end
        
        r.defer(loop)
      end
      
      r.ShowConsoleMsg("启动主循环...\n")
      r.defer(loop)
      
    else
      r.ShowConsoleMsg("✗ UI渲染失败: " .. tostring(render_error) .. "\n")
    end
  else
    r.ShowConsoleMsg("! 窗口初始化跳过（可能是环境限制）\n")
  end
end

-- 运行测试
main()
