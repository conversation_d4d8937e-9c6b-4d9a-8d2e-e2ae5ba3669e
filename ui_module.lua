-- UI模块 - 负责所有界面渲染逻辑
-- 从mark.lua中提取的UI相关功能

local ui_module = {}

-- 依赖的模块（通过init函数注入）
local style_module, text_utils, button_module, utils_module, gfx, r

-- UI状态变量
local ui = {}
local window_w, window_h = 1200, 800
local font_size, content_font_size, font_name
local last_redraw_time = 0
local redraw_cooldown = 0.016  -- 约60FPS
local force_redraw = false
local partial_render = false
local full_redraw_counter = 0

-- 性能优化变量
local cached_sentence_heights = {}
local last_content_font_size = 0
local use_smooth_scroll = true
local cached_total_content_height = nil
local cached_text_wrapping = {}

-- 搜索高亮颜色
local search_highlight_color = {r = 1.0, g = 1.0, b = 0.0, a = 0.5}

-- 初始化UI模块
function ui_module.init(deps)
  -- 注入依赖
  style_module = deps.style_module
  text_utils = deps.text_utils
  button_module = deps.button_module
  utils_module = deps.utils_module
  gfx = deps.gfx
  r = deps.r

  -- 获取样式配置
  font_size = style_module.font_size
  content_font_size = style_module.content_font_size
  font_name = style_module.font_name

  return ui_module
end

-- 初始化gfx窗口
function ui_module.init_window()
  local status, err = pcall(function()
    -- 获取REAPER主窗口的位置，计算窗口居中位置
    local center_x, center_y = 0, 0
    local main_hwnd = r.GetMainHwnd()

    if main_hwnd and r.JS_Window_GetRect then
      local retval, left, top, right, bottom = r.JS_Window_GetRect(main_hwnd)
      if retval then
        local w = right - left
        local h = bottom - top
        center_x = left + math.floor((w - window_w) / 2)
        center_y = top + math.floor((h - window_h) / 2)
      end
    end

    -- 初始化窗口
    gfx.init("审听助手 v1.0 by 流浪物语", window_w, window_h, 0, center_x, center_y)
    gfx.dock(1026)  -- 停靠在附加泊坞窗右侧
    gfx.setfont(1, font_name, font_size)
    gfx.clear = 3355443  -- 深灰背景色

    -- 初始化UI元素
    ui = style_module.init_ui_elements()

    return true
  end)

  if not status then
    utils_module.error_handler:add("窗口初始化失败: " .. tostring(err), "error", "ui_module.init_window")
    return false
  end

  return true
end

-- 主渲染函数
function ui_module.render(app_state)
  local status, err = pcall(function()
    local current_time = os.clock()

    -- 检查是否需要完整重绘
    if app_state.force_redraw or (current_time - last_redraw_time) > redraw_cooldown then
      full_redraw_counter = full_redraw_counter + 1
      last_redraw_time = current_time
      app_state.force_redraw = false
      partial_render = false
    end

    -- 更新窗口大小
    if gfx.w ~= window_w or gfx.h ~= window_h then
      window_w, window_h = gfx.w, gfx.h
      ui = style_module.init_ui_elements(window_w, window_h)
      app_state.force_redraw = true
    end

    -- 绘制主界面
    ui_module.draw_main_ui(app_state)

    -- 更新显示
    gfx.update()
  end)

  if not status then
    utils_module.error_handler:add("渲染失败: " .. tostring(err), "error", "ui_module.render")
    r.ShowConsoleMsg("Render Error: " .. tostring(err) .. "\n")
  end
end

-- 绘制主界面（完整版本，支持动画）
function ui_module.draw_main_ui(app_state)
  -- 清除背景
  gfx.clear = style_module.colors.bg_int or 3355443

  -- 更新章节列表动画
  local animation_active = utils_module.update_chapter_animation(app_state, ui)
  if animation_active then
    app_state.force_redraw = true
  end

  -- 动态调整界面布局
  ui_module.update_layout_for_chapter_list(app_state)

  -- 绘制内容区域
  ui_module.draw_content_area(app_state)

  -- 绘制CV角色列表
  ui_module.draw_cv_role_list(app_state)

  -- 绘制选择内容区域
  ui_module.draw_selection_content(app_state)

  -- 绘制章节列表（如果可见或正在动画中）
  if app_state.is_chapter_list_visible or utils_module.chapter_animation.in_progress then
    -- 只有当章节列表在屏幕内时才绘制
    if ui.chapter_list and ui.chapter_list.x > utils_module.chapter_animation.hide_position then
      ui_module.draw_chapter_list(app_state)
    end
  end

  -- 绘制输入区域
  ui_module.draw_input_areas(app_state)

  -- 绘制按钮
  ui_module.draw_buttons(app_state)

  -- 绘制搜索UI
  ui_module.draw_search_ui(app_state)
end

-- 绘制内容区域（句子列表）
function ui_module.draw_content_area(app_state)
  if not ui.content_area then return end

  local content_area = ui.content_area

  -- 绘制背景（使用羊皮纸色背景，与原脚本一致）
  style_module.set_color(style_module.colors.content_bg)  -- 羊皮纸黄色背景
  gfx.rect(content_area.x, content_area.y, content_area.w, content_area.h)
  style_module.draw_black_outline(content_area.x, content_area.y, content_area.w, content_area.h)

  -- 绘制句子列表
  ui_module.draw_sentences_list(app_state, content_area)

  -- 绘制滚动条
  ui_module.draw_content_scrollbar(app_state, content_area)
end

-- 绘制句子列表
function ui_module.draw_sentences_list(app_state, content_area)
  local sentences = app_state.sentences
  if #sentences == 0 then
    -- 显示提示信息
    gfx.set(0.6, 0.6, 0.6, 1)
    gfx.x, gfx.y = content_area.x + 10, content_area.y + 10
    gfx.drawstr("请从剪贴板或文件读取文本内容...")
    return
  end

  -- 使用text_utils的高级文本渲染函数
  if text_utils and text_utils.draw_sentences_list then
    -- 准备搜索高亮参数
    local search_keyword = app_state.search_text ~= "" and app_state.search_text or nil
    local highlight_color = search_highlight_color

    -- 调用text_utils的高级渲染函数
    text_utils.draw_sentences_list(
      sentences,
      content_area,
      app_state.sentence_scroll_pos,
      app_state.hover_sentence_idx,
      app_state.selected_indices,
      content_font_size,
      font_name,
      app_state.cached_sentence_heights or {},
      app_state.cached_total_content_height,
      style_module,
      gfx,
      search_keyword,
      highlight_color
    )
  else
    -- 降级到简单渲染（如果text_utils不可用）
    ui_module.draw_sentences_list_simple(app_state, content_area)
  end
end

-- 简单的句子列表渲染（降级方案）
function ui_module.draw_sentences_list_simple(app_state, content_area)
  local sentences = app_state.sentences

  -- 计算可见区域
  local line_height = content_font_size + 5
  local visible_area_height = content_area.h - 10
  local visible_items = math.floor(visible_area_height / line_height)

  -- 计算滚动范围
  local max_scroll = math.max(0, #sentences - visible_items)
  app_state.sentence_scroll_pos = math.min(app_state.sentence_scroll_pos, max_scroll)

  -- 绘制可见的句子
  local y_offset = 5
  local start_idx = math.max(1, app_state.sentence_scroll_pos + 1)
  local end_idx = math.min(#sentences, start_idx + visible_items - 1)

  for i = start_idx, end_idx do
    local sentence = sentences[i]
    if sentence and sentence ~= "__SKIP_THIS_SENTENCE__" then
      local y_pos = content_area.y + y_offset

      -- 检查是否是搜索结果
      local is_search_result = false
      for _, result_idx in ipairs(app_state.search_results) do
        if result_idx == i then
          is_search_result = true
          break
        end
      end

      -- 绘制句子背景（悬停或搜索高亮）
      if app_state.hover_sentence_idx == i then
        gfx.set(0.3, 0.3, 0.4, 1)  -- 悬停背景
        gfx.rect(content_area.x + 2, y_pos - 2, content_area.w - 4, line_height)
      elseif is_search_result then
        gfx.set(search_highlight_color.r, search_highlight_color.g, search_highlight_color.b, search_highlight_color.a)
        gfx.rect(content_area.x + 2, y_pos - 2, content_area.w - 4, line_height)
      end

      -- 绘制句子文本（在羊皮纸背景上使用黑色文本）
      style_module.set_color(style_module.colors.content_text)  -- 黑色文本
      gfx.x, gfx.y = content_area.x + 5, y_pos

      -- 清理标签后显示
      local clean_sentence = ui_module.clean_text_tags(sentence)
      gfx.drawstr(clean_sentence)

      y_offset = y_offset + line_height
    end
  end
end

-- 绘制内容区域滚动条
function ui_module.draw_content_scrollbar(app_state, content_area)
  local sentences = app_state.sentences
  if #sentences == 0 then return end

  local line_height = content_font_size + 5
  local visible_items = math.floor((content_area.h - 10) / line_height)

  if #sentences > visible_items then
    local scrollbar = {
      x = content_area.x + content_area.w - 10,
      y = content_area.y,
      w = 10,
      h = content_area.h
    }

    local max_scroll = math.max(0, #sentences - visible_items)
    style_module.draw_scrollbar(scrollbar, app_state.sentence_scroll_pos, max_scroll, visible_items, #sentences)
  end
end

-- 清除文本标签的辅助函数
function ui_module.clean_text_tags(text)
  if not text then return "" end

  -- 清除各种标签
  local cleaned = text:gsub("%[#[0-9A-Fa-f]*%]", ""):gsub("%[#%]", "")
  cleaned = cleaned:gsub("%[bg#[0-9A-Fa-f]*%]", ""):gsub("%[bg#%]", "")
  cleaned = cleaned:gsub("%[x%]", ""):gsub("%[/x%]", "")
  cleaned = cleaned:gsub("%[CHAPTER%]", ""):gsub("%[/CHAPTER%]", "")

  return cleaned
end

-- 绘制CV角色列表
function ui_module.draw_cv_role_list(app_state)
  if not ui.cv_role_list then return end

  local cv_role_list = ui.cv_role_list

  -- 绘制背景
  gfx.set(0.15, 0.15, 0.15, 1)  -- 深灰色背景
  gfx.rect(cv_role_list.x, cv_role_list.y, cv_role_list.w, cv_role_list.h)
  style_module.draw_black_outline(cv_role_list.x, cv_role_list.y, cv_role_list.w, cv_role_list.h)

  -- 绘制标题和交换位置复选框
  gfx.set(1, 1, 1, 1)  -- 白色文本
  gfx.x, gfx.y = cv_role_list.x, cv_role_list.y - 20
  gfx.drawstr(style_module.cv_role_label_text or "CV角色列表")

  -- 绘制交换位置复选框
  if ui.cv_role_reverse_checkbox then
    style_module.draw_checkbox(ui.cv_role_reverse_checkbox, "交换位置", app_state.is_cv_role_reversed)
  end

  -- 绘制CV角色对（按CV分类）
  ui_module.draw_cv_role_pairs_categorized(app_state, cv_role_list)

  -- 绘制滚动条
  ui_module.draw_cv_role_scrollbar_categorized(app_state, cv_role_list)
end

-- 获取CV角色对的分类数据，参照原mark.lua的实现
function ui_module.get_cv_role_categories(pairs, is_reversed)
  local cv_categories = {}  -- 用于存储按CV分类的角色
  local cv_order = {}       -- 用于记录CV的顺序

  -- 遍历所有CV角色对，按CV进行分类
  -- 无论是否选择了交换位置复选框，都以相同的方式显示
  for i, pair in ipairs(pairs) do
    -- 始终保持角色在右边，CV在左边的显示方式，忽略is_reversed参数
    -- 这确保了CV角色列表的显示始终一致，不受交换位置复选框影响
    local cv_name = pair.cv
    local role_name = pair.role

    if not cv_categories[cv_name] then
      cv_categories[cv_name] = {}
      table.insert(cv_order, cv_name)
    end
    table.insert(cv_categories[cv_name], {role = role_name, index = i})
  end

  return cv_categories, cv_order
end

-- 绘制CV角色对（按CV分类，参照原mark.lua的实现）
function ui_module.draw_cv_role_pairs_categorized(app_state, cv_role_list)
  local cv_role_pairs = app_state.cv_role_pairs
  if #cv_role_pairs == 0 then
    gfx.set(0.6, 0.6, 0.6, 1)
    gfx.x, gfx.y = cv_role_list.x + 10, cv_role_list.y + 10
    gfx.drawstr("暂无CV角色数据")
    return
  end

  -- 使用基于字体大小的动态行高
  local cv_role_line_height = math.max(20, font_size + 2)

  -- 按CV分类整理角色列表
  local cv_categories, cv_order = ui_module.get_cv_role_categories(cv_role_pairs, app_state.is_cv_role_reversed)

  -- 计算所有分类项的总数（包括CV分类标题）
  local total_items = 0
  for _, roles in pairs(cv_categories) do
    -- 每个CV类别标题占一行，加上所有角色
    total_items = total_items + 1 + #roles
  end

  -- 计算可见项目数和最大滚动位置
  local visible_items = math.floor((cv_role_list.h - 10) / cv_role_line_height)
  local max_scroll = math.max(0, total_items - visible_items)
  app_state.cv_role_scroll_pos = math.min(app_state.cv_role_scroll_pos, max_scroll)

  -- 计算开始绘制的位置（考虑滚动位置）
  local visible_start = app_state.cv_role_scroll_pos + 1
  local current_item = 1
  local y_offset = 5

  -- 按CV分类绘制角色列表
  for _, cv_name in ipairs(cv_order) do
    local roles = cv_categories[cv_name]

    -- 绘制CV分类标题
    if current_item >= visible_start and (current_item - visible_start) < visible_items then
      -- 绘制CV分类标题背景
      gfx.set(0.25, 0.25, 0.25, 1)  -- 稍亮的背景
      gfx.rect(cv_role_list.x + 2, cv_role_list.y + y_offset - 2, cv_role_list.w - 4, cv_role_line_height)

      -- 绘制CV分类标题文字
      gfx.set(1, 1, 1, 1)  -- 白色文字
      gfx.x, gfx.y = cv_role_list.x + 5, cv_role_list.y + y_offset
      gfx.drawstr("CV: " .. cv_name)

      -- 更新Y偏移
      y_offset = y_offset + cv_role_line_height
    end
    current_item = current_item + 1

    -- 绘制该CV下的所有角色
    for _, role_info in ipairs(roles) do
      if current_item >= visible_start and (current_item - visible_start) < visible_items then
        -- 检查是否是当前选中的CV和角色或鼠标悬停的角色
        local display_selected_cv, display_selected_role

        if app_state.is_cv_role_reversed then
          -- 如果已交换，则需要反向处理以保持显示一致
          display_selected_cv = app_state.selected_role  -- 实际上是CV
          display_selected_role = app_state.selected_cv  -- 实际上是角色
        else
          -- 未交换时正常处理
          display_selected_cv = app_state.selected_cv
          display_selected_role = app_state.selected_role
        end

        local is_selected = (cv_name == display_selected_cv and role_info.role == display_selected_role)
        local is_hovered = (app_state.hover_cv_role.cv == cv_name and app_state.hover_cv_role.role == role_info.role)

        -- 绘制角色背景
        if is_selected then
          -- 使用金属效果的选中高亮（如果style_module支持）
          if style_module.draw_metallic_highlight then
            style_module.draw_metallic_highlight(
              cv_role_list.x + 2, cv_role_list.y + y_offset - 2,
              cv_role_list.w - 14, cv_role_line_height,
              {r = 0.2, g = 0.4, b = 0.6, a = 1}, true
            )
          else
            gfx.set(0.2, 0.4, 0.6, 1)  -- 选中背景（蓝色）
            gfx.rect(cv_role_list.x + 2, cv_role_list.y + y_offset - 2, cv_role_list.w - 4, cv_role_line_height)
          end
        elseif is_hovered then
          -- 使用金属效果的悬停高亮（如果style_module支持）
          if style_module.draw_metallic_highlight then
            style_module.draw_metallic_highlight(
              cv_role_list.x + 2, cv_role_list.y + y_offset - 2,
              cv_role_list.w - 14, cv_role_line_height,
              {r = 0.3, g = 0.6, b = 0.3, a = 1}, true
            )
          else
            gfx.set(0.3, 0.3, 0.4, 1)  -- 悬停背景（灰色）
            gfx.rect(cv_role_list.x + 2, cv_role_list.y + y_offset - 2, cv_role_list.w - 4, cv_role_line_height)
          end
        end

        -- 绘制角色文字
        gfx.set(1, 1, 1, 1)  -- 白色文字
        gfx.x, gfx.y = cv_role_list.x + 15, cv_role_list.y + y_offset  -- 缩进显示角色
        gfx.drawstr("  " .. role_info.role)

        -- 更新Y偏移
        y_offset = y_offset + cv_role_line_height
      end
      current_item = current_item + 1
    end
  end
end

-- 绘制CV角色列表滚动条（按分类计算）
function ui_module.draw_cv_role_scrollbar_categorized(app_state, cv_role_list)
  local cv_role_pairs = app_state.cv_role_pairs
  if #cv_role_pairs == 0 then return end

  -- 使用基于字体大小的动态行高
  local cv_role_line_height = math.max(20, font_size + 2)

  -- 按CV分类整理角色列表
  local cv_categories, cv_order = ui_module.get_cv_role_categories(cv_role_pairs, app_state.is_cv_role_reversed)

  -- 计算所有分类项的总数（包括CV分类标题）
  local total_items = 0
  for _, roles in pairs(cv_categories) do
    -- 每个CV类别标题占一行，加上所有角色
    total_items = total_items + 1 + #roles
  end

  -- 计算可见项目数
  local visible_items = math.floor((cv_role_list.h - 10) / cv_role_line_height)

  -- 绘制滚动条
  if total_items > visible_items then
    -- 创建滚动条对象
    local scrollbar = {
      x = cv_role_list.x + cv_role_list.w - 10,
      y = cv_role_list.y,
      w = 10,
      h = cv_role_list.h
    }

    -- 调用style_module的draw_scrollbar函数
    local max_scroll = math.max(0, total_items - visible_items)
    style_module.draw_scrollbar(scrollbar, app_state.cv_role_scroll_pos, max_scroll, visible_items, total_items)
  end
end

-- 绘制选择内容区域（完整版本，与原脚本一致）
function ui_module.draw_selection_content(app_state)
  if not ui.selection_area then return end

  local selection_area = ui.selection_area

  -- 绘制背景
  gfx.set(0.15, 0.15, 0.15, 1)  -- 深灰色背景
  gfx.rect(selection_area.x, selection_area.y, selection_area.w, selection_area.h)
  style_module.draw_black_outline(selection_area.x, selection_area.y, selection_area.w, selection_area.h)

  -- 绘制选择区域标题
  gfx.set(1, 1, 1, 1)  -- 白色
  gfx.x, gfx.y = selection_area.x, selection_area.y - 20
  gfx.drawstr("当前选择:")

  -- 准备显示内容
  local content_to_draw = {}
  local total_height = 0
  local max_width = selection_area.w - 20  -- 减少宽度，为滚动条留出空间

  -- 确保绘制前设置默认的白色文本
  gfx.set(1, 1, 1, 1)  -- 白色

  -- 收集所有要显示的内容并计算总高度
  if app_state.episode_number ~= "" then
    table.insert(content_to_draw, {type = "text", text = "集数: " .. app_state.episode_number, height = 20})
    total_height = total_height + 20
  end

  if app_state.selected_role ~= "" then
    local display_role = ui_module.clean_text_tags(app_state.selected_role)
    table.insert(content_to_draw, {type = "text", text = "角色: " .. display_role, height = 20})
    total_height = total_height + 20
  end

  if app_state.selected_cv ~= "" then
    local display_cv = ui_module.clean_text_tags(app_state.selected_cv)
    table.insert(content_to_draw, {type = "text", text = "CV: " .. display_cv, height = 20})
    total_height = total_height + 20
  end

  if app_state.error_note ~= "" then
    table.insert(content_to_draw, {type = "text", text = "错误描述: " .. app_state.error_note, height = 20})
    total_height = total_height + 20
  end

  if app_state.correct_note ~= "" then
    table.insert(content_to_draw, {type = "text", text = "正确表达: " .. app_state.correct_note, height = 20})
    total_height = total_height + 20
  end

  if app_state.process_suggestion ~= "" then
    table.insert(content_to_draw, {type = "text", text = "处理建议: " .. app_state.process_suggestion, height = 20})
    total_height = total_height + 20
  end

  if app_state.marked_relative_time ~= "" then
    table.insert(content_to_draw, {type = "text", text = "错误时间: " .. app_state.marked_relative_time, height = 20})
    total_height = total_height + 20
  end

  if app_state.selected_text ~= "" then
    -- 分离前缀和内容，单独处理
    local prefix = "文本: "
    -- 计算前缀宽度
    gfx.setfont(1, font_name, content_font_size)
    local prefix_width = gfx.measurestr(prefix)

    -- 直接从选择区域宽度计算可用宽度，确保考虑滚动条空间
    local content_max_width = selection_area.w - prefix_width - 30  -- 增加边距，确保文本不会超出背景框

    -- 根据是否需要滚动条进一步调整宽度
    if total_height > selection_area.h then
      content_max_width = content_max_width - 10  -- 额外减去滚动条宽度
    end

    -- 如果是多选模式，分别处理每个选中的句子
    if #app_state.selected_indices > 1 then
      local combined_height = 0
      local sentence_elements = {}

      -- 计算每个句子的高度并添加分隔符
      for i, idx in ipairs(app_state.selected_indices) do
        -- 清除句子中的标签
        local sentence_text = ui_module.clean_text_tags(app_state.sentences[idx])
        local sentence_height = text_utils.calculate_text_height and text_utils.calculate_text_height(sentence_text, content_max_width, gfx) or 20

        -- 添加句子内容
        table.insert(sentence_elements, {
          type = "complex",
          prefix = "",
          content = sentence_text,
          prefix_width = 0,
          content_max_width = content_max_width,
          height = sentence_height,
          indent = 10  -- 缩进显示
        })
        combined_height = combined_height + sentence_height

        -- 除最后一个句子外，添加分隔线
        if i < #app_state.selected_indices then
          table.insert(sentence_elements, {
            type = "separator",
            height = 10
          })
          combined_height = combined_height + 10
        end
      end

      -- 添加多选标题
      table.insert(content_to_draw, {
        type = "complex",
        prefix = prefix,
        content = string.format("多选模式 (%d个句子):", #app_state.selected_indices),
        prefix_width = prefix_width,
        content_max_width = content_max_width,
        height = 20
      })
      total_height = total_height + 20

      -- 添加所有句子元素
      for _, element in ipairs(sentence_elements) do
        table.insert(content_to_draw, element)
        total_height = total_height + element.height
      end
    else
      -- 单选模式，处理单个文本
      local clean_text = ui_module.clean_text_tags(app_state.selected_text)
      local text_height = text_utils.calculate_text_height and text_utils.calculate_text_height(clean_text, content_max_width, gfx) or 20

      table.insert(content_to_draw, {
        type = "complex",
        prefix = prefix,
        content = clean_text,
        prefix_width = prefix_width,
        content_max_width = content_max_width,
        height = text_height
      })
      total_height = total_height + text_height
    end
  end

  -- 绘制滚动条
  if total_height > selection_area.h then
    -- 创建滚动条对象
    local scrollbar = {
      x = selection_area.x + selection_area.w - 10,
      y = selection_area.y,
      w = 10,
      h = selection_area.h
    }

    -- 保存滚动条信息供鼠标处理使用
    ui.selection_scrollbar = {
      x = scrollbar.x,
      y = scrollbar.y,
      w = scrollbar.w,
      h = scrollbar.h,
      total_height = total_height
    }

    -- 确保滚动位置不超过有效范围
    local max_scroll = math.max(0, total_height - selection_area.h)
    app_state.selection_scroll_pos = math.min(app_state.selection_scroll_pos, max_scroll)

    -- 调用style_module中的绘制滚动条函数
    style_module.draw_scrollbar(scrollbar, app_state.selection_scroll_pos, max_scroll, selection_area.h, total_height)
  else
    ui.selection_scrollbar = nil  -- 无需滚动条时清空引用
    app_state.selection_scroll_pos = 0  -- 重置滚动位置
  end

  -- 绘制内容
  local y_offset = 5 - app_state.selection_scroll_pos  -- 应用滚动偏移
  for _, content in ipairs(content_to_draw) do
    if y_offset + content.height > 0 and y_offset < selection_area.h then  -- 只绘制可见区域内的内容
      if content.type == "text" then
        -- 使用白色文本（适配黑色背景）
        gfx.set(1, 1, 1, 1)
        -- 检查文本是否完全在可见区域内
        if y_offset >= 0 and y_offset + 20 <= selection_area.h then
          -- 应用缩进(如果有)
          local x_offset = content.is_header and 15 or 5
          gfx.x, gfx.y = selection_area.x + x_offset, selection_area.y + y_offset
          gfx.drawstr(content.text)
        elseif y_offset < 0 and y_offset + 20 > 0 then
          -- 文本部分可见（顶部被截断）
          local x_offset = content.is_header and 15 or 5
          gfx.x, gfx.y = selection_area.x + x_offset, selection_area.y
          gfx.drawstr(content.text)
        elseif y_offset < selection_area.h and y_offset + 20 > selection_area.h then
          -- 文本部分可见（底部被截断）
          local x_offset = content.is_header and 15 or 5
          gfx.x, gfx.y = selection_area.x + x_offset, selection_area.y + y_offset
          gfx.drawstr(content.text)
        end
      elseif content.type == "complex" then
        -- 处理复杂文本（带前缀的多行文本）
        ui_module.draw_complex_text_content(content, selection_area, y_offset)
      elseif content.type == "separator" then
        -- 绘制分隔线
        if y_offset >= 0 and y_offset < selection_area.h then
          gfx.set(0.5, 0.5, 0.5, 1)  -- 灰色分隔线
          gfx.rect(selection_area.x + 10, selection_area.y + y_offset + 4, selection_area.w - 30, 1)
        end
      end
    end
    y_offset = y_offset + content.height
  end
end

-- 绘制章节列表
function ui_module.draw_chapter_list(app_state)
  if not ui.chapter_list then return end

  local chapter_list = ui.chapter_list

  -- 绘制背景
  gfx.set(0.12, 0.12, 0.12, 1)  -- 深灰色背景
  gfx.rect(chapter_list.x, chapter_list.y, chapter_list.w, chapter_list.h)
  style_module.draw_black_outline(chapter_list.x, chapter_list.y, chapter_list.w, chapter_list.h)

  -- 绘制标题
  gfx.set(1, 1, 1, 1)  -- 白色文本
  gfx.x, gfx.y = chapter_list.x + 5, chapter_list.y + 5
  gfx.drawstr("章节列表")

  local chapters = app_state.chapters
  if #chapters == 0 then
    gfx.set(0.6, 0.6, 0.6, 1)
    gfx.x, gfx.y = chapter_list.x + 10, chapter_list.y + 30
    gfx.drawstr("暂无章节数据")
    return
  end

  -- 计算可见区域
  local line_height = font_size + 5
  local visible_area_height = chapter_list.h - 10
  local visible_items = math.floor(visible_area_height / line_height)

  -- 计算滚动范围
  local max_scroll = math.max(0, #chapters - visible_items)
  app_state.chapter_scroll_pos = math.min(app_state.chapter_scroll_pos, max_scroll)

  -- 绘制滚动条
  if #chapters > visible_items then
    local scrollbar = {
      x = chapter_list.x + chapter_list.w - 10,
      y = chapter_list.y,
      w = 10,
      h = chapter_list.h
    }
    style_module.draw_scrollbar(scrollbar, app_state.chapter_scroll_pos, max_scroll, visible_items, #chapters)
  end

  -- 绘制章节
  local y_offset = 30
  local start_idx = math.max(1, app_state.chapter_scroll_pos + 1)
  local end_idx = math.min(#chapters, start_idx + visible_items - 1)

  for i = start_idx, end_idx do
    local chapter = chapters[i]
    if chapter then
      local y_pos = chapter_list.y + y_offset

      -- 检查悬停状态
      if app_state.hover_chapter_idx == i then
        gfx.set(0.3, 0.3, 0.4, 1)  -- 悬停背景
        gfx.rect(chapter_list.x + 2, y_pos - 2, chapter_list.w - 4, line_height)
      end

      -- 绘制章节文本
      gfx.set(1, 1, 1, 1)  -- 白色文本
      gfx.x, gfx.y = chapter_list.x + 5, y_pos
      gfx.drawstr(chapter.title)

      y_offset = y_offset + line_height
    end
  end
end

-- 绘制输入区域
function ui_module.draw_input_areas(app_state)
  if not ui.error_input then return end

  -- 绘制各个输入区域
  button_module.draw_input_area(ui.error_input, app_state.error_note, "", "错误描述:")
  button_module.draw_input_area(ui.correct_input, app_state.correct_note, "", "正确表达:")
  button_module.draw_input_area(ui.episode_input, app_state.episode_number, "", "集数:")
  button_module.draw_suggestion_input(ui.suggestion_input, app_state.process_suggestion, "", "处理建议:")

  -- 绘制处理建议下拉菜单
  if app_state.show_suggestion_dropdown then
    ui_module.draw_suggestion_dropdown(app_state)
  end

  -- 绘制时间显示
  ui_module.draw_time_display(app_state)
end

-- 绘制按钮
function ui_module.draw_buttons(app_state)
  if not ui.block_mark_button then return end

  -- 检查按钮启用状态
  local has_selected_item = r.GetSelectedMediaItem(0, 0) ~= nil
  local role_cv_selected = app_state.selected_cv ~= "" and app_state.selected_role ~= ""
  local has_process_suggestion = app_state.process_suggestion ~= ""

  -- 块标按钮需要满足三个条件
  local block_mark_button_enabled = has_selected_item and role_cv_selected and has_process_suggestion
  -- 区标按钮需要满足两个条件
  local region_mark_button_enabled = role_cv_selected and has_process_suggestion

  -- 绘制打标按钮（使用原始样式）
  button_module.draw_block_mark_button(ui.block_mark_button, block_mark_button_enabled)
  button_module.draw_region_mark_button(ui.region_mark_button, region_mark_button_enabled)

  -- 绘制Excel按钮
  if ui.excel_button then
    local has_selected_text = #app_state.selected_texts > 0 or app_state.selected_text ~= ""
    button_module.draw_excel_button(ui.excel_button, has_selected_text)
  end

  -- 绘制开/AU/区名/文名/轨色/分轨按钮
  if ui.open_csv_button and ui.au_button then
    button_module.draw_open_au_buttons(
      ui.open_csv_button,
      ui.au_button,
      ui.region_name_button,
      ui.file_name_button,
      ui.track_color_button,
      ui.track_split_button
    )
  end

  -- 绘制播放按钮
  if ui.play_button then
    button_module.draw_play_button(ui.play_button, app_state.is_playing)
  end

  -- 绘制速率控制按钮
  if ui.rate_minus_button and ui.rate_plus_button and ui.rate_display_area then
    -- 确保current_playrate有默认值
    local current_playrate = app_state.current_playrate or 1.0
    button_module.draw_rate_buttons(ui.rate_minus_button, ui.rate_display_area, ui.rate_plus_button, ui.rate_reset_button, current_playrate)
  end

  -- 绘制字体大小按钮
  if ui.font_decrease_button and ui.font_increase_button then
    button_module.draw_font_buttons(ui.font_size_label, ui.font_decrease_button, ui.font_increase_button, ui.font_size_display, content_font_size)
  end

  -- 绘制文档和剪贴板按钮
  if ui.document_button and ui.clipboard_button then
    button_module.draw_document_buttons(ui.document_button, ui.clipboard_button)
  end

  -- 绘制对轨按钮
  if ui.track_align_button then
    button_module.draw_track_align_button(ui.track_align_button, app_state.is_track_align_enabled)
  end

  -- 绘制章节按钮
  if ui.chapter_button then
    button_module.draw_chapter_button(ui.chapter_button, app_state.is_chapter_list_visible)
  end

  -- 更新按钮状态（悬停和动画效果）
  ui_module.update_button_states(app_state)
end

-- 绘制搜索UI
function ui_module.draw_search_ui(app_state)
  if not ui.content_area or not ui.track_align_button then return end

  local content_area = ui.content_area

  -- 搜索框位置 - 放在轨按钮左边
  local search_box = {
    x = ui.track_align_button.x - 210,  -- 在轨按钮左边，留出10像素间距
    y = ui.track_align_button.y,       -- 与轨按钮同一水平线
    w = 200,
    h = 25
  }

  -- 更新UI元素中的搜索框位置
  ui.search_box = search_box

  -- 绘制搜索框背景
  if app_state.search_input_active then
    gfx.set(1.0, 1.0, 0.9, 1)  -- 激活时浅黄色背景
  else
    gfx.set(0.9, 0.9, 0.9, 1)  -- 普通时浅灰色背景
  end
  gfx.rect(search_box.x, search_box.y, search_box.w, search_box.h)

  -- 绘制搜索框边框
  if app_state.search_input_active then
    gfx.set(0.0, 0.5, 1.0, 1)  -- 激活时蓝色边框
  else
    gfx.set(0.5, 0.5, 0.5, 1)  -- 普通时深灰色边框
  end
  style_module.draw_black_outline(search_box.x, search_box.y, search_box.w, search_box.h)

  -- 绘制搜索文本
  gfx.set(0, 0, 0, 1)  -- 黑色文本
  gfx.x, gfx.y = search_box.x + 5, search_box.y + 5
  local display_text = app_state.search_text
  if display_text == "" then
    if app_state.search_input_active then
      display_text = "输入搜索关键词..."
      gfx.set(0.4, 0.4, 0.4, 1)  -- 激活时深灰色占位符
    else
      display_text = "搜索... (Ctrl+F)"
      gfx.set(0.6, 0.6, 0.6, 1)  -- 普通时浅灰色占位符
    end
  end
  gfx.drawstr(display_text)

  -- 绘制光标
  if app_state.search_input_active then
    local text_width = gfx.measurestr(display_text)
    gfx.set(0, 0, 0, 1)
    gfx.line(search_box.x + 5 + text_width, search_box.y + 3, search_box.x + 5 + text_width, search_box.y + search_box.h - 3)
  end

  -- 绘制搜索结果信息和导航按钮
  if #app_state.search_results > 0 then
    ui_module.draw_search_results_info(app_state, search_box)
  end

  -- 存储搜索框信息供事件处理使用
  ui.search_box = search_box
end

-- 绘制搜索结果信息和导航按钮
function ui_module.draw_search_results_info(app_state, search_box)
  -- 显示搜索结果数量
  gfx.set(1, 1, 1, 1)  -- 白色文本
  gfx.x, gfx.y = search_box.x + search_box.w + 10, search_box.y + 5
  local result_text = string.format("%d/%d", app_state.current_search_index, #app_state.search_results)
  gfx.drawstr(result_text)

  -- 绘制导航按钮
  local btn_size = 20
  local btn_y = search_box.y + 2

  -- 上一个按钮
  local prev_btn = {
    x = search_box.x + search_box.w + 50,
    y = btn_y,
    w = btn_size,
    h = btn_size
  }

  -- 下一个按钮
  local next_btn = {
    x = prev_btn.x + btn_size + 5,
    y = btn_y,
    w = btn_size,
    h = btn_size
  }

  -- 使用金属质感按钮绘制导航按钮
  if style_module and style_module.draw_enhanced_metal_button then
    style_module.draw_enhanced_metal_button(prev_btn, "◀", style_module.colors.button_rate)
    style_module.draw_enhanced_metal_button(next_btn, "▶", style_module.colors.button_rate)
  else
    -- 备用绘制方式
    gfx.set(0.8, 0.8, 0.8, 1)
    gfx.rect(prev_btn.x, prev_btn.y, prev_btn.w, prev_btn.h)
    gfx.rect(next_btn.x, next_btn.y, next_btn.w, next_btn.h)

    -- 绘制按钮边框
    gfx.set(0.5, 0.5, 0.5, 1)
    style_module.draw_black_outline(prev_btn.x, prev_btn.y, prev_btn.w, prev_btn.h)
    style_module.draw_black_outline(next_btn.x, next_btn.y, next_btn.w, next_btn.h)

    -- 绘制按钮文本
    gfx.set(0, 0, 0, 1)
    gfx.x, gfx.y = prev_btn.x + 6, prev_btn.y + 4
    gfx.drawstr("◀")
    gfx.x, gfx.y = next_btn.x + 6, next_btn.y + 4
    gfx.drawstr("▶")
  end

  -- 存储按钮信息供事件处理使用
  ui.search_prev_btn = prev_btn
  ui.search_next_btn = next_btn
end

-- 获取UI元素（供事件处理使用）
function ui_module.get_ui_elements()
  return ui
end

-- 更新按钮状态（悬停和动画效果）
function ui_module.update_button_states(app_state)
  local mouse_x, mouse_y = gfx.mouse_x, gfx.mouse_y
  local mouse_cap = gfx.mouse_cap

  -- 获取所有按钮
  local buttons = {
    ui.play_button,
    ui.block_mark_button,
    ui.region_mark_button,
    ui.excel_button,
    ui.open_csv_button,
    ui.au_button,
    ui.region_name_button,
    ui.file_name_button,
    ui.track_color_button,
    ui.track_split_button,
    ui.track_align_button,
    ui.chapter_button,
    ui.rate_minus_button,
    ui.rate_plus_button,
    ui.rate_reset_button,
    ui.document_button,
    ui.clipboard_button,
    ui.font_decrease_button,
    ui.font_increase_button
  }

  -- 更新按钮状态
  if style_module and style_module.update_button_states then
    local button_state_changed = style_module.update_button_states(buttons, mouse_x, mouse_y, mouse_cap)
    if style_module.update_button_animations then
      style_module.update_button_animations()
    end

    if button_state_changed then
      app_state.force_redraw = true
    end
  end
end

-- 强制重绘
function ui_module.force_redraw(app_state)
  if app_state then
    app_state.force_redraw = true
  end
end

-- 绘制处理建议下拉菜单
function ui_module.draw_suggestion_dropdown(app_state)
  if not ui.suggestion_input then return end

  local suggestion_input = ui.suggestion_input
  local dropdown_x = suggestion_input.x
  local dropdown_y = suggestion_input.y + suggestion_input.h
  local dropdown_w = suggestion_input.w
  local dropdown_h = #app_state.suggestion_options * 25

  -- 绘制下拉菜单背景
  gfx.set(0.9, 0.9, 0.9, 1)  -- 浅灰色背景
  gfx.rect(dropdown_x, dropdown_y, dropdown_w, dropdown_h)

  -- 绘制边框
  gfx.set(0.5, 0.5, 0.5, 1)  -- 深灰色边框
  style_module.draw_black_outline(dropdown_x, dropdown_y, dropdown_w, dropdown_h)

  -- 绘制选项
  for i, option in ipairs(app_state.suggestion_options) do
    local option_y = dropdown_y + (i - 1) * 25

    -- 检查是否悬停
    if app_state.hover_suggestion_idx == i then
      gfx.set(0.7, 0.7, 0.9, 1)  -- 悬停背景
      gfx.rect(dropdown_x + 1, option_y + 1, dropdown_w - 2, 23)
    end

    -- 绘制选项文本
    gfx.set(0, 0, 0, 1)  -- 黑色文本
    gfx.x, gfx.y = dropdown_x + 5, option_y + 5
    gfx.drawstr(option)
  end

  -- 存储下拉菜单位置供事件处理使用
  ui.suggestion_dropdown = {
    x = dropdown_x,
    y = dropdown_y,
    w = dropdown_w,
    h = dropdown_h
  }
end

-- 绘制时间显示
function ui_module.draw_time_display(app_state)
  if not ui.suggestion_input then return end

  -- 获取当前光标时间
  local cursor_pos = r.GetCursorPosition()
  local cursor_time_str = utils_module.format_time(cursor_pos)

  -- 获取选中音频块的内部时间
  local item_time_str = "无选中音频块"
  local selected_item = r.GetSelectedMediaItem(0, 0)
  if selected_item then
    local item_pos = r.GetMediaItemInfo_Value(selected_item, "D_POSITION")
    local relative_time = cursor_pos - item_pos
    if relative_time >= 0 then
      item_time_str = utils_module.format_time(relative_time)
    else
      item_time_str = "光标在音频块外"
    end
  end

  -- 在处理建议输入框下方显示时间信息
  local time_y = ui.suggestion_input.y + ui.suggestion_input.h + 5

  -- 绘制光标时间
  gfx.set(1, 1, 1, 1)  -- 白色文本
  gfx.x, gfx.y = ui.suggestion_input.x, time_y
  gfx.drawstr("光标时间: " .. cursor_time_str)

  -- 绘制音频块内部时间
  gfx.set(1, 1, 0, 1)  -- 黄色文本，使提示更加醒目
  gfx.x, gfx.y = ui.suggestion_input.x, time_y + 20
  gfx.drawstr("块内时间: " .. item_time_str)

  -- 更新应用状态中的时间信息
  app_state.cursor_time_str = cursor_time_str
  app_state.marked_relative_time = item_time_str
end

-- 滚动到指定句子
function ui_module.scroll_to_sentence(app_state, sentence_index)
  if not sentence_index or sentence_index < 1 or sentence_index > #app_state.sentences then
    return
  end

  -- 计算目标滚动位置
  local line_height = content_font_size + 5
  local visible_items_count = math.floor((ui.content_area.h - 10) / line_height)
  local target_scroll = math.max(0, sentence_index - math.floor(visible_items_count / 2))

  -- 确保不超过最大滚动范围
  local max_scroll = math.max(0, #app_state.sentences - visible_items_count)
  app_state.sentence_scroll_pos = math.min(target_scroll, max_scroll)
  app_state.force_redraw = true
end



-- 绘制复杂文本内容（带前缀的多行文本）
function ui_module.draw_complex_text_content(content, selection_area, y_offset)
  if not content or content.type ~= "complex" then return end

  -- 绘制前缀
  if content.prefix and content.prefix ~= "" then
    gfx.set(1, 1, 1, 1)  -- 白色文本
    gfx.x, gfx.y = selection_area.x + (content.indent or 5), selection_area.y + y_offset
    gfx.drawstr(content.prefix)
  end

  -- 绘制内容（支持多行）
  if content.content and content.content ~= "" then
    local content_x = selection_area.x + (content.prefix_width or 0) + (content.indent or 5)
    local content_y = selection_area.y + y_offset
    local max_width = content.content_max_width or (selection_area.w - 20)

    -- 使用text_utils绘制多行文本
    if text_utils and text_utils.draw_wrapped_text then
      text_utils.draw_wrapped_text(content.content, content_x, content_y, max_width, gfx)
    else
      -- 简单的单行绘制
      gfx.set(1, 1, 1, 1)
      gfx.x, gfx.y = content_x, content_y
      gfx.drawstr(content.content)
    end
  end
end

-- 滚动到指定的CV角色位置
function ui_module.scroll_to_cv_role(app_state, role, cv)
  if not role or not cv or not app_state.cv_role_pairs then return end

  -- 查找目标CV角色在列表中的位置
  local target_index = 0
  local current_index = 0

  -- 按CV分类计算位置
  local cv_categories = {}
  local cv_order = {}

  -- 分类CV角色对
  for _, pair in ipairs(app_state.cv_role_pairs) do
    local cv_name = pair.cv
    local role_name = pair.role

    if not cv_categories[cv_name] then
      cv_categories[cv_name] = {}
      table.insert(cv_order, cv_name)
    end

    table.insert(cv_categories[cv_name], {role = role_name, cv = cv_name})
  end

  -- 计算目标位置
  for _, cv_name in ipairs(cv_order) do
    current_index = current_index + 1  -- CV标题行

    local roles = cv_categories[cv_name]
    for _, role_info in ipairs(roles) do
      current_index = current_index + 1  -- 角色行

      if cv_name == cv and role_info.role == role then
        target_index = current_index
        break
      end
    end

    if target_index > 0 then break end
  end

  if target_index > 0 then
    -- 计算滚动位置
    local cv_role_line_height = math.max(20, content_font_size + 2)
    local visible_items = math.floor((ui.cv_role_list.h - 10) / cv_role_line_height)
    local total_items = current_index

    -- 将目标项目居中显示
    local target_scroll = math.max(0, target_index - math.floor(visible_items / 2))
    local max_scroll = math.max(0, total_items - visible_items)

    app_state.cv_role_scroll_pos = math.min(target_scroll, max_scroll)
    app_state.force_redraw = true
  end
end

-- 处理选择列表的多选功能
function ui_module.handle_multi_selection(app_state, sentence_idx, is_ctrl_down)
  if not sentence_idx or sentence_idx < 1 or sentence_idx > #app_state.sentences then
    return
  end

  -- 初始化选择数组
  app_state.selected_indices = app_state.selected_indices or {}
  app_state.selected_texts = app_state.selected_texts or {}

  if is_ctrl_down then
    -- 多选模式
    local found_idx = nil
    for i, idx in ipairs(app_state.selected_indices) do
      if idx == sentence_idx then
        found_idx = i
        break
      end
    end

    if found_idx then
      -- 取消选择
      table.remove(app_state.selected_indices, found_idx)
      table.remove(app_state.selected_texts, found_idx)
    else
      -- 添加选择
      table.insert(app_state.selected_indices, sentence_idx)
      table.insert(app_state.selected_texts, app_state.sentences[sentence_idx])
    end

    -- 更新选中文本显示
    if #app_state.selected_texts > 0 then
      app_state.selected_text = table.concat(app_state.selected_texts, "\n")
    else
      app_state.selected_text = ""
    end
  else
    -- 单选模式
    app_state.selected_indices = {sentence_idx}
    app_state.selected_texts = {app_state.sentences[sentence_idx]}
    app_state.selected_text = app_state.sentences[sentence_idx]
  end

  app_state.force_redraw = true
end

-- 获取选择列表的显示内容
function ui_module.get_selection_display_content(app_state)
  local content = {}

  -- 基本信息
  if app_state.episode_number ~= "" then
    table.insert(content, "集数: " .. app_state.episode_number)
  end

  if app_state.selected_role ~= "" then
    table.insert(content, "角色: " .. app_state.selected_role)
  end

  if app_state.selected_cv ~= "" then
    table.insert(content, "CV: " .. app_state.selected_cv)
  end

  if app_state.error_note ~= "" then
    table.insert(content, "错误描述: " .. app_state.error_note)
  end

  if app_state.correct_note ~= "" then
    table.insert(content, "正确表达: " .. app_state.correct_note)
  end

  if app_state.process_suggestion ~= "" then
    table.insert(content, "处理建议: " .. app_state.process_suggestion)
  end

  if app_state.marked_relative_time ~= "" then
    table.insert(content, "错误时间: " .. app_state.marked_relative_time)
  end

  -- 选中的文本
  if #app_state.selected_texts > 1 then
    table.insert(content, string.format("多选模式 (%d个句子):", #app_state.selected_texts))
    for i, text in ipairs(app_state.selected_texts) do
      local clean_text = ui_module.clean_text_tags(text)
      table.insert(content, string.format("  %d. %s", i, clean_text))
    end
  elseif app_state.selected_text ~= "" then
    table.insert(content, "选中文本:")
    local clean_text = ui_module.clean_text_tags(app_state.selected_text)
    table.insert(content, "  " .. clean_text)
  end

  return content
end

-- 动态调整界面布局以适应章节列表的显示/隐藏
function ui_module.update_layout_for_chapter_list(app_state)
  if not ui.content_area or not ui.chapter_list then return end

  local window_w = gfx.w
  local animation = utils_module.chapter_animation

  -- 如果动画正在进行，位置由动画系统控制
  if animation.in_progress then
    return
  end

  -- 非动画状态下根据章节列表是否可见调整内容区域位置和宽度
  if app_state.is_chapter_list_visible then
    -- 章节列表可见时，内容区域靠右
    ui.content_area.x = ui.chapter_list.x + ui.chapter_list.w + 10
    ui.content_area.w = window_w - 14 - ui.chapter_list.w - 10  -- 将右侧边距从20减少到14，增加显示宽度

    -- 确保章节列表在正确位置
    ui.chapter_list.x = animation.default_x
  else
    -- 章节列表不可见时，内容区域占据更多空间
    ui.content_area.x = 20  -- 左侧保留20像素边距
    ui.content_area.w = window_w - 24  -- 右侧边距从30减少到24，进一步增加显示宽度

    -- 确保章节列表在隐藏位置
    ui.chapter_list.x = animation.hide_position
  end

  -- 更新动画系统的当前位置
  animation.current_x = ui.chapter_list.x
end

-- 初始化章节列表位置
function ui_module.initialize_chapter_list_position(app_state)
  if not ui.chapter_list then return end

  local animation = utils_module.chapter_animation

  -- 根据初始状态设置章节列表位置
  if app_state.is_chapter_list_visible then
    ui.chapter_list.x = animation.default_x
  else
    ui.chapter_list.x = animation.hide_position
  end

  animation.current_x = ui.chapter_list.x

  -- 初始化内容区域布局
  ui_module.update_layout_for_chapter_list(app_state)
end

-- 检查章节列表是否应该绘制
function ui_module.should_draw_chapter_list(app_state)
  if not ui.chapter_list then return false end

  -- 如果章节列表可见或正在动画中，且位置在屏幕内，则绘制
  local animation = utils_module.chapter_animation
  return (app_state.is_chapter_list_visible or animation.in_progress) and
         ui.chapter_list.x > animation.hide_position
end

-- 获取章节列表的当前可见性状态
function ui_module.get_chapter_list_visibility(app_state)
  local animation = utils_module.chapter_animation

  if animation.in_progress then
    -- 动画进行中，根据目标状态判断
    return animation.target_x == animation.default_x
  else
    -- 非动画状态，直接返回当前状态
    return app_state.is_chapter_list_visible
  end
end

return ui_module